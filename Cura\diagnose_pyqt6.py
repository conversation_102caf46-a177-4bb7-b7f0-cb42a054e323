#!/usr/bin/env python3
"""
诊断 PyQt6 问题
"""

import sys
import os

def check_python_paths():
    print("Python 路径:")
    for i, path in enumerate(sys.path):
        if path:
            print(f"  {i}: {path}")
    print()

def check_pyqt6_location():
    print("查找 PyQt6 位置:")
    for path in sys.path:
        if path and os.path.exists(path):
            pyqt6_path = os.path.join(path, "PyQt6")
            if os.path.exists(pyqt6_path):
                print(f"  找到 PyQt6: {pyqt6_path}")
                
                # 检查 Qt6 目录
                qt6_path = os.path.join(pyqt6_path, "Qt6")
                if os.path.exists(qt6_path):
                    print(f"    Qt6 目录: {qt6_path}")
                    
                    # 检查 bin 目录
                    bin_path = os.path.join(qt6_path, "bin")
                    if os.path.exists(bin_path):
                        print(f"    bin 目录: {bin_path}")
                        
                        # 检查关键 DLL
                        core_dll = os.path.join(bin_path, "Qt6Core.dll")
                        if os.path.exists(core_dll):
                            print(f"    ✓ Qt6Core.dll 存在: {core_dll}")
                            print(f"      大小: {os.path.getsize(core_dll)} 字节")
                        else:
                            print(f"    ✗ Qt6Core.dll 不存在")
    print()

def check_environment():
    print("环境变量:")
    important_vars = ['PATH', 'PYTHONPATH', 'PYTHON_ROOT', 'VIRTUAL_ENV']
    for var in important_vars:
        value = os.environ.get(var, '')
        if value:
            print(f"  {var}:")
            if var == 'PATH':
                for path in value.split(';'):
                    if path.strip():
                        print(f"    {path}")
            else:
                print(f"    {value}")
        else:
            print(f"  {var}: 未设置")
    print()

def try_import_pyqt6():
    print("尝试导入 PyQt6:")
    try:
        print("  导入 PyQt6...")
        import PyQt6
        print(f"  ✓ PyQt6 导入成功: {PyQt6.__file__}")
        
        print("  导入 PyQt6.QtCore...")
        from PyQt6 import QtCore
        print(f"  ✓ QtCore 导入成功: {QtCore.__file__}")
        
        print("  获取版本信息...")
        from PyQt6.QtCore import QT_VERSION_STR, PYQT_VERSION_STR
        print(f"  ✓ Qt 版本: {QT_VERSION_STR}")
        print(f"  ✓ PyQt6 版本: {PYQT_VERSION_STR}")
        
        return True
    except ImportError as e:
        print(f"  ✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ✗ 运行时错误: {e}")
        return False

def main():
    print("=" * 60)
    print("PyQt6 诊断工具")
    print("=" * 60)
    
    print(f"Python 版本: {sys.version}")
    print(f"Python 可执行文件: {sys.executable}")
    print()
    
    check_python_paths()
    check_pyqt6_location()
    check_environment()
    
    success = try_import_pyqt6()
    
    print("=" * 60)
    if success:
        print("✓ PyQt6 工作正常!")
    else:
        print("✗ PyQt6 存在问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
