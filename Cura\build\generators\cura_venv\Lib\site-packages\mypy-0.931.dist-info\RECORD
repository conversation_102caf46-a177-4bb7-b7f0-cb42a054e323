../../Scripts/dmypy.exe,sha256=nNdz_UWXy8e3MNQBcsdEuFO0fF1rZlMwd75WDOAvXmg,108452
../../Scripts/mypy.exe,sha256=B-MkUcZsi3cae07Cq1tyZnootWa84cb-RiR24TR48Ks,108448
../../Scripts/mypyc.exe,sha256=1IMuC7af0KxRLEwnGbgQK0NpKBe9xg7OoFVmZ_2U0Q8,108431
../../Scripts/stubgen.exe,sha256=xhR6FknLH2cimAX_w_7FshRZpL_OUF7Zh0zAw9Fxah4,108429
../../Scripts/stubtest.exe,sha256=KeDRYCBhs5PkfrUBvWzfKvVMdAjOjVdBnTaG_KUA8NM,108430
mypy-0.931.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mypy-0.931.dist-info/LICENSE,sha256=7HlGuxkDbiI9uooZIb2yWetNOl3KB2XZlarGp1bxEvA,11333
mypy-0.931.dist-info/METADATA,sha256=oV341hg9YQ-BSvVQdwVMI-PtCWjUBc_NX4r80rKfh0g,1741
mypy-0.931.dist-info/RECORD,,
mypy-0.931.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy-0.931.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
mypy-0.931.dist-info/entry_points.txt,sha256=AjOHSeBnNSuIY_fD2NqaCo30-kPzDBCyJTdvRFI8e7E,180
mypy-0.931.dist-info/top_level.txt,sha256=gdz5XLe8-vYL_KT_2ka2B7fsxk4bZs6swFIZsuuXEGk,11
mypy/__init__.py,sha256=4yp43qNAZZ0ViBpVn56Bc7MA4H2UMXe0WTVPdkODP6k,37
mypy/__main__.py,sha256=B3TQ40v_IpqwIKUkrvyyT3Y2v2hkaprN4QXUr9JwF5I,1052
mypy/__pycache__/__init__.cpython-312.pyc,,
mypy/__pycache__/__main__.cpython-312.pyc,,
mypy/__pycache__/api.cpython-312.pyc,,
mypy/__pycache__/applytype.cpython-312.pyc,,
mypy/__pycache__/argmap.cpython-312.pyc,,
mypy/__pycache__/backports.cpython-312.pyc,,
mypy/__pycache__/binder.cpython-312.pyc,,
mypy/__pycache__/bogus_type.cpython-312.pyc,,
mypy/__pycache__/build.cpython-312.pyc,,
mypy/__pycache__/checker.cpython-312.pyc,,
mypy/__pycache__/checkexpr.cpython-312.pyc,,
mypy/__pycache__/checkmember.cpython-312.pyc,,
mypy/__pycache__/checkstrformat.cpython-312.pyc,,
mypy/__pycache__/config_parser.cpython-312.pyc,,
mypy/__pycache__/constraints.cpython-312.pyc,,
mypy/__pycache__/defaults.cpython-312.pyc,,
mypy/__pycache__/dmypy_os.cpython-312.pyc,,
mypy/__pycache__/dmypy_server.cpython-312.pyc,,
mypy/__pycache__/dmypy_util.cpython-312.pyc,,
mypy/__pycache__/erasetype.cpython-312.pyc,,
mypy/__pycache__/errorcodes.cpython-312.pyc,,
mypy/__pycache__/errors.cpython-312.pyc,,
mypy/__pycache__/expandtype.cpython-312.pyc,,
mypy/__pycache__/exprtotype.cpython-312.pyc,,
mypy/__pycache__/fastparse.cpython-312.pyc,,
mypy/__pycache__/fastparse2.cpython-312.pyc,,
mypy/__pycache__/find_sources.cpython-312.pyc,,
mypy/__pycache__/fixup.cpython-312.pyc,,
mypy/__pycache__/freetree.cpython-312.pyc,,
mypy/__pycache__/fscache.cpython-312.pyc,,
mypy/__pycache__/fswatcher.cpython-312.pyc,,
mypy/__pycache__/gclogger.cpython-312.pyc,,
mypy/__pycache__/git.cpython-312.pyc,,
mypy/__pycache__/indirection.cpython-312.pyc,,
mypy/__pycache__/infer.cpython-312.pyc,,
mypy/__pycache__/ipc.cpython-312.pyc,,
mypy/__pycache__/join.cpython-312.pyc,,
mypy/__pycache__/literals.cpython-312.pyc,,
mypy/__pycache__/lookup.cpython-312.pyc,,
mypy/__pycache__/main.cpython-312.pyc,,
mypy/__pycache__/maptype.cpython-312.pyc,,
mypy/__pycache__/meet.cpython-312.pyc,,
mypy/__pycache__/memprofile.cpython-312.pyc,,
mypy/__pycache__/message_registry.cpython-312.pyc,,
mypy/__pycache__/messages.cpython-312.pyc,,
mypy/__pycache__/metastore.cpython-312.pyc,,
mypy/__pycache__/mixedtraverser.cpython-312.pyc,,
mypy/__pycache__/modulefinder.cpython-312.pyc,,
mypy/__pycache__/moduleinspect.cpython-312.pyc,,
mypy/__pycache__/mro.cpython-312.pyc,,
mypy/__pycache__/nodes.cpython-312.pyc,,
mypy/__pycache__/operators.cpython-312.pyc,,
mypy/__pycache__/options.cpython-312.pyc,,
mypy/__pycache__/parse.cpython-312.pyc,,
mypy/__pycache__/plugin.cpython-312.pyc,,
mypy/__pycache__/pyinfo.cpython-312.pyc,,
mypy/__pycache__/reachability.cpython-312.pyc,,
mypy/__pycache__/renaming.cpython-312.pyc,,
mypy/__pycache__/report.cpython-312.pyc,,
mypy/__pycache__/sametypes.cpython-312.pyc,,
mypy/__pycache__/scope.cpython-312.pyc,,
mypy/__pycache__/semanal.cpython-312.pyc,,
mypy/__pycache__/semanal_classprop.cpython-312.pyc,,
mypy/__pycache__/semanal_enum.cpython-312.pyc,,
mypy/__pycache__/semanal_infer.cpython-312.pyc,,
mypy/__pycache__/semanal_main.cpython-312.pyc,,
mypy/__pycache__/semanal_namedtuple.cpython-312.pyc,,
mypy/__pycache__/semanal_newtype.cpython-312.pyc,,
mypy/__pycache__/semanal_pass1.cpython-312.pyc,,
mypy/__pycache__/semanal_shared.cpython-312.pyc,,
mypy/__pycache__/semanal_typeargs.cpython-312.pyc,,
mypy/__pycache__/semanal_typeddict.cpython-312.pyc,,
mypy/__pycache__/sharedparse.cpython-312.pyc,,
mypy/__pycache__/solve.cpython-312.pyc,,
mypy/__pycache__/split_namespace.cpython-312.pyc,,
mypy/__pycache__/state.cpython-312.pyc,,
mypy/__pycache__/stats.cpython-312.pyc,,
mypy/__pycache__/strconv.cpython-312.pyc,,
mypy/__pycache__/stubdoc.cpython-312.pyc,,
mypy/__pycache__/stubgen.cpython-312.pyc,,
mypy/__pycache__/stubgenc.cpython-312.pyc,,
mypy/__pycache__/stubinfo.cpython-312.pyc,,
mypy/__pycache__/stubtest.cpython-312.pyc,,
mypy/__pycache__/stubutil.cpython-312.pyc,,
mypy/__pycache__/subtypes.cpython-312.pyc,,
mypy/__pycache__/suggestions.cpython-312.pyc,,
mypy/__pycache__/traverser.cpython-312.pyc,,
mypy/__pycache__/treetransform.cpython-312.pyc,,
mypy/__pycache__/tvar_scope.cpython-312.pyc,,
mypy/__pycache__/type_visitor.cpython-312.pyc,,
mypy/__pycache__/typeanal.cpython-312.pyc,,
mypy/__pycache__/typeops.cpython-312.pyc,,
mypy/__pycache__/types.cpython-312.pyc,,
mypy/__pycache__/typestate.cpython-312.pyc,,
mypy/__pycache__/typetraverser.cpython-312.pyc,,
mypy/__pycache__/typevars.cpython-312.pyc,,
mypy/__pycache__/util.cpython-312.pyc,,
mypy/__pycache__/version.cpython-312.pyc,,
mypy/__pycache__/visitor.cpython-312.pyc,,
mypy/api.py,sha256=7DOJxy4DHNkriBn5uo1pFJOf57KLqA-wh9iiklPW7Yg,2888
mypy/applytype.py,sha256=FCI3dXcr5Pf9S1vhlIQ5QvHMvl9iSjuqBDlEbQMmuME,4118
mypy/argmap.py,sha256=0wYcxwA2vjpVUsjZmw5Cx7B21jC_BMVHXkflIgZCU0Q,10679
mypy/backports.py,sha256=IGEzJEgrnvkHaPtQhyO5FoJcjhK0_k9mgTyNlaFPOQs,502
mypy/binder.py,sha256=MYs-vXumAADZ01F7gDTH40s0wjkEywxlpvedYgZUjbk,17775
mypy/bogus_type.py,sha256=bTk0-CsLSm_tQZzE3EUM3nZs_z7kt077ZBVPwL0Y-TQ,779
mypy/build.py,sha256=Mzm0uVLy_taD5YtcwNZrDxQvcU0lTszr5RqZ5nmghYY,143237
mypy/checker.py,sha256=Hzd8AWXMmo0BIYrg3L38LTEkDuuGaU6kvz7mPcnXAJU,301395
mypy/checkexpr.py,sha256=a2yAl41KnIbMuItmmdMTI7ejqUPjNa3NqpqgO5Qvun8,223359
mypy/checkmember.py,sha256=yaKeScZ44S6ZhxQx7qVRrtMBE2CAv9mgSbmmE9ptCk4,47198
mypy/checkstrformat.py,sha256=6mKyxNtHrECCtsCLYXfgFnUW9_HontW-qvIC-LkBPOU,47505
mypy/config_parser.py,sha256=u3g7a27w220OiZmqbdL7Jd_BHWIlP2gi0EXHcFAmA-k,21129
mypy/constraints.py,sha256=m9p6RUKU_gC80F5D1iVGZkwtega-ndR4pYWoFrKccbc,32357
mypy/defaults.py,sha256=isL_5WA2Sa77sJap4ekFkc2i8e0eZd1pX4MVjYnXMd8,1165
mypy/dmypy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/dmypy/__main__.py,sha256=OPHdXfI4IT9tCNO_ZjoJsAfVck6EiOou_t9RX2AV8mI,92
mypy/dmypy/__pycache__/__init__.cpython-312.pyc,,
mypy/dmypy/__pycache__/__main__.cpython-312.pyc,,
mypy/dmypy/__pycache__/client.cpython-312.pyc,,
mypy/dmypy/client.py,sha256=V-OHhf2LDZPieVuCDC3Du-S-SxuPxvzy0-wNK87XPKA,21026
mypy/dmypy_os.py,sha256=BLiD7tJ3pCM71DuSBfIBESdy2X9nkyUhPf94uQCCCio,1192
mypy/dmypy_server.py,sha256=QZ1AQZVvmocfjcT3tMqUUdEQwyBNHAiwhpE_zaudnOU,40511
mypy/dmypy_util.py,sha256=mG5souz_gT-XgB-Wdn3fONdrdCfqcxu2S4wXDlVVGB0,847
mypy/erasetype.py,sha256=NUv-8_sVI1Dp2qCCWkheie_ydR4mAw_CXHJzRof1ZVI,5796
mypy/errorcodes.py,sha256=J_l8odOtcj_xz2F7RbLWTgJlfAfU6kM2Wmwc917KgxY,5485
mypy/errors.py,sha256=Kv1MndSyHB_OtKgYztp2MtO4I_Qq80wyMetGVJTho6I,34724
mypy/expandtype.py,sha256=Vmhusyf030tLUWFMn1LugGa5E2A9BU3QecFpepKDTGQ,7437
mypy/exprtotype.py,sha256=Jjeq29fUIOY8QXYfqxvi9jTr8ad467_M6HQdsASq85E,7548
mypy/fastparse.py,sha256=e8ohIK-mM58ywCyO_kEql8T_Rmkl4hkyY5OUs_EkvE0,68957
mypy/fastparse2.py,sha256=QNFurLsL0zim9H6YdBm8HPGFJyyXAYKLUIXoq0tykXA,44306
mypy/find_sources.py,sha256=WnKuSjeJ2CRSqC0wpShPZ1xBvKnIjc68lBfjngGavUs,9497
mypy/fixup.py,sha256=he2HuejInbMh31VFt3A8RrPeIbe8drWc0fDGuTce6ig,12757
mypy/freetree.py,sha256=djtzkC5Ly0pJU3jaHKoXvIAcUyCIvoqBZ4vFEMXVEdA,581
mypy/fscache.py,sha256=RkEPy7o0c6QuaP4ud7y5WQZZktXVzztcsbZ2C1OmR-8,11487
mypy/fswatcher.py,sha256=RZ_raeOflMURhnqy9jlip6Yqd6kn-eSkO6aBW_djMco,4121
mypy/gclogger.py,sha256=9SbqYymLQGubmUMxImFuarZFGCpKHr0d5mZLKG7SMk8,1624
mypy/git.py,sha256=390obtxNgd-JfamQspDkv-I4X6ADyBhQfODZkzf2YBo,1163
mypy/indirection.py,sha256=U5kV_3rHbtsoLgTGDFc6LOddz3J0MOM0kPlpniAQdHY,4175
mypy/infer.py,sha256=6XadQfpM97ElhaM18W4ooL7C476wp0Fu4wnVUcHweHY,2472
mypy/ipc.py,sha256=b7jgYDsQLNNPkNwqVNOKbMWsW_FB1Qfm3F8olEJ2xlw,10581
mypy/join.py,sha256=HxfhU6k6VJ9UphkvQsTGfkp2XfjQ5DDxxN7EROlaRAY,25602
mypy/literals.py,sha256=XZHMxjlb8PrlzNECzbKUfvh69JFrvQN8CT_YZBQsNDY,7983
mypy/lookup.py,sha256=VAi360xwtem5VtEx2B5TrUcFMyKu66nT9sM4rySs59s,2093
mypy/main.py,sha256=Fu2W-KRBXuaVDoFTvmMbJcMGsBkhWcuQNA43GdO4kFc,53303
mypy/maptype.py,sha256=pIwX03kt5Y6AG9dF9SZ6dzKsxqme9LCBhezz4pR1wTY,4027
mypy/meet.py,sha256=NLXdgG0ttqi4V1fAt1OU8JFoMqu8ix8i7sFY6yDrqic,34946
mypy/memprofile.py,sha256=VpGjyL_YdQ_s-_rFL4IbgdHjhSalveXugHSGIBjSM6s,4168
mypy/message_registry.py,sha256=KvXGqPclKw7DO4Cojv3BwnjPMs2XgeoYWRpV_VGtrao,11084
mypy/messages.py,sha256=W5WBr5YzVxAAca8JAUyBrnemIq-KAn32v1QPjxuCbNI,103045
mypy/metastore.py,sha256=TNnpB6_lF_v3TT5IeJmLLbUkaeaERgJhPslS6acudM8,6656
mypy/mixedtraverser.py,sha256=gVUgwRxQ8EX91kkvSwxPZYN7PF_khVm_WoXh8BUh4d8,2841
mypy/modulefinder.py,sha256=NaMULiaMlnhQVobd4Aqc5rh2eKEP3WhMOoPGETSegGY,38869
mypy/moduleinspect.py,sha256=PoFL4OmXKwHhh9iwdLklJiZQDapR_YT0hZo_NSEpUSc,6198
mypy/mro.py,sha256=P7p9Mo7-tkkx7Jyd0FFO-j8BnOM-vKD51EI8_Ut5-2g,2090
mypy/nodes.py,sha256=elYN8pouwSiCfejZeTw2OY3KVYHBVKwonNINWEGd7OQ,117991
mypy/operators.py,sha256=TpU-z6e6Z2cl3LSbwdSyhegfLlsvCv0dXl9D17l89Jk,2540
mypy/options.py,sha256=Il6TiFeY-6BXQE1GzIMOqgQtfIsJGVUUWcRFq6b3bRw,17966
mypy/parse.py,sha256=azOUCE0K3wtErfz33eyKgIxOUN67BEL21Z0fgex-0gc,1363
mypy/plugin.py,sha256=VIjhs6lJgQ-EQfJaZbb3R6WqknKUlqgHWTVYWZaxgkY,34453
mypy/plugins/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/plugins/__pycache__/__init__.cpython-312.pyc,,
mypy/plugins/__pycache__/attrs.cpython-312.pyc,,
mypy/plugins/__pycache__/common.cpython-312.pyc,,
mypy/plugins/__pycache__/ctypes.cpython-312.pyc,,
mypy/plugins/__pycache__/dataclasses.cpython-312.pyc,,
mypy/plugins/__pycache__/default.cpython-312.pyc,,
mypy/plugins/__pycache__/enums.cpython-312.pyc,,
mypy/plugins/__pycache__/functools.cpython-312.pyc,,
mypy/plugins/__pycache__/singledispatch.cpython-312.pyc,,
mypy/plugins/attrs.py,sha256=0yuBo1U_Ux2JpkLQ9UJ7mwzja1ufSO9sDkeQCfexiBQ,32472
mypy/plugins/common.py,sha256=nGJMZEObpHTl0NzMajAV4LxYN6CB9tuKy5nerHD2MJU,5795
mypy/plugins/ctypes.py,sha256=JUtlxLTKFzuoCpnwqdIlLvYPXJ7kP9G-_98if7-Wo_U,10623
mypy/plugins/dataclasses.py,sha256=Cwj8a_OVDF_xLlb8elXl3ImVlJ1lxSnMqoQDzRkLaq4,21204
mypy/plugins/default.py,sha256=goyEGC1IRYiOtNxr8nKcIOFKhCWSVEvm98rQb9PXzfI,20848
mypy/plugins/enums.py,sha256=b6FZsc8H_8DzEDUYd2ip_FJGbTo1eAiDZGiXLj0tsn8,10401
mypy/plugins/functools.py,sha256=ker9B7qvJg7ElUWQS_vkmZd3_fTGMuUWtKmRUFxhkW8,4175
mypy/plugins/singledispatch.py,sha256=eskx0FCNPhZ9h-npa1WvFBdVESboohs87yN4Qj5rf4c,8706
mypy/py.typed,sha256=zlBhTdAQBRfJxeJXD-QnlXiZYsiAJYYkr3mbEsmwSac,64
mypy/pyinfo.py,sha256=PKk1ITuBTgXccfOLUl-jpisCfqTkOWU73kBvNktykiE,1384
mypy/reachability.py,sha256=6YQPjTHGiAfpK77_fIbt5C6totC35nO-VpITHXXM7u8,11352
mypy/renaming.py,sha256=MRVrrxOXUcIC-5pgMs07lYRFgoXpir8i_7tVOgmU1mY,13709
mypy/report.py,sha256=uzKIjO8axjUpBl1b0o546a9ksyqFY8K3Rh-byO7Q77Q,35469
mypy/sametypes.py,sha256=xxDtIsnJxAF1R4iQ2Yy5SANz_iudqkzc5fp9fSHbRUk,6881
mypy/scope.py,sha256=yXRecLYND0bIYryM3OZh0l2E67dxO-4GOtNsU881dAA,4073
mypy/semanal.py,sha256=O_j3eFaz0gay5ZB7fC9iQmkygfhVuzq66H1nYWYd4I0,247593
mypy/semanal_classprop.py,sha256=wPUEZGPhIrFMRb3NbEEL_A_5cP5PTXqAUw5LXM4oDbg,7209
mypy/semanal_enum.py,sha256=M0HR5UMa2itPvx3HIi1uG5dlp8a-53RWMCWMgSfIO9A,8512
mypy/semanal_infer.py,sha256=l7oXihYSZcYk4RkNnZA5lCMN4VulPopTlzp3n1os7Lw,5174
mypy/semanal_main.py,sha256=V-ItK7R5yBgGzYaPC3ZPPgA3FjYgcacZdb1gDLcmuOo,17810
mypy/semanal_namedtuple.py,sha256=q5fM3jG8Q7LkWR3YRQzcPKKLfAJEezPjXjNUgwRkvuQ,26240
mypy/semanal_newtype.py,sha256=KsqKDMrNFAnXUsCh0x38LSXOsXH4M_JkFj3NdCfN-Xw,9538
mypy/semanal_pass1.py,sha256=gq9cgTlk-R07CLaMCiG671uc6vjLeSlhYhx1NFTymQU,4434
mypy/semanal_shared.py,sha256=wvtoM2qzG5yZxDLQKFMJS3Ivpa3BnS1bRtNCe_L_KyI,7985
mypy/semanal_typeargs.py,sha256=-Iga2K_l0UfpQe45IpMxiTG97oTu3FxTJWsgIf1twMg,5425
mypy/semanal_typeddict.py,sha256=9qdzZfHHuYJUw-2Hg3wE787yaS6whT0curQd5R4fQ8c,18046
mypy/server/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/server/__pycache__/__init__.cpython-312.pyc,,
mypy/server/__pycache__/astdiff.cpython-312.pyc,,
mypy/server/__pycache__/astmerge.cpython-312.pyc,,
mypy/server/__pycache__/aststrip.cpython-312.pyc,,
mypy/server/__pycache__/deps.cpython-312.pyc,,
mypy/server/__pycache__/mergecheck.cpython-312.pyc,,
mypy/server/__pycache__/objgraph.cpython-312.pyc,,
mypy/server/__pycache__/subexpr.cpython-312.pyc,,
mypy/server/__pycache__/target.cpython-312.pyc,,
mypy/server/__pycache__/trigger.cpython-312.pyc,,
mypy/server/__pycache__/update.cpython-312.pyc,,
mypy/server/astdiff.py,sha256=jU1WNH1RAH_tX0DKU4JD2G4sYgxTcTMlidtxgN62SnU,16462
mypy/server/astmerge.py,sha256=1h4qvsLwPzsyXAm80GwMU5kf08BFpVKmENBq33mUKio,18792
mypy/server/aststrip.py,sha256=2s3HgLxN-452U-FOg-GAmYZ-VHwCxEeS1T8MFXi15h0,10903
mypy/server/deps.py,sha256=9F1RPI6K_QrJCCPii4xtNcti8MSnNLbkqlhNXY8dCHM,48904
mypy/server/mergecheck.py,sha256=inNQE69ov2eLeoTmE429ePS365yf9e-sGiNvstb7GTA,2792
mypy/server/objgraph.py,sha256=Z3Bh5PrB383RN2ltKb5GmMhyngbRt31zqphEw8mxUlk,3451
mypy/server/subexpr.py,sha256=BlcEVxCwKhspl9mhmUJGg9tpJnkT5SOZ4ANqqwPif_s,5093
mypy/server/target.py,sha256=UGl5BtedDpJx5McJAGtPlhFxtoZEbbHtxgi6AjY5IA4,236
mypy/server/trigger.py,sha256=ywN4YSEBcVDbHAHpGBhJPj57zCo8okQ-kxjKtQiyR9U,776
mypy/server/update.py,sha256=albF-tluD5wb2ExkoI6SQ4qBf9BNZWz8l9_yAXaJnkU,52023
mypy/sharedparse.py,sha256=2b4UXRGuqfUUNx7ArSa2YvKCuO9iI_6CMgUUQ3wkxUg,2176
mypy/solve.py,sha256=3ubZX3l4FMzx3OBfW0Z4evKckJniIBSTQ9ANX4fc834,2836
mypy/split_namespace.py,sha256=5kwyOJBHw_5CKnWIO2MoGt617dFWOUMytyOow7yhYbw,1259
mypy/state.py,sha256=3JngnrgdyuRYHHqk15F1QALQSHV_ioiLyWBcuDQA2a4,519
mypy/stats.py,sha256=hfgxuoWBMwJGFa7UCq-jlfPKrt_-WPbtjoqS4qxREpw,17119
mypy/strconv.py,sha256=4_waP8CP6laN0Nqjeyk7HxckgM8xiUye-E2sGEPUSbw,21577
mypy/stubdoc.py,sha256=RnS7_9UiZJPeL9PezyL6NjUYjB2uxpfoX1Zcb8bULLo,13816
mypy/stubgen.py,sha256=P-kRIlUiNMo47u16hzq8STerhn3TNImoQ6a6aKsGIUo,70323
mypy/stubgenc.py,sha256=_fwPXAJZG2gxSfzH8-VhhnxwZcAJQI6lusQTaahc3CI,19019
mypy/stubinfo.py,sha256=-taufYY4n99FAsdt0QqhbAXHiuwcMTuPwTSGmFVrD7o,4030
mypy/stubtest.py,sha256=n8SqWISL1BdsPY058ZuqCA_a7OVo9KIoGAlQaV_d714,51069
mypy/stubutil.py,sha256=nHKj1mM7Eq0VfTWf-mwESoHx72-bJ4HUlHk-9WW-yic,9696
mypy/subtypes.py,sha256=SJ9RumxRK_pI2PbddNt8hEt5yg9qAIt3VJ3EP9Dq4hM,68268
mypy/suggestions.py,sha256=Bx1hYdm36mPL0IFumoqstVCcdLje2HjOrxQ-NnE9u6c,39347
mypy/test/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/test/__pycache__/__init__.cpython-312.pyc,,
mypy/test/__pycache__/config.cpython-312.pyc,,
mypy/test/__pycache__/data.cpython-312.pyc,,
mypy/test/__pycache__/helpers.cpython-312.pyc,,
mypy/test/__pycache__/test_find_sources.cpython-312.pyc,,
mypy/test/__pycache__/testapi.cpython-312.pyc,,
mypy/test/__pycache__/testargs.cpython-312.pyc,,
mypy/test/__pycache__/testcheck.cpython-312.pyc,,
mypy/test/__pycache__/testcmdline.cpython-312.pyc,,
mypy/test/__pycache__/testdaemon.cpython-312.pyc,,
mypy/test/__pycache__/testdeps.cpython-312.pyc,,
mypy/test/__pycache__/testdiff.cpython-312.pyc,,
mypy/test/__pycache__/testerrorstream.cpython-312.pyc,,
mypy/test/__pycache__/testfinegrained.cpython-312.pyc,,
mypy/test/__pycache__/testfinegrainedcache.cpython-312.pyc,,
mypy/test/__pycache__/testformatter.cpython-312.pyc,,
mypy/test/__pycache__/testfscache.cpython-312.pyc,,
mypy/test/__pycache__/testgraph.cpython-312.pyc,,
mypy/test/__pycache__/testinfer.cpython-312.pyc,,
mypy/test/__pycache__/testipc.cpython-312.pyc,,
mypy/test/__pycache__/testmerge.cpython-312.pyc,,
mypy/test/__pycache__/testmodulefinder.cpython-312.pyc,,
mypy/test/__pycache__/testmypyc.cpython-312.pyc,,
mypy/test/__pycache__/testparse.cpython-312.pyc,,
mypy/test/__pycache__/testpep561.cpython-312.pyc,,
mypy/test/__pycache__/testpythoneval.cpython-312.pyc,,
mypy/test/__pycache__/testreports.cpython-312.pyc,,
mypy/test/__pycache__/testsamples.cpython-312.pyc,,
mypy/test/__pycache__/testsemanal.cpython-312.pyc,,
mypy/test/__pycache__/testsolve.cpython-312.pyc,,
mypy/test/__pycache__/teststubgen.cpython-312.pyc,,
mypy/test/__pycache__/teststubinfo.cpython-312.pyc,,
mypy/test/__pycache__/teststubtest.cpython-312.pyc,,
mypy/test/__pycache__/testsubtypes.cpython-312.pyc,,
mypy/test/__pycache__/testtransform.cpython-312.pyc,,
mypy/test/__pycache__/testtypegen.cpython-312.pyc,,
mypy/test/__pycache__/testtypes.cpython-312.pyc,,
mypy/test/__pycache__/testutil.cpython-312.pyc,,
mypy/test/__pycache__/typefixture.cpython-312.pyc,,
mypy/test/__pycache__/update.cpython-312.pyc,,
mypy/test/__pycache__/visitors.cpython-312.pyc,,
mypy/test/config.py,sha256=hlyuEz3oOWt4-ucH700Rnqv8Zi-ypRu8nUedOhdMkk0,679
mypy/test/data.py,sha256=xjh700HRcHpzM3pQ7mx1cHHKCZqtr4NFE4m4IGQVUGE,26641
mypy/test/helpers.py,sha256=QZIn7IJtibWvW5fr7wTMIJ7J24ZbJtUREQvQoqerZcY,17207
mypy/test/test_find_sources.py,sha256=ALEHTtOXfENsdYBd0GLZttR6Id10qtmjmPgmhxOKe8Y,13803
mypy/test/testapi.py,sha256=---IZm0oD6chHt2Swmjx_56jARDQNI6vVMlMuusZrpU,1413
mypy/test/testargs.py,sha256=Pj91L1GlG-c_n7bTp1w8Uj5awULxNfpQ_A9euNc1cOA,3251
mypy/test/testcheck.py,sha256=Ef5iAuoy40VTlUZJgeZwxKrn_VU5A0JC28w7a4ilRgM,14716
mypy/test/testcmdline.py,sha256=T6_Rdru-TKURWER-8PaeoOV0ZLztQuhhv8NXSd2UMlM,4527
mypy/test/testdaemon.py,sha256=85vL9kxfRRXHu0ZiCYsEkwI_jPiqYHQH2x7SFtI89EI,4673
mypy/test/testdeps.py,sha256=NpS_h1f6Ez4LoebjoepINALNKm6XcXGykCB180Y0tUY,3891
mypy/test/testdiff.py,sha256=VMUZoejE5dWUp3pUcHRHQUITMLq076Y6RXBN7vqjRKo,2559
mypy/test/testerrorstream.py,sha256=CjnN4CaCWjn8dqliAEZY4TkC-h-XdppqOiFxCJliJIk,1474
mypy/test/testfinegrained.py,sha256=bz2tHqRM2YS09wBjz2K0fq2VB9n_WMWEoRGPHA7ZEzs,13406
mypy/test/testfinegrainedcache.py,sha256=uuHzqvpZQ6L_u1q-toqAkLDwMFrkDaFp_msflvKqH5Q,541
mypy/test/testformatter.py,sha256=HUfVEJ8QiCyN7Hcbdg8jxASBh-3DZ2HZIShcA2CHmZk,2627
mypy/test/testfscache.py,sha256=i54aIK8X9T9OAoCbbX3D-Og_OLL9d9aUXkXv6A5Mx-Y,4451
mypy/test/testgraph.py,sha256=VbM3kyHjSpfujO39NFQ8Z82_miGD_amn_mwpGZbi9nM,3098
mypy/test/testinfer.py,sha256=e4-PpepEdr0oqR5zgK_ulTe99hCz-0PO89u3zQCF0Io,15649
mypy/test/testipc.py,sha256=HPIlIQqL6vEZ56Laaku4PUg66f0uDsu9rIW892Errrg,2241
mypy/test/testmerge.py,sha256=_CGDesaSUPIhdYg2FOaExGkhGwGSwHYoWoqCZTEtdSk,9248
mypy/test/testmodulefinder.py,sha256=VnTLJoW2yqETXKQfq7IiNkiipQ9PT7si9TzmgOXP4Ko,12984
mypy/test/testmypyc.py,sha256=X7Pd42QeaJ3HK1mbUaw-C6B6Z12N9XpbsImtdhvqmls,361
mypy/test/testparse.py,sha256=Qr4V616mti9jMzc0KfMZgYo1S4PR2px666KfFGVEI6U,2727
mypy/test/testpep561.py,sha256=M-KPcvsk9esMvFX01gMsBTeLZ9SQXIo3frUV502F8PA,7991
mypy/test/testpythoneval.py,sha256=FAbVThV37ak5q9Kvtknq4xTYb3G389VP2MvFZijnga0,4353
mypy/test/testreports.py,sha256=HWKnHuvUedw4lpMcx5jb1XmrBSo07S1SZjkzTgWnPHA,1422
mypy/test/testsamples.py,sha256=uZJV12IpiBsEUFfbHdQ3DopgW_ZHqPisbj7rNW-fQ-w,1337
mypy/test/testsemanal.py,sha256=yo6NiTaWuuMxITHXDD4WfAu8DtBR5jEE6fpRL2wFqaA,7569
mypy/test/testsolve.py,sha256=z_WdzAysUQ52Vh2136H8PchchVBVKr7hKtSxY92jIEs,5903
mypy/test/teststubgen.py,sha256=PtsONaUpvLHovs0NIgvc4wQd-S8cLmV0SX3BodV4mxk,41603
mypy/test/teststubinfo.py,sha256=RgVq4HCfFKaxNVeD1BL0JwhaGmHR5BTEp8YKdmTXxbA,638
mypy/test/teststubtest.py,sha256=vwiGGH9xFMjo9kxsraSKSA2Zu8XXYmJSWDjzfNExcrU,31377
mypy/test/testsubtypes.py,sha256=XuSSuIjp6D4MawKXdLwVJUnCjxSmp3ntvMB2R_3IUJw,8817
mypy/test/testtransform.py,sha256=4kcJlhYmPmlWA3Wb8O2B9w8YAINqGdJwvnaWAs7Bigk,2715
mypy/test/testtypegen.py,sha256=B8OgZpELsIKlqJ8C6ryXiPX6mfNbl-f4yQ8mSAscXRQ,2945
mypy/test/testtypes.py,sha256=y-Qfo5GZUpBu1B2eLAB7RYFXVRPni4zCzADXH7-GM2Y,47308
mypy/test/testutil.py,sha256=DVOE_KIxn7JSx2HzuonEMlPYd9JXOFg0BWzo_yZ1r8Q,593
mypy/test/typefixture.py,sha256=LQXcvX5wlTIwkURC4r_B0in6y8CDFO4GllHRr6AqFr4,13303
mypy/test/update.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/test/visitors.py,sha256=g7HsiVMIqQQqAwFMRzYcEYDOxKuTO0Zfpvn-sqrQsiw,2283
mypy/traverser.py,sha256=fN6HsKxZZ7EFDBnII6HXgHOSHRu3gN_vglh6VyHypwY,11629
mypy/treetransform.py,sha256=Bi4kYjAoYsYmfWwC8yR6_Z1yoopkGXR80bB4fxNXw8M,26425
mypy/tvar_scope.py,sha256=pJ5oJ98sUVxZmlccTDS7fPQ0NqRzy38aEbd6Fvt038Y,4118
mypy/type_visitor.py,sha256=2jS5KFCYnDzBm3Q2VclOugLGWZe0MipSK9ZomxeG72Q,11615
mypy/typeanal.py,sha256=HUwwo-mSuPkiRTJ21h5X7EwQsODDJsdopqnwqtDxITU,66610
mypy/typeops.py,sha256=XrWLK1WXC_wu6sV2aaALVNjcYPbC5QOGcE2RWFsw7Oo,34827
mypy/types.py,sha256=1zdYAAJkxRfaNtUY4XXbCi5PuNKt6YnQuhmC53ssM3A,95797
mypy/typeshed/LICENSE,sha256=tBt49WKi5lt1yrRDVDNfb0NdTvcwZVCWAKyRDPTiL-A,12658
mypy/typeshed/stdlib/@python2/BaseHTTPServer.pyi,sha256=DSo6AcxRbOEKSR9eGJIydnKLKtSFPLclVXM2MOnwagM,1709
mypy/typeshed/stdlib/@python2/CGIHTTPServer.pyi,sha256=pv5Xb3udKa7NXOLe3MI__b_zP6a0wmYuFmOYP_N3kuc,187
mypy/typeshed/stdlib/@python2/ConfigParser.pyi,sha256=dUxoC2eX-Jp9fdlnM3ObpKeYcRN0nvmydXMekaNweqU,3840
mypy/typeshed/stdlib/@python2/Cookie.pyi,sha256=92Zi44O6mqOTDVvdhSNdunEMkqV7KvEXSaKXTKKrl2M,1308
mypy/typeshed/stdlib/@python2/HTMLParser.pyi,sha256=4mV-tgsweFHjVH6VFVR6pwxsUJt8Ttszq4Ka3gtSOPA,1064
mypy/typeshed/stdlib/@python2/Queue.pyi,sha256=tpGX1wnyMznEbDwzA71nX0Ct8pkONuBmXKsPt4W3QUA,849
mypy/typeshed/stdlib/@python2/SimpleHTTPServer.pyi,sha256=Am5ArEYnO-BLiTWKj1Np4Te5RWwuEK9kR2MROlq0-C8,613
mypy/typeshed/stdlib/@python2/SocketServer.pyi,sha256=JGJuYgewH5XfzMRL5To-yaD7bsxqi2y5O5MT30Z_ajo,4506
mypy/typeshed/stdlib/@python2/StringIO.pyi,sha256=0eVpo3EB_yy-QllJVlN152TjFpFvwuIYNRTNeorI3Zc,1133
mypy/typeshed/stdlib/@python2/UserDict.pyi,sha256=SRS6XMX4zjs8Cg26Bn6SShmg2FqTtH0AORbYZV5BYl4,1570
mypy/typeshed/stdlib/@python2/UserList.pyi,sha256=aOIB5G_9vR_w2-xlBFj6hwTVHnLx5oGP6uZXaeq-xKk,617
mypy/typeshed/stdlib/@python2/UserString.pyi,sha256=ee9J1dVV85_6CGrlPI5KBOHH-YIMtwYvHBJHzcEQIcI,3835
mypy/typeshed/stdlib/@python2/__builtin__.pyi,sha256=JPoyxlwaISWk51l-wJNnhF_yXD_mlFKv_5O703dUFDw,48711
mypy/typeshed/stdlib/@python2/__future__.pyi,sha256=XCwdalx6eWh7nksAqKCHK-3bJp0ZjKS7g6iGaqLzoBQ,536
mypy/typeshed/stdlib/@python2/__main__.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
mypy/typeshed/stdlib/@python2/_ast.pyi,sha256=CREXsJkQ2OCYJe7rIlPZ2cTgNV-MuZglAS90cuMmDps,5632
mypy/typeshed/stdlib/@python2/_bisect.pyi,sha256=zW6FQ_eMdZebxVzw_9p2gxLauCBeGc-qXb_OP5Xdq5M,449
mypy/typeshed/stdlib/@python2/_codecs.pyi,sha256=-iqb7s1DAwadLosHOvMCTwyBMQKLU_RLAhRTdwNwo9M,4591
mypy/typeshed/stdlib/@python2/_collections.pyi,sha256=Oso87iPQyjb8ENwnnj55LBcvzLIpSqPhIEaN269MMj0,1404
mypy/typeshed/stdlib/@python2/_csv.pyi,sha256=o48DbmPaGMwMVkPbdQ9htFS4Bq4s0G21dbomW5uo7ug,1268
mypy/typeshed/stdlib/@python2/_curses.pyi,sha256=OyJB9jwPJ83u9qdn3HWIOgvsDbNynmRNBWaVlwHggjU,13714
mypy/typeshed/stdlib/@python2/_dummy_threading.pyi,sha256=d0V2E6_pT3iv9QGBDDBUWYz4iIGjAJzg9VtT8U-Ozek,3689
mypy/typeshed/stdlib/@python2/_functools.pyi,sha256=s_I1RupQha6TeO1JK0TbZf5vsi4B3wtbYehyTHeGxWs,567
mypy/typeshed/stdlib/@python2/_heapq.pyi,sha256=XFH3blqXcLc28JLZ1a1iJ75sRWh3eP2LzBEt9Z5oKpc,549
mypy/typeshed/stdlib/@python2/_hotshot.pyi,sha256=krYUeFkvew6-8npnA92iUBgiKU5Onilq_K2_K4z8KfM,614
mypy/typeshed/stdlib/@python2/_io.pyi,sha256=__YZm5n19Ctuz9pBhSJ1CPhr6uaNUUhnvCZz-hLRY_0,6877
mypy/typeshed/stdlib/@python2/_json.pyi,sha256=7Lnw20DzZ5N7LFyb59Sd7jAB4-MQ21yD_Ptk7CJ0L5c,205
mypy/typeshed/stdlib/@python2/_markupbase.pyi,sha256=bxmSN6aw-V3qwShSR82gk2l_ZrCF0KGQhP3baRo7TPE,256
mypy/typeshed/stdlib/@python2/_md5.pyi,sha256=pGqwb01a_RcSP1QRE8XtVPB0RKaoJOGuRdVk6pwvEag,300
mypy/typeshed/stdlib/@python2/_msi.pyi,sha256=JNB0W_OIsyHiC0hBBekoC9CSgNOrkpCzFNfJiCTAlp0,2133
mypy/typeshed/stdlib/@python2/_osx_support.pyi,sha256=KVj7__3pz3NajMAonY9QZ9lDCt9JhOr29uonFiBthiw,1749
mypy/typeshed/stdlib/@python2/_random.pyi,sha256=OEdeUw-4rrUPzD8bs7YeMTLAplfM5FQveP6eYdDuYzI,431
mypy/typeshed/stdlib/@python2/_sha.pyi,sha256=32F3_E2nGplztFti0fx5GwfPqobLiyg2rtTLHopfCw4,348
mypy/typeshed/stdlib/@python2/_sha256.pyi,sha256=EMj-hg12mxN_Te1NTVb3mX4YJJyuC_tHx4Ub45GgoL4,597
mypy/typeshed/stdlib/@python2/_sha512.pyi,sha256=2ta8R4vP9oq4rQkonGaG8eQNkbqp9feq9H1kEPDJHxY,597
mypy/typeshed/stdlib/@python2/_socket.pyi,sha256=qgozqpLLED0vrrXry2Eb8GaDrznR_sFI2a4kPc_poIU,6260
mypy/typeshed/stdlib/@python2/_sre.pyi,sha256=bbozl5UF3EHOki1QyqCU3M16e8uq9BVKF4kdNveBZ7M,1893
mypy/typeshed/stdlib/@python2/_struct.pyi,sha256=cAuIIq62g6PJsL_G5z16j4v5aTGqYDM6j69kyRcPIsM,767
mypy/typeshed/stdlib/@python2/_symtable.pyi,sha256=LLeUYtJkj_EBmkpwSffnopwY_lsPpopzvM-kXQOwg_I,677
mypy/typeshed/stdlib/@python2/_thread.pyi,sha256=NGUjx8HHEibK7FQkF2Bc0kvNhxdNeTGWt-I1mBmm3LA,814
mypy/typeshed/stdlib/@python2/_threading_local.pyi,sha256=1y8WTQOMyBV6cA0BZRbm0Q7bDtCDciIspnuKwYHTrd8,319
mypy/typeshed/stdlib/@python2/_tkinter.pyi,sha256=5jVGboY-TvJmADLtc0EwfqKsIq2GdvStw46qVDuN-u8,2709
mypy/typeshed/stdlib/@python2/_typeshed/__init__.pyi,sha256=3j2643ko92BSOOT6vw0w4RAoFKrB7kRViewPfg6aXDE,4498
mypy/typeshed/stdlib/@python2/_typeshed/wsgi.pyi,sha256=z9IBLS4C7TZ4zTgpzZTNfR5ryy_KoGMllF5ng-RSncg,1290
mypy/typeshed/stdlib/@python2/_typeshed/xml.pyi,sha256=V5_Pybqvfy_VWS9CS3Alr0bfVZij5icC5HU4g71CS_A,512
mypy/typeshed/stdlib/@python2/_warnings.pyi,sha256=0i6cQYB94NSOZT6grWtesPOw8lDjKN_D_dLp_3iP1Tg,901
mypy/typeshed/stdlib/@python2/_weakref.pyi,sha256=srxBO8YVoo6v_mWCWltMpI5n5vewKJ8qDhHWAtDhBGo,932
mypy/typeshed/stdlib/@python2/_weakrefset.pyi,sha256=rhGH9RSVygbloctD420hPhXOSNJFdLHEJVnppqBa__k,2174
mypy/typeshed/stdlib/@python2/_winreg.pyi,sha256=dXcjk6Kd4V16ZgJpyUiiBr9WnDuJs9cxy_J0mc5UBSg,3642
mypy/typeshed/stdlib/@python2/abc.pyi,sha256=rRN9NmWhSINACzOOJmsjl5p-HCkE0gByW1IqcaYYT_k,1179
mypy/typeshed/stdlib/@python2/aifc.pyi,sha256=cAOe4Mo5I8m_r0Ixg9l7oSoZHlJ4fXb6g7Hlm4z9E2g,2764
mypy/typeshed/stdlib/@python2/antigravity.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/argparse.pyi,sha256=f28dAF50wmVhsOAlNvL3n5rN3DZ0dA5aRbvlEI95rew,14084
mypy/typeshed/stdlib/@python2/array.pyi,sha256=y6zGjCYZITOdOr8yvXSy_K71NT4jsKI9LNjy2zOYvi8,3073
mypy/typeshed/stdlib/@python2/ast.pyi,sha256=s4YhqZ4oiy4pikcDw7dqs48lYZ_OiGL7fS2q6oT7F90,1155
mypy/typeshed/stdlib/@python2/asynchat.pyi,sha256=f7UjQ1ugfpiTm44Di-3Racl9xOW8PafLLIRBHfCA8Jg,1441
mypy/typeshed/stdlib/@python2/asyncore.pyi,sha256=PZ1HtilKeQ8G2N6D3quLr0yvZzFcWV-bwqQmivsj4Yw,5305
mypy/typeshed/stdlib/@python2/atexit.pyi,sha256=Zx4nX1WyFC_XDP2Pp17lXrMMJiaCjCut0VWHgtXWBok,117
mypy/typeshed/stdlib/@python2/audioop.pyi,sha256=iMZ1o5FfnC5IjBfX5R-C1i2KT51A2VMEfijiKQ38yHE,2100
mypy/typeshed/stdlib/@python2/base64.pyi,sha256=uaq350XMBqG0OI_xFvJiIr2fToFZKx1jW6Msk9RA8vg,922
mypy/typeshed/stdlib/@python2/bdb.pyi,sha256=iLy7ITW7mUaxygduanUS33oTLrbJlManG2vcTBGN4IE,4500
mypy/typeshed/stdlib/@python2/binascii.pyi,sha256=TkW3taA2nk7Cnr184UwPtS-KMBalcDQCBnmK6Jhd8TY,967
mypy/typeshed/stdlib/@python2/binhex.pyi,sha256=KPO4jyuFjgvRV1ryvanL0j0cIThoIoDisYOz3TBm_nw,1147
mypy/typeshed/stdlib/@python2/bisect.pyi,sha256=sQn9UUS0Cw5XZMEGcEj8Ka5VKPVobL43Pex_SagjXg8,67
mypy/typeshed/stdlib/@python2/builtins.pyi,sha256=JPoyxlwaISWk51l-wJNnhF_yXD_mlFKv_5O703dUFDw,48711
mypy/typeshed/stdlib/@python2/bz2.pyi,sha256=O7vZwHNgyh7d1dmm5J0JZn2iP47We4wc6QSzH0jAuII,1371
mypy/typeshed/stdlib/@python2/cPickle.pyi,sha256=FR7Mq3gugr2YQWV0TgHkrE0e-UgEB7OrklOlOg66vdc,795
mypy/typeshed/stdlib/@python2/cProfile.pyi,sha256=mwdpoQm31GtfdVnrTse--TCisixIF3pzNlYL7W_E2NE,1254
mypy/typeshed/stdlib/@python2/cStringIO.pyi,sha256=_QtKiLIEjTPX8bk5V8gJQNcicpacyfTCQYeABNhY3Yg,1803
mypy/typeshed/stdlib/@python2/calendar.pyi,sha256=Cd5F0Rb4I0Xz0QUFiVtSCj4GeXeBy70BEU6oLE-Bk1M,5031
mypy/typeshed/stdlib/@python2/cgi.pyi,sha256=EMKq4eL3CAYEmBPqztwsOq12IKCmXbDoz1ZmoV3qspA,4004
mypy/typeshed/stdlib/@python2/cgitb.pyi,sha256=IGpi97pL22Z55AYmmFDNiSU3Hpqp8fuc4_CLGd8jtiI,1332
mypy/typeshed/stdlib/@python2/chunk.pyi,sha256=juLgoUjtZJj3tExr1grorsVNUnVZtTgoJ8N9gj8opXs,613
mypy/typeshed/stdlib/@python2/cmath.pyi,sha256=w_X71N5YulunhSroYEup2IM-Ggjn5BAOqXrm4T44TE4,894
mypy/typeshed/stdlib/@python2/cmd.pyi,sha256=DVL7HQaKgRVoYxLXLZTmkpOLAOPj-F7vF-J4gWUplpY,1609
mypy/typeshed/stdlib/@python2/code.pyi,sha256=GC5g7y89f52mLeyAXyf5p3G3kwQVbnAf0QMQQ-EBR0w,1060
mypy/typeshed/stdlib/@python2/codecs.pyi,sha256=mefS3ro0jg6LWluXly1OgeHVuV4Lv56RJtZL0k_67lA,12026
mypy/typeshed/stdlib/@python2/codeop.pyi,sha256=p8xGn0wBvQpLp_nqlAvBA_4_kC64B9iO9UJiUTpwBU0,455
mypy/typeshed/stdlib/@python2/collections.pyi,sha256=vi65ZbjaAVEgGBdwW1FwuPEo4HPETD3Jf-eFb-cYa9I,4814
mypy/typeshed/stdlib/@python2/colorsys.pyi,sha256=JwCA0W2O9-JS0KA9u7kKNVJKRiu1ACOPzxy-SEKzDRg,578
mypy/typeshed/stdlib/@python2/commands.pyi,sha256=nGsS9_5mqYgZqIx-g-A0eQ4Vd9WQWvp53Fv8PEpAeic,329
mypy/typeshed/stdlib/@python2/compileall.pyi,sha256=RuBoJTLlvL0PY1v73Z_Sjk2bIcr4e_f0Q-AjB9uOAa0,549
mypy/typeshed/stdlib/@python2/contextlib.pyi,sha256=B6jiQU5ukG8ikgPWNEHFrdoD-535qe-0M4KdPpd3Bqk,1010
mypy/typeshed/stdlib/@python2/cookielib.pyi,sha256=QHlG-MiRVM731Tfv40wj1rHp9t9aSUaIIuq38VXVCqM,4664
mypy/typeshed/stdlib/@python2/copy.pyi,sha256=-PNuq8Znc-Bai1JTKfeqdZbu_0qW1KR6crzyn_PWono,315
mypy/typeshed/stdlib/@python2/copy_reg.pyi,sha256=M86xHfuZNWZb28DovWje7B4CRf_QRmKsQX17bfC2-lM,741
mypy/typeshed/stdlib/@python2/copyreg.pyi,sha256=M86xHfuZNWZb28DovWje7B4CRf_QRmKsQX17bfC2-lM,741
mypy/typeshed/stdlib/@python2/crypt.pyi,sha256=9wRxT1YekqCpEwZ6CCbGyqDNhhgF-m_DpYgl5zI_A-w,44
mypy/typeshed/stdlib/@python2/csv.pyi,sha256=WTl9_WG1L-R46MzN6imzl0Vwii1_rQYgTnC22dAgvuM,2555
mypy/typeshed/stdlib/@python2/ctypes/__init__.pyi,sha256=-qlAHjIb0f79DhvIgz3-4FsXZ3xCoPi6G7KcqnHALTw,11446
mypy/typeshed/stdlib/@python2/ctypes/util.pyi,sha256=Lf_nOeoLmPYs5DJ00UmxbAlTex7Z8bwByM3bSg4wg1w,129
mypy/typeshed/stdlib/@python2/ctypes/wintypes.pyi,sha256=4mLfzJ8kXytQo4DDsO5HX13sZWXUcs-XdwPygO6MOE0,4642
mypy/typeshed/stdlib/@python2/curses/__init__.pyi,sha256=e6zyQJFe4QJgc6mUK3ZqOqRTKGXq_QxJLXLs3vyVQHU,370
mypy/typeshed/stdlib/@python2/curses/ascii.pyi,sha256=SyQ58p2bv9-V5-D9j_6rBoV463YZWIErwhYYxLrBDZs,1133
mypy/typeshed/stdlib/@python2/curses/panel.pyi,sha256=Wsl42xkXk8GQesNABDijIoBVX5Nx8dGm6prO1-gxlyU,801
mypy/typeshed/stdlib/@python2/curses/textpad.pyi,sha256=t84r8Htx35C4vIky8zoSME_DkyQseFa5Wn4ugh9zG1M,431
mypy/typeshed/stdlib/@python2/datetime.pyi,sha256=Q7BG1Z8Nwk1UQUGXuTB-FAYE1xtl9s4oWtyHF5iS9Us,7724
mypy/typeshed/stdlib/@python2/dbm/__init__.pyi,sha256=RTSqTjrJGd8STF9TW4isaCSUjuzUTimkXzZNBC98H-0,978
mypy/typeshed/stdlib/@python2/dbm/dumb.pyi,sha256=MvVcZdC0x5bbsXc9vGdjhz4BEEvGdxikl9HFvL_ib9I,991
mypy/typeshed/stdlib/@python2/dbm/gnu.pyi,sha256=RMFMbTtxYCCm0n1HGCbaX0Z98g_Xy291nvZ7Rtntjdc,1323
mypy/typeshed/stdlib/@python2/dbm/ndbm.pyi,sha256=qE4RgaYO21_uHAQ40KPAi-SbWHbRdkQCmrhPCTGpHkA,1202
mypy/typeshed/stdlib/@python2/decimal.pyi,sha256=w6k4TpBksMaGfMy0j5kGH1vXMXHHXhgXSlHntjsrjDs,13305
mypy/typeshed/stdlib/@python2/difflib.pyi,sha256=dGQk1IlXblqsQ-h3_H-238j-e8kl8qeKU_Bj9QO1tRk,3608
mypy/typeshed/stdlib/@python2/dircache.pyi,sha256=uQCeC3YysI4DYx-n-TXtbaV6-8pdVHl19hkUGz9lWRA,255
mypy/typeshed/stdlib/@python2/dis.pyi,sha256=usQeOUZXnggMxmYC4Dv2uneO9EI5qmF8uo2aP416U6U,1078
mypy/typeshed/stdlib/@python2/distutils/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/archive_util.pyi,sha256=H5RVUDKKtYIY052de-i-gvcckKSl0q6Fb3HSxShhTpY,388
mypy/typeshed/stdlib/@python2/distutils/bcppcompiler.pyi,sha256=fge2cMbG4jp--o0I2zNcwykh24tJWZtk6leQgAH2NJw,78
mypy/typeshed/stdlib/@python2/distutils/ccompiler.pyi,sha256=NXPeu_xPRvnR0jCHG5WqadCmku7WjkM2KMpp7F5vLyA,6263
mypy/typeshed/stdlib/@python2/distutils/cmd.pyi,sha256=PkEsChoO3A9nggYgRl7XOvTXR2FVCnP950vq1Wo3RMU,2753
mypy/typeshed/stdlib/@python2/distutils/command/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/bdist.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/bdist_dumb.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/bdist_msi.pyi,sha256=sDSqH7TRcOiXC5S4VXxJ_YHB-WFPpa1fo8F8g5XeV3Y,182
mypy/typeshed/stdlib/@python2/distutils/command/bdist_packager.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/bdist_rpm.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/bdist_wininst.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/build.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/build_clib.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/build_ext.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/build_py.pyi,sha256=z4m9RU6PID-qalM7jzvc2mIWMwk0saczeEmqq9qleH0,181
mypy/typeshed/stdlib/@python2/distutils/command/build_scripts.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/check.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/clean.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/config.pyi,sha256=IqpHcIXZFIzozo5b5WbMwqF6U55pQnn8R2ApU_56tSM,2901
mypy/typeshed/stdlib/@python2/distutils/command/install.pyi,sha256=sRjw70WN-a-GYCpjaDvDY2WntgtOLRqK92tBTkEIt7k,316
mypy/typeshed/stdlib/@python2/distutils/command/install_data.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/install_egg_info.pyi,sha256=OcT2tcyEIIJ2L7rCA0DPazvsYFz1ozAPV2Ll4K7Mu24,367
mypy/typeshed/stdlib/@python2/distutils/command/install_headers.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/install_lib.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/install_scripts.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/register.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/sdist.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/distutils/command/upload.pyi,sha256=n8qH_q3mlK0HyKbYLw9lQr3PxQ4Ql55YdcHq3x5-HL8,286
mypy/typeshed/stdlib/@python2/distutils/config.pyi,sha256=EUQP6rymbWfxZJ6qxNWdUYwy-GyZBcnYmBn04C6liEo,510
mypy/typeshed/stdlib/@python2/distutils/core.pyi,sha256=rifRNGVjWN_mcByii7xIeqEEnFDL31hS3Cq-DHv4TAM,1656
mypy/typeshed/stdlib/@python2/distutils/cygwinccompiler.pyi,sha256=Y7qhVOqXrkPT0yyQnudNCtTzBYC2lzS38HB5Mh45zEI,138
mypy/typeshed/stdlib/@python2/distutils/debug.pyi,sha256=7_zuriUBqHbc62x7tCONq7LQXLuK_hCaBK0laoR3HeY,12
mypy/typeshed/stdlib/@python2/distutils/dep_util.pyi,sha256=QCheHEDF7waISF42_aaumqvVOIcTw-yh5e5-CbPvQ2o,252
mypy/typeshed/stdlib/@python2/distutils/dir_util.pyi,sha256=0nHuLCqZ36gvDVaF6PoC76_JOC3v6P_310eFauW1ZDM,555
mypy/typeshed/stdlib/@python2/distutils/dist.pyi,sha256=xh2xwNv3RtnsYH2qoaX7LmqG5_iZTgnEoEenn_9wv9w,489
mypy/typeshed/stdlib/@python2/distutils/emxccompiler.pyi,sha256=FxHjF75rMcBNR4odfAyfbbayTXE-2tfbAkHtKxGgYHw,90
mypy/typeshed/stdlib/@python2/distutils/errors.pyi,sha256=l1W_FgoP9L-D-hEPFA2BzZuybjN0lV4WBXl0VJ-k7J8,852
mypy/typeshed/stdlib/@python2/distutils/extension.pyi,sha256=JScc0ADXLO-oo5ximrU6aevdKGj4FaH8sTQ1vD-PmUs,690
mypy/typeshed/stdlib/@python2/distutils/fancy_getopt.pyi,sha256=5_7Ka2cfcK6tFOMx24setmI6XkMVnKpBowyKKKRUp88,831
mypy/typeshed/stdlib/@python2/distutils/file_util.pyi,sha256=X_KkiEdHldsM4rF8MWooliHEwJT1KouerVKKGV9nmkw,426
mypy/typeshed/stdlib/@python2/distutils/filelist.pyi,sha256=-WeYFFKsEUUjPvbzeZbVCOKkPV-oqc3RoZvN2SB1VOE,20
mypy/typeshed/stdlib/@python2/distutils/log.pyi,sha256=9vvQVRer-_-S5lcV7OHF1Ptr1N3npjKvzTVXReSpZKA,863
mypy/typeshed/stdlib/@python2/distutils/msvccompiler.pyi,sha256=qQLr26msfhjz-omJutWcRHik3shLh1CIt7CDI3jBd3I,78
mypy/typeshed/stdlib/@python2/distutils/spawn.pyi,sha256=DGh7bqTkZghQANUbQw7-Xim7J3VB2KJI65pFukM5w88,211
mypy/typeshed/stdlib/@python2/distutils/sysconfig.pyi,sha256=vh2IaZIUdIBkwbUep--ZdZ3YJuLB6FRt3y77UZuTVPg,586
mypy/typeshed/stdlib/@python2/distutils/text_file.pyi,sha256=O_FroMX1Elg--tqqukRgFYK6NCgwBOdIGKx06bhatOE,685
mypy/typeshed/stdlib/@python2/distutils/unixccompiler.pyi,sha256=R3VKldSfFPIPPIhygeq0KEphtTp0gxUzLoOHd0QoWW8,79
mypy/typeshed/stdlib/@python2/distutils/util.pyi,sha256=LABzGlevH__sNs-FWnLuiWlG8il9YwqkCuUgwIr8qpE,807
mypy/typeshed/stdlib/@python2/distutils/version.pyi,sha256=7zD2PkRFVzDpDqc8lFxctqmik60C6oQAtnzVJTV7w4o,1107
mypy/typeshed/stdlib/@python2/doctest.pyi,sha256=uyKFhIixHcrIhdXDxXzaun7k78zopR4720TuaZ_qBPE,6719
mypy/typeshed/stdlib/@python2/dummy_thread.pyi,sha256=sj4sgs4BMK0uassRWZN9BcMDc-nFHLovq5l3Z5eNzDE,775
mypy/typeshed/stdlib/@python2/dummy_threading.pyi,sha256=ZI04ySfGgI8qdlogWtA8USUTFGfzm32t2ZxL5Ps53O8,79
mypy/typeshed/stdlib/@python2/email/MIMEText.pyi,sha256=4Hjv1f-LZwoj-ihndmbQNHdwpjOy6wUOJoKS_axJmNo,159
mypy/typeshed/stdlib/@python2/email/__init__.pyi,sha256=QmHr2DwpnlHiemG2j55rkI5xCkcdV1IlXDWIjc-RnJ8,265
mypy/typeshed/stdlib/@python2/email/_parseaddr.pyi,sha256=jbthfAl9SY0rALBB0DzbntZDOGVMwmKnC1uYH_6pPaQ,1059
mypy/typeshed/stdlib/@python2/email/base64mime.pyi,sha256=Qb1Q4NHIbSJOcsZ8vUBqaPT-s6lWpj-YD1kI9DI6Xfo,303
mypy/typeshed/stdlib/@python2/email/charset.pyi,sha256=VVEUOTe1XZ824-FhBuIBrSCB16hMAnQ1Ygseu3Noc_Q,902
mypy/typeshed/stdlib/@python2/email/encoders.pyi,sha256=s8kQE5AG1wvh0h0qbNn3_As6ExYQccVdg6Bx2PKGu8E,143
mypy/typeshed/stdlib/@python2/email/feedparser.pyi,sha256=cKLfhKboxZeJxceH5e_broSJZDa4teMu_ZJvZRhREQU,536
mypy/typeshed/stdlib/@python2/email/generator.pyi,sha256=TOAFU4Cb0_a3EitMT62JWGtcoGuvgrfKlbWpNAmwEuA,377
mypy/typeshed/stdlib/@python2/email/header.pyi,sha256=sCk_MfWl5P_bc5v9302SubX0hqgODtlpJsnPb6h-eC8,457
mypy/typeshed/stdlib/@python2/email/iterators.pyi,sha256=vPq5eJF8HBwFQ1hS--niEmurSl4x42YOrU65TxKk0Jc,256
mypy/typeshed/stdlib/@python2/email/message.pyi,sha256=M3XzQbdji1k8_hygt88priwEMJqWKRixQsN4qDLmfeU,1950
mypy/typeshed/stdlib/@python2/email/mime/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/email/mime/application.pyi,sha256=zlzwumM16ipPeDnDgS11A0hzIJ1LEegxL7g0-I3dSWw,371
mypy/typeshed/stdlib/@python2/email/mime/audio.pyi,sha256=GYtzGiC2dTHTPD3Cm3uIUlBAQ_25NK2BsbbcuDbLZiU,176
mypy/typeshed/stdlib/@python2/email/mime/base.pyi,sha256=lG1Re_xRHsaw4WRUnLh1Jyneb4M6m8kxqa0NUfwuONg,128
mypy/typeshed/stdlib/@python2/email/mime/image.pyi,sha256=-HVa8k6js_9-sGt5jFg2SF-UjZ6cLP53T9GvRIVI63s,176
mypy/typeshed/stdlib/@python2/email/mime/message.pyi,sha256=OwJjEUiejk2bc9FGqgbvz8Q6ZgQyAg9gphhDXPyXsLU,147
mypy/typeshed/stdlib/@python2/email/mime/multipart.pyi,sha256=1pTSK5lU6L5AJG5H35PTIQtHYIplMoipa7Kkd_m9HNQ,159
mypy/typeshed/stdlib/@python2/email/mime/nonmultipart.pyi,sha256=C9WcyywCzQqkL9MPpSlWHgChmP04r0rrWVw3VlSVHQo,107
mypy/typeshed/stdlib/@python2/email/mime/text.pyi,sha256=4Hjv1f-LZwoj-ihndmbQNHdwpjOy6wUOJoKS_axJmNo,159
mypy/typeshed/stdlib/@python2/email/parser.pyi,sha256=9QChl7gsm0KPwZHUYy5tR_kZkmQpdSnxCwuZTnp9ceo,415
mypy/typeshed/stdlib/@python2/email/quoprimime.pyi,sha256=ZRJzHi-3Fszfa8nRpz6EpGYZdSpLyGc4K3pxr1uyMUA,490
mypy/typeshed/stdlib/@python2/email/utils.pyi,sha256=ioyL79tcg2t4gXrV9pq6yp5ZQSQUf6FuXIdmXL4YhlA,738
mypy/typeshed/stdlib/@python2/encodings/__init__.pyi,sha256=q95Eqw-U2YICiPawMyarXy1iWCXsz1wV_793ZSg_4ek,184
mypy/typeshed/stdlib/@python2/encodings/utf_8.pyi,sha256=tgCdNX8etJQWYWmOYAIZhK8lcYm_Kn67kylKJp0SgUo,573
mypy/typeshed/stdlib/@python2/ensurepip/__init__.pyi,sha256=GBsa1HBs0MSt7W0Nc-ebh1Pm9jT5wMPy6JQK2hrCtx8,214
mypy/typeshed/stdlib/@python2/errno.pyi,sha256=KDjlJTkt1sdcrwGgLkPMZlSwz1Dp0Xkt6PqEnqcLZWY,2011
mypy/typeshed/stdlib/@python2/exceptions.pyi,sha256=i3AvRM6Osg9r_q5outQ4hn7COs0fyhsJizSH5M72G7k,1756
mypy/typeshed/stdlib/@python2/fcntl.pyi,sha256=cRYcJCmkljcz9A-8rVyOjjFrsUstHfrTd5H_sBRiuk8,1561
mypy/typeshed/stdlib/@python2/filecmp.pyi,sha256=Sg3EWMFrT8PzwZzfRbmLJoA-ob77wOnBwtYZTFEtYHo,1434
mypy/typeshed/stdlib/@python2/fileinput.pyi,sha256=sHsHV1onoo-f6gp8YaSRft6kkQuiOIyowt_j3CPrA0c,1479
mypy/typeshed/stdlib/@python2/fnmatch.pyi,sha256=8kgI-ZZR0lhAGSuQk0M0kt3cYrYRx29bwhIg9ESvLbs,348
mypy/typeshed/stdlib/@python2/formatter.pyi,sha256=yzaMOJQcXfaEMXMNV7RPFqvCm6ow8xuBO-IkiTvsZaU,4578
mypy/typeshed/stdlib/@python2/fractions.pyi,sha256=P3PzPoY5_jF-gyZfAvL3MmTA-V5CWVvGea0QC8MfNZE,5233
mypy/typeshed/stdlib/@python2/ftplib.pyi,sha256=qPrCptXITxjTtCB5ijRaSSJwGxKiKILTU4B1hmLPEC8,4907
mypy/typeshed/stdlib/@python2/functools.pyi,sha256=niWJuDlXf0gaPe0nTmHCYTQdxfL1GUiAhJEOBBM1yJo,1131
mypy/typeshed/stdlib/@python2/future_builtins.pyi,sha256=vkVYaUei63-XJqSnDDLn7KwkUyLpVwbP01ZGTRWlySc,194
mypy/typeshed/stdlib/@python2/gc.pyi,sha256=5Lfpz5C3KDyocZ8qEKncV-mvf192A-3xlMModHwVFi4,752
mypy/typeshed/stdlib/@python2/genericpath.pyi,sha256=yaM2lyVoIfrIP-l5lhQnqU_q1iXlp3LnnWYFa4JwSFA,1112
mypy/typeshed/stdlib/@python2/getopt.pyi,sha256=6hPbDzz4CuSglcyFspFGyWCNVW0AKygfMXPTD0LKI8Q,448
mypy/typeshed/stdlib/@python2/getpass.pyi,sha256=5FZTPudjVwTRUD2BQJhOr0pyxEyTeXUxYmj_xt1VC0E,160
mypy/typeshed/stdlib/@python2/gettext.pyi,sha256=Xhzf8LiGHVPa-St8FHOzPigNhirAMAELOOrgTBeqh3o,2208
mypy/typeshed/stdlib/@python2/glob.pyi,sha256=N1C0T_3qlgRms4tEXgaBosvLdIj9__3nj0_sLQXQsS0,350
mypy/typeshed/stdlib/@python2/grp.pyi,sha256=zsuV-xPHpzToOzBJxrs7dTEraiwlvK7X2CYg02UyIC4,281
mypy/typeshed/stdlib/@python2/gzip.pyi,sha256=YIsrQQFHmhyG8XrqIaWZV1glkmXHWa837LAk-f_ZDT0,997
mypy/typeshed/stdlib/@python2/hashlib.pyi,sha256=LsKxu--Y4ZYq2Pf5OlPa8hFQelw8zawAIWyWKWlybFw,1095
mypy/typeshed/stdlib/@python2/heapq.pyi,sha256=6yXrWLV3PPxpRLe82aUFzCGHHtxLs_mW1fG9fpFYRfY,725
mypy/typeshed/stdlib/@python2/hmac.pyi,sha256=i35HY3Wa8sv3zPgfNWZIq2VdXY5KZNvevRkq75TogBg,799
mypy/typeshed/stdlib/@python2/htmlentitydefs.pyi,sha256=1dyH0i00daNQ_7gDuT-mxXzx-V_nDSRuF4q_vjkSUHg,114
mypy/typeshed/stdlib/@python2/httplib.pyi,sha256=sDzaMcTl0Qt3VAAH4zkmffI2mi3jKm0hFFPnO0dnWNA,5824
mypy/typeshed/stdlib/@python2/imaplib.pyi,sha256=zbFs0fPMOYtQh1sSgO35AZd4Di5zLVS7J4aabdQdQaA,6164
mypy/typeshed/stdlib/@python2/imghdr.pyi,sha256=izbCt2ganKFMKdwiMfl6AZUiuqh4dyWVy_-3rM2nbvw,460
mypy/typeshed/stdlib/@python2/imp.pyi,sha256=XxX6QYWZVwKDSF7JRbR5ZoHbpCve7Pa793Gp7JXa7f4,1277
mypy/typeshed/stdlib/@python2/importlib.pyi,sha256=-CGFRgyK4ZRqvp_SZ7EA-3XJ-iez8RYr38-Hglff5Lk,121
mypy/typeshed/stdlib/@python2/inspect.pyi,sha256=ujAuiZpgfSrqImCvQnRZGKhBQSsUSj_3wesr5XGYHxQ,4617
mypy/typeshed/stdlib/@python2/io.pyi,sha256=IscZHqbVtY9vMd3bIsALRU9KFU85Py-UGUlB7i-Z4xA,1124
mypy/typeshed/stdlib/@python2/itertools.pyi,sha256=9pKtakIkfy5IRqhaVMvvZqImi_ICRCsZXeE9uMZvxw8,5941
mypy/typeshed/stdlib/@python2/json.pyi,sha256=QfF1vHt6z8Hp9OKybR4_jR8R8NUOOoqrwlj92jbQw04,3066
mypy/typeshed/stdlib/@python2/keyword.pyi,sha256=SmvqmzqenWQNuzcGJielAYV3NonCY8rzlIduedZlgMI,94
mypy/typeshed/stdlib/@python2/lib2to3/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/lib2to3/pgen2/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/lib2to3/pgen2/driver.pyi,sha256=z0KuoXp5SSeN_gfDssZIruLNHdNCdtPHzcipGwOrygQ,931
mypy/typeshed/stdlib/@python2/lib2to3/pgen2/grammar.pyi,sha256=zuKtRoL1GMizL8ij0akjhZVXH2mBsbb2LxJkFJPi0ME,733
mypy/typeshed/stdlib/@python2/lib2to3/pgen2/literals.pyi,sha256=CSUI0I_W6I9fN-nb6rGgzLjGMIO0C6OwOmzYz-HEQZU,172
mypy/typeshed/stdlib/@python2/lib2to3/pgen2/parse.pyi,sha256=ey0HyvJuT00Wg6jCVWRlyrPNSi8YFOtyc3MvtkD6R7o,1073
mypy/typeshed/stdlib/@python2/lib2to3/pgen2/pgen.pyi,sha256=r4X9oBXPlnqNaWbngjdebfmQ2GiS__sR-zfkmYCn2XU,2118
mypy/typeshed/stdlib/@python2/lib2to3/pgen2/token.pyi,sha256=XbxOU6rIsL4k8Q1cWy6EjHkkAhV4ULlyu8UBifyInfg,919
mypy/typeshed/stdlib/@python2/lib2to3/pgen2/tokenize.pyi,sha256=pA9uvZ22MHGVm37U1xhMEde2hUPvJQJBq6XDQ3Gzcts,883
mypy/typeshed/stdlib/@python2/lib2to3/pygram.pyi,sha256=lyhgf6kpNVMPN6g6W-DSxt8E2_IyVguf7JjKzVhAmzc,2208
mypy/typeshed/stdlib/@python2/lib2to3/pytree.pyi,sha256=G17KXjvX4PDZWo05wo56ft_bpsWBAb26pSkZpMjcacw,3158
mypy/typeshed/stdlib/@python2/linecache.pyi,sha256=oiRDSRMR6fqnm9e7yNSUBvQO5J3JbRYmx3yN1Csyah8,447
mypy/typeshed/stdlib/@python2/locale.pyi,sha256=h-CRTSajghyujWJnNVgxFC1Eutdtsi5pmunGVYvS3oE,2202
mypy/typeshed/stdlib/@python2/logging/__init__.pyi,sha256=OrjjfotbXLZUdjWn1x7y-KEOfMwzHO_qlY0kLQAjQzs,9635
mypy/typeshed/stdlib/@python2/logging/config.pyi,sha256=ZxHdxuDo0xkcpBoH6irGizgwf1ItyeON1_N_s5HAI7A,365
mypy/typeshed/stdlib/@python2/logging/handlers.pyi,sha256=hov8li22CV0FAymYOJGL_DbsK9iFmLBf9NXJEPoMu9E,4146
mypy/typeshed/stdlib/@python2/macpath.pyi,sha256=DxXy81tV430lSUq2QafMuhuppeJD0CGF7bJ3-P-3sWg,1640
mypy/typeshed/stdlib/@python2/macurl2path.pyi,sha256=AX2ReRSdgi3Mw_8zm0RXsnGeunJ4Ke2bfSwFkXTC4p8,140
mypy/typeshed/stdlib/@python2/mailbox.pyi,sha256=q0MslgoenuQdC5YuPM9-5ilrEdHR0hpHWQHnvTkW6Fo,7619
mypy/typeshed/stdlib/@python2/mailcap.pyi,sha256=tapSJSLwZFVxwSGUSw-GRbQOUONWiPjx20g2bkLEJzw,314
mypy/typeshed/stdlib/@python2/markupbase.pyi,sha256=GmpHjRP9pa1Ybup4LFHoYS0TKu9oh8EOqX5-CY2yNb4,264
mypy/typeshed/stdlib/@python2/marshal.pyi,sha256=zXZaX_H3VD9clK-iiNZz7f5GKDdG4_TriqlTPCBR6oM,253
mypy/typeshed/stdlib/@python2/math.pyi,sha256=AWs6mE-wPFBRGzk2DHGuuF9PIkC6wt8Zpq_jxXOsSco,1986
mypy/typeshed/stdlib/@python2/md5.pyi,sha256=RxbpEbnpbF491HuNmiN3legMGS-8W11hHBxE1tcy7b4,74
mypy/typeshed/stdlib/@python2/mimetools.pyi,sha256=vZd4d0QRIrqrvjtei-P-uK-vhduilbUpT55hlBGzIFA,703
mypy/typeshed/stdlib/@python2/mimetypes.pyi,sha256=1YZzlslNR87oMYMYJwLknEDF6eW722XCsT8do9wuaWM,1409
mypy/typeshed/stdlib/@python2/mmap.pyi,sha256=dnU8XHad6KpWPilJjAoQA8ja_aL_Fjt_OGgkipBqprk,1796
mypy/typeshed/stdlib/@python2/modulefinder.pyi,sha256=GO1buDCfJNYBkP3AKf1-wAOgPlkcN7DlnbghxzNEz-A,3126
mypy/typeshed/stdlib/@python2/msilib/__init__.pyi,sha256=qZCfEqJiwu5Gy1zhTYGCsdY7rX0dlbAy2CeYqeqMNEc,5906
mypy/typeshed/stdlib/@python2/msilib/schema.pyi,sha256=CW8r60s_87S7N-T_1qPgTSV0Hsfc1Rvd05JkDIfEMlQ,2172
mypy/typeshed/stdlib/@python2/msilib/sequence.pyi,sha256=XQC-7D28bgEuJzAFC7WIX86AtUBrQ7pC5Sc1JTyPwEk,356
mypy/typeshed/stdlib/@python2/msilib/text.pyi,sha256=yECOLJZaQbIsrcCNS53CgwtdE0rNPw02cz00ePv1SPQ,186
mypy/typeshed/stdlib/@python2/msvcrt.pyi,sha256=DDiTa2YmqkChpLzKr80n-ZOfJAXWb6YYB0RER013dHw,795
mypy/typeshed/stdlib/@python2/multiprocessing/__init__.pyi,sha256=oDBJUM6-PUZkqF3pCSvzZoqoySGuMswzjG9FKjl0Mqc,1890
mypy/typeshed/stdlib/@python2/multiprocessing/dummy/__init__.pyi,sha256=FdwG9M_okJwQ982zTQPBGCy40YualVqTN0EQFsWPmGA,1173
mypy/typeshed/stdlib/@python2/multiprocessing/dummy/connection.pyi,sha256=wUC0PdGC8NjvL-DxeylRTFePzXrHhxiG1_u3Mcs58x0,654
mypy/typeshed/stdlib/@python2/multiprocessing/pool.pyi,sha256=z8wnmSHtqTXcr9aWNIatBjJE5KR6xi-eLnD70v4ZNfE,1986
mypy/typeshed/stdlib/@python2/multiprocessing/process.pyi,sha256=-yvTXmXu1G9eaxYYl548DM73F_bon54DD1-DhMmC5XE,870
mypy/typeshed/stdlib/@python2/multiprocessing/util.pyi,sha256=Cofj85Nd-TDGTXwNPhSUOqdj4XrG-23GW5goqXu4egA,736
mypy/typeshed/stdlib/@python2/mutex.pyi,sha256=TjAl-N_Gt1GMDKwHdLf2yfe5m0TVHkWrg4qm5FfEFCM,339
mypy/typeshed/stdlib/@python2/netrc.pyi,sha256=KELbVP7ewjNH4op0KsNKj2ciozwXYj7HbJ4x5zN7AK0,550
mypy/typeshed/stdlib/@python2/nis.pyi,sha256=CUnTx-mKL-YinbUfrvw8WIWLwuqu4PtSVZ9M2mxSSvY,322
mypy/typeshed/stdlib/@python2/nntplib.pyi,sha256=kJiIEYZpca-zIh3mZTQq028CIdbhrHaQcdpt4ctoDig,4180
mypy/typeshed/stdlib/@python2/ntpath.pyi,sha256=nuUdj_12j9Ta6Gav7ySfflTilTG3oD4Ypk7Sx73bZuc,2797
mypy/typeshed/stdlib/@python2/nturl2path.pyi,sha256=_u8yHiGMMnRRTjQAs37HCefvy5193SJDBUZTw1nZ0I4,115
mypy/typeshed/stdlib/@python2/numbers.pyi,sha256=gfdinsGBWXj5A-0Z9UZ9J5yFggAWqcKQRYFMi6X5_qQ,3648
mypy/typeshed/stdlib/@python2/opcode.pyi,sha256=PgZgQmvmaNZB-NLyH23EV1xf6h88yVz5seP4NLGgV7U,279
mypy/typeshed/stdlib/@python2/operator.pyi,sha256=n4ZJd7sId9Ca3w6ILKyYOXRmWCq6DZcDhaBmLtcc_gs,7438
mypy/typeshed/stdlib/@python2/optparse.pyi,sha256=s99RIwzS8CqwyGVCqkq6MJe_7NeTTUqhYvuLpVhPdK8,10015
mypy/typeshed/stdlib/@python2/os/__init__.pyi,sha256=1UADS6rI2-Et1H1dkq8et4wrgAzz33orZnGHmn_H42M,11530
mypy/typeshed/stdlib/@python2/os/path.pyi,sha256=LXauRNtfoNtJt3a7wKAB12aQ4xnJH4Hp1I_-IFar3tE,2792
mypy/typeshed/stdlib/@python2/os2emxpath.pyi,sha256=nuUdj_12j9Ta6Gav7ySfflTilTG3oD4Ypk7Sx73bZuc,2797
mypy/typeshed/stdlib/@python2/ossaudiodev.pyi,sha256=frJ_3GI9RGqngiM90FCXT8rl72JGep9BX2t7zd402Xc,3065
mypy/typeshed/stdlib/@python2/parser.pyi,sha256=M31hc6UA8ziZaj_mxqqjan-OxlwAyl-u7lOk4KYRpKM,930
mypy/typeshed/stdlib/@python2/pdb.pyi,sha256=MAIa_x91U_zjqyy_mqLCcuJzeD6FDrzfgP6CXeWWkSs,6442
mypy/typeshed/stdlib/@python2/pickle.pyi,sha256=l7B1BN7-b9GScio_8PJp9_pS2lqn2g_9De29w0GA7fs,2125
mypy/typeshed/stdlib/@python2/pickletools.pyi,sha256=UUR7dSxc2lxYIZkIsFTLOg4a9PPLq4NvQ21OsGSsAXA,2960
mypy/typeshed/stdlib/@python2/pipes.pyi,sha256=OTfpqql0CUZDbJx-Ka4gWuoDub7FsF7bH7N1PplvU6s,467
mypy/typeshed/stdlib/@python2/pkgutil.pyi,sha256=W8V4QeoUAYFy6lNztE-KbnFSwd-Psb3BjE3ffuAyCIg,1168
mypy/typeshed/stdlib/@python2/platform.pyi,sha256=dtZBGSK5BPvqm-WSVZvUPjwKQR0-90_y97LeEkaSKwU,1793
mypy/typeshed/stdlib/@python2/plistlib.pyi,sha256=dKHpvDmGZ5iwkXSdt-y2oElFqlnk11gMHB1b-wpoKoo,1031
mypy/typeshed/stdlib/@python2/popen2.pyi,sha256=gkom0odmjMRk9T9aexaZitKAdiuwiKjOTXGG2DK4W_A,967
mypy/typeshed/stdlib/@python2/poplib.pyi,sha256=McULUycsryabFb1Lcs2MzOynE7tgcbJPf90Xkw0r0Ec,1423
mypy/typeshed/stdlib/@python2/posix.pyi,sha256=QSE9_2VVyIGFhtOEFYVvXYeIrTyjRCk_pYYiPJ2VCB4,6364
mypy/typeshed/stdlib/@python2/posixpath.pyi,sha256=nuUdj_12j9Ta6Gav7ySfflTilTG3oD4Ypk7Sx73bZuc,2797
mypy/typeshed/stdlib/@python2/pprint.pyi,sha256=lcEKXSUoi6cTV4ioq-0814h8yjCsbCoq3SZz7Wd458k,891
mypy/typeshed/stdlib/@python2/profile.pyi,sha256=6UXGJz3h-0pE7qHEZfjoR-jPUVtJAl5J6gjx9LoJ9fo,1270
mypy/typeshed/stdlib/@python2/pstats.pyi,sha256=c0IvFjhjXLd9Z2jUA7xCjLC1Eref7fmHcaMGkNO7Kqg,1878
mypy/typeshed/stdlib/@python2/pty.pyi,sha256=AtwlwyzjbSscFqljkTbmv7WND-uKYOt8mYyAYd5J0_o,411
mypy/typeshed/stdlib/@python2/pwd.pyi,sha256=UlU5YFcnP6NejDJPcPDn8n1iK-qEsZ7u-zdbR3ufzrA,354
mypy/typeshed/stdlib/@python2/py_compile.pyi,sha256=O7WE88SIYH5sLvmoSgb06nyO8cJgOT3VJ4sgMql-FbQ,490
mypy/typeshed/stdlib/@python2/pyclbr.pyi,sha256=2H2QrmkVWd2I8XR1oKtl-mT0cr3JrzhAor7MvhUus60,657
mypy/typeshed/stdlib/@python2/pydoc.pyi,sha256=dYBC8Lv49QkPCnDaLHZBu2XcsBrXGLe_DRJQOUlErKk,10229
mypy/typeshed/stdlib/@python2/pydoc_data/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/pydoc_data/topics.pyi,sha256=PLI9r9ujTHeIKw7D9GxCeB_LwxV17eUTauVx9AKiU8k,48
mypy/typeshed/stdlib/@python2/pyexpat/__init__.pyi,sha256=4vGA0YySvkqav-sj5bZCcNbmljgm18VS6LLIGdMh0eE,3216
mypy/typeshed/stdlib/@python2/pyexpat/errors.pyi,sha256=hvQH3tTGliR-dkZxqHvvyFjxWfX6Jc30tebWXC4sMek,1152
mypy/typeshed/stdlib/@python2/pyexpat/model.pyi,sha256=LlBMaLvt3TBD1JQSAQTYUrHKAfKNEeQYaDw5GsJA--g,205
mypy/typeshed/stdlib/@python2/quopri.pyi,sha256=0zH--hEadPU_f_GNqwlxU8vrs2tqmPaLCO94BeZXCyo,343
mypy/typeshed/stdlib/@python2/random.pyi,sha256=8oeiOQxa3MJtuKgLomOIeskuqQcNpguqqVRUwh-T7j4,3136
mypy/typeshed/stdlib/@python2/re.pyi,sha256=g_L-C8mT4-xtIrDRaaKqIHaEZ1mj4vN1Sf3g0j7Bfis,3394
mypy/typeshed/stdlib/@python2/readline.pyi,sha256=k6qBmZ7yN8azkF36B4XHOgUD_YdRPpLb26vBM5ssik8,1433
mypy/typeshed/stdlib/@python2/repr.pyi,sha256=Ic8zKDnkZLcpQmOGBLvScWrBx4sYHHmlA4gSoIrSQOM,1095
mypy/typeshed/stdlib/@python2/resource.pyi,sha256=ZMxMAadyqjJ7Nmvu-GFPdy5XwG7RKunEkKu3LuRevPE,877
mypy/typeshed/stdlib/@python2/rfc822.pyi,sha256=NY_5l0Lf_SFOR7ujkOBJlfJdqWPsumeRq34u4-HAojo,2147
mypy/typeshed/stdlib/@python2/rlcompleter.pyi,sha256=0yYYs-v2GTssnTDOprFML7ZAtUbZA7wc79Z_DdFiZyc,226
mypy/typeshed/stdlib/@python2/robotparser.pyi,sha256=IpfpnvNmCtN84yyZR9TmNdCQA7F1M5MQcqbUdkwoPXQ,230
mypy/typeshed/stdlib/@python2/runpy.pyi,sha256=M21xZNGc_XrCoNR_JUu3GdqFhx0qhT_fTRoo_IeXQrA,519
mypy/typeshed/stdlib/@python2/sched.pyi,sha256=8o6hlituoao07J9eHx2yqq12JWuM1k6sq1YIjLuk-S8,744
mypy/typeshed/stdlib/@python2/select.pyi,sha256=GjOYMzH1mzUmaAeIfXuluk50cfHTT4ZHnLxEJaWD3eU,3564
mypy/typeshed/stdlib/@python2/sets.pyi,sha256=6JqearuY8coc7Gv9dMhNcOilECcdt1kcHbxM_r0sJx8,2949
mypy/typeshed/stdlib/@python2/sha.pyi,sha256=35pkvQygB0J3E1etUSO_ijjsk_H8Q8k6EYjuNMLFHQQ,236
mypy/typeshed/stdlib/@python2/shelve.pyi,sha256=wqjWO36TM9KfJchdNAAfkIkvvKJCGr_gtofnGaTYpkY,1590
mypy/typeshed/stdlib/@python2/shlex.pyi,sha256=Hr3DLwJIWFeqFVXU8JzFBh05TAIZImlTNzDMhvHRFq0,990
mypy/typeshed/stdlib/@python2/shutil.pyi,sha256=pgX_ERPwY7GKEZ7BMzma5FfTt9DAwD17QPYpkXXqBJw,1828
mypy/typeshed/stdlib/@python2/signal.pyi,sha256=-MfrlwCJjsm2R0fIEV8ZflElH5-48pPJOx-i42iJFkY,1566
mypy/typeshed/stdlib/@python2/site.pyi,sha256=U9XpvnCP__zThvAo-gTRogsqbWoEJN3xXH3QzU5VYgA,381
mypy/typeshed/stdlib/@python2/smtpd.pyi,sha256=myGBwuvJWNW5O_SNcLwEh_nAtE1buMObIuy2QVOI154,1734
mypy/typeshed/stdlib/@python2/smtplib.pyi,sha256=8wiSP1iFF9-l9IKgh8p6S0rGwLuguGQfFH3xyWPh4ec,2542
mypy/typeshed/stdlib/@python2/sndhdr.pyi,sha256=Bkeu5nMcvS5ZU_xcHup7Bur58lCLCtrx5sZ2bbi5wD4,204
mypy/typeshed/stdlib/@python2/socket.pyi,sha256=OjdbOPDZuKqmMnZSi2zUabn4AjcGN1TWJAQMiwNnN6w,12589
mypy/typeshed/stdlib/@python2/spwd.pyi,sha256=BDoGUDub7DFTKhD_tzXW6DbD3uGX15Ujm2DzuFF_cvA,308
mypy/typeshed/stdlib/@python2/sqlite3/__init__.pyi,sha256=aJu9MCNl8y9HCykdUpo1Z-LSiP3mxRSxrWkCsMxemYI,43
mypy/typeshed/stdlib/@python2/sqlite3/dbapi2.pyi,sha256=0kAnQrilXVKmgRtcs9G-REz0C1Dw9iYfliJV-JREk5s,9534
mypy/typeshed/stdlib/@python2/sre_compile.pyi,sha256=4GGUI7tr4dJ726rsb0XwUjGgPqwBUgB-AQftUal59DY,784
mypy/typeshed/stdlib/@python2/sre_constants.pyi,sha256=T6kBTKeYYGkM83SbbgVx9L38eaZgqEY-AkgfCLr9GbU,1744
mypy/typeshed/stdlib/@python2/sre_parse.pyi,sha256=HUdN2coOWGi8kK-wQ-qJteK5tflYaqHAGVR7b8Qz908,2272
mypy/typeshed/stdlib/@python2/ssl.pyi,sha256=vdIzuqGbG8dhiq8O50Kq1hAApvtjZogsuLqpf-NZMKQ,9343
mypy/typeshed/stdlib/@python2/stat.pyi,sha256=Tzy8jDY2wz2pZucTjKwCHly-4C9c3bhLBpQaZW8zk7o,992
mypy/typeshed/stdlib/@python2/string.pyi,sha256=v5CUiaeoYK5UQA3ih3jUsqhbvvH0aQbgSF_xT0ZrU_Y,3514
mypy/typeshed/stdlib/@python2/stringold.pyi,sha256=BhsQOCvrynJ7WsHXCb5QfdUyzZjmH4F1P7sx4krBwTk,1991
mypy/typeshed/stdlib/@python2/stringprep.pyi,sha256=fqKAxHgpLExMmFDO66rJ-kFZS5mVKeWvK_qWQ2yvsWc,817
mypy/typeshed/stdlib/@python2/strop.pyi,sha256=kF2oXemBZd_VaHlTzV19pp9fi8iwcVsq8avQS8YwdXc,1157
mypy/typeshed/stdlib/@python2/struct.pyi,sha256=j0N8jxgkDv2760aSXn-_rveqmue7xJmtWS791hNXOy0,1046
mypy/typeshed/stdlib/@python2/subprocess.pyi,sha256=TOgUd1hLOLw_LLYcA1fuE0O-aBEIRY4PNOuoFYSp2XA,3216
mypy/typeshed/stdlib/@python2/sunau.pyi,sha256=AJHWICr3MaqFS5Fdh7M1oBp-gyeo2lA_qNfaEzVa1G4,2372
mypy/typeshed/stdlib/@python2/symbol.pyi,sha256=gmMHvO88vurNaeIXHNnl7UgNPg0gdf8D6iuxX5aTJiM,1341
mypy/typeshed/stdlib/@python2/symtable.pyi,sha256=1IVFbRxDU8zDf04WFOJMYWfQGsM3o446aBPASL-E69o,1696
mypy/typeshed/stdlib/@python2/sys.pyi,sha256=g_X2qzs5pJzD7aNchU-ZOVeBtJnoLPuskFb4FkBe6yA,3549
mypy/typeshed/stdlib/@python2/sysconfig.pyi,sha256=z-M0RgbhiMupX0PdlvJE_5vir5XA4Byhqzj2LkhnMtY,839
mypy/typeshed/stdlib/@python2/syslog.pyi,sha256=WbINKcwKTg5WFsn4f06K3-jriyoYXzqnOU9HwbAFbZY,821
mypy/typeshed/stdlib/@python2/tabnanny.pyi,sha256=kIHchnfwXgWn4GeSp0TMnp52aWw5Ibk6_gdrxBL_Hck,420
mypy/typeshed/stdlib/@python2/tarfile.pyi,sha256=50AZC8KU6GK-kTBr9vOGm3rpBW8B73qYjkyfbdhGock,9239
mypy/typeshed/stdlib/@python2/telnetlib.pyi,sha256=SchzRzZbWlGbvK-hOJmKrGdyfv8OFeTDlXS_VE9wzwI,2519
mypy/typeshed/stdlib/@python2/tempfile.pyi,sha256=q4FMAq-yGr67KkZJqhe6NeswBLp4j-tCtbO4gF9vlDc,3575
mypy/typeshed/stdlib/@python2/termios.pyi,sha256=vN0zq_JTJuJKMgBYvKirwRAt8YzIhJxzCnmvO-oRpOM,3526
mypy/typeshed/stdlib/@python2/textwrap.pyi,sha256=8VV3JRR4Lq73UZ4t9gtYOBeM-YcbOCaGbUDPmKHQeJM,1854
mypy/typeshed/stdlib/@python2/this.pyi,sha256=5hi7CD2tChI9_genuMBxsS8GOFo0gAVvFGuv-_Uc9p0,50
mypy/typeshed/stdlib/@python2/thread.pyi,sha256=P3v99RXZZFRPUzpe9St8fswlzP4IEStuPFKdlwlUJvk,920
mypy/typeshed/stdlib/@python2/threading.pyi,sha256=d0V2E6_pT3iv9QGBDDBUWYz4iIGjAJzg9VtT8U-Ozek,3689
mypy/typeshed/stdlib/@python2/time.pyi,sha256=UeEH2bNzkUnOi3NNuHiuDsUazskdcjfPcFTbKjHCcRs,1301
mypy/typeshed/stdlib/@python2/timeit.pyi,sha256=r4awXSgu3pMkf6SrUwh5uahGjt1Nk7g8gLCdpfPrZRg,880
mypy/typeshed/stdlib/@python2/toaiff.pyi,sha256=FA2QwiYCh-YuKQyMuSj4DhQQo9iTASevNspzkoWfRB4,243
mypy/typeshed/stdlib/@python2/token.pyi,sha256=7GNqRjVaw90zVqr2qqeQdxHsnJsMyMk3LcsrrgsaVJA,899
mypy/typeshed/stdlib/@python2/tokenize.pyi,sha256=QmBuDGv3u1JV1lnRC_gcwIsvZlTwePul450uAerhmXk,2679
mypy/typeshed/stdlib/@python2/trace.pyi,sha256=mQmEUf3aRV-6Y92CnWk01mXl0uxQy_bnfALio9HcReE,2380
mypy/typeshed/stdlib/@python2/traceback.pyi,sha256=hP1MVSq2eVaWoBww4ynBx4gy398yinNOJk9GFeIstYo,1480
mypy/typeshed/stdlib/@python2/tty.pyi,sha256=A25_a1yrTL55nQAsEpOWKsni215-75a4rAoFJ7g7Qr0,275
mypy/typeshed/stdlib/@python2/turtle.pyi,sha256=-EfvLJFJd9RfVcABcnp8n1vcnuSpGhQ16Ej3b4Ft_ZE,17270
mypy/typeshed/stdlib/@python2/types.pyi,sha256=L7bXf_LP580N-OJD1zULQpwZ_8FM4xlUgrthPqm5vhM,5374
mypy/typeshed/stdlib/@python2/typing.pyi,sha256=JZEdcjuxLprgONzUCGflgmh4MsNmbpWKtXHNJzY_4hU,17535
mypy/typeshed/stdlib/@python2/typing_extensions.pyi,sha256=PYgH43-crcT3_Vhh_uCOnHXVSP6H2HlIgp7Qs0Q0m7c,2980
mypy/typeshed/stdlib/@python2/unicodedata.pyi,sha256=RX8S6sUykl22nG1jVn5tvStQouqbXruRsLQ7-HBkVzw,1620
mypy/typeshed/stdlib/@python2/unittest.pyi,sha256=iyx5utO2lPGV607VDrKDrsHEMNQWS3h5QFXHidtXNWo,12595
mypy/typeshed/stdlib/@python2/urllib.pyi,sha256=-d0IUFI7vJu28ItD6NLLGdc9hvnnmlFi_W2VWtMzjFY,4752
mypy/typeshed/stdlib/@python2/urllib2.pyi,sha256=1lGrdLhFXBe6Gf1hB-azYcsGpJsCgo9Du7cUexkV2b0,8262
mypy/typeshed/stdlib/@python2/urlparse.pyi,sha256=z3j3W2GsJQSVUEnBv4epa8Z3kX2co42we-SjgYLccLE,1922
mypy/typeshed/stdlib/@python2/user.pyi,sha256=Mz3QGfgG58h1uuCR-b9r_Pv8L-vA8oH4I4NN6L6hfC0,83
mypy/typeshed/stdlib/@python2/uu.pyi,sha256=Ad45a-YFEuIvacZcArtjgV1sEIJN8H2iwyDIzZUWeh0,319
mypy/typeshed/stdlib/@python2/uuid.pyi,sha256=vpB4JFnh-OCJ4qyAUMjJE6ZjQVPsMLQKep3MFFWN37Y,2380
mypy/typeshed/stdlib/@python2/warnings.pyi,sha256=QZXLoLVzNXvBUnMF0P9op3IFHRVmfMmcfWfioSOaSNM,2029
mypy/typeshed/stdlib/@python2/wave.pyi,sha256=oJA8wQGRqRxSszBAKSPKwdtA_XKvN0y0UZWFaino2Lc,2101
mypy/typeshed/stdlib/@python2/weakref.pyi,sha256=R11X4T-Qyoncnkis2cXuOc6h5b21_Jpffov6N6EfZPg,2964
mypy/typeshed/stdlib/@python2/webbrowser.pyi,sha256=GjePmYTFrlogHaUI0R4o27WJoHZ61Yu0ePPr2l_bYyc,3002
mypy/typeshed/stdlib/@python2/whichdb.pyi,sha256=lRr3RFZP2SsfjyTuXGWWlcAlg7AVwohiisE3NNrBRbY,72
mypy/typeshed/stdlib/@python2/winsound.pyi,sha256=v1Yto63zOzzxynR5IiKyNr6ft1zcU0bKN00y8aB6SJI,782
mypy/typeshed/stdlib/@python2/wsgiref/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/wsgiref/handlers.pyi,sha256=D2m4QwW_zQYCRss6KS8RyVUBV51lLSYaItNNTEBnlt4,3015
mypy/typeshed/stdlib/@python2/wsgiref/headers.pyi,sha256=4auiumJHNst4_Jb8Jr7eww3-QxRED8Zf1Po0ViH4qSk,977
mypy/typeshed/stdlib/@python2/wsgiref/simple_server.pyi,sha256=4VrZOKLhDpyNq2aJuA685V3yKE78zFZeBPk8VspMXvY,1397
mypy/typeshed/stdlib/@python2/wsgiref/types.pyi,sha256=1qhS0qVWoV0IVfe3b1y4Mzhou65EPtwb_QLmWfX5_I4,71
mypy/typeshed/stdlib/@python2/wsgiref/util.pyi,sha256=gbnzvEoZ0XioPeXI9I0xVdWX4bpZw4694ae2-38cnMQ,782
mypy/typeshed/stdlib/@python2/wsgiref/validate.pyi,sha256=33o0s7Up8Mdph_dX9TzQ2NTerbYXI8Gp487kt6vBvag,1591
mypy/typeshed/stdlib/@python2/xdrlib.pyi,sha256=yHZnDsb7h-kuMZ04mQip7YRjBSX-g900SOE0HhGyMl4,2315
mypy/typeshed/stdlib/@python2/xml/__init__.pyi,sha256=BqMXnsXiYPoalMzEakn6mYDxgyW5N2UPF0Ao7xPuGVY,30
mypy/typeshed/stdlib/@python2/xml/dom/NodeFilter.pyi,sha256=bi0L5SEOxk4FyEhf18oU-I8Msf9S9o_tJt-mVc93f28,457
mypy/typeshed/stdlib/@python2/xml/dom/__init__.pyi,sha256=gjfWhkwyNoY8SeH6cztWZ9W8w9E4CLgCpHeP8vnHM5c,1844
mypy/typeshed/stdlib/@python2/xml/dom/domreg.pyi,sha256=hQPp7AUJ02uKbiNKvBNOrH1RkGbHfCNVd-Szl-C-7Ok,422
mypy/typeshed/stdlib/@python2/xml/dom/expatbuilder.pyi,sha256=wI_eu1G8yaaquRHmZ9mYRgjy4zNNhJC385TjSMoamRg,77
mypy/typeshed/stdlib/@python2/xml/dom/minicompat.pyi,sha256=jXMKSRZq4F0gwGs9U9TU1rlTRTVil2XS7d70-BfqOIM,517
mypy/typeshed/stdlib/@python2/xml/dom/minidom.pyi,sha256=yLctp9Su-UEax8GqYgB8JvuWaLJ3zB51OsraaI7oIhg,10313
mypy/typeshed/stdlib/@python2/xml/dom/pulldom.pyi,sha256=wI_eu1G8yaaquRHmZ9mYRgjy4zNNhJC385TjSMoamRg,77
mypy/typeshed/stdlib/@python2/xml/dom/xmlbuilder.pyi,sha256=Zih-KjswK4cRJKEbQ-6flIfo9sCwRtVVJ_v4GvfV2uo,173
mypy/typeshed/stdlib/@python2/xml/etree/ElementInclude.pyi,sha256=sBdQ69UuiEc-Yvrse9bbBE8rQJzmW7x6bZZH_6Y7-b0,574
mypy/typeshed/stdlib/@python2/xml/etree/ElementPath.pyi,sha256=fqhuuyLzEW_9yuEC7tyo27S519bfPntms9JrfschhyI,1535
mypy/typeshed/stdlib/@python2/xml/etree/ElementTree.pyi,sha256=YXxfl6yeR5-NWFAPYpR0CxpoAwUc4dcLImtCcndqRv8,8798
mypy/typeshed/stdlib/@python2/xml/etree/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/@python2/xml/etree/cElementTree.pyi,sha256=D25RVU5Y1Sai47EQ49UTStrYaY39HMqT1HUOZrSioRg,50
mypy/typeshed/stdlib/@python2/xml/parsers/__init__.pyi,sha256=FHZYB9bXDrj4RiKUgrctpkuf7_Rms9PqQrGyjkn0EE4,34
mypy/typeshed/stdlib/@python2/xml/parsers/expat/__init__.pyi,sha256=qmz8tuPGbZ2rBfRrfYANxDZNxn9BTQXdd9AugF5wDW0,22
mypy/typeshed/stdlib/@python2/xml/parsers/expat/errors.pyi,sha256=mH9YRZuV4quzksDMLEmxiisAFgNhMOhl8p07ZzlS2XE,29
mypy/typeshed/stdlib/@python2/xml/parsers/expat/model.pyi,sha256=M7GVdd-AxOh6oGw6zfONEATLMsxAIYW2y9kROXnn-Zg,28
mypy/typeshed/stdlib/@python2/xml/sax/__init__.pyi,sha256=rj8dtzpuWUnf7ZJQ_Ywif4kJMaRrdDU7k9v6yCfUg8o,1219
mypy/typeshed/stdlib/@python2/xml/sax/handler.pyi,sha256=lzRvfmcsC4Px11th3C-OB53OJgrSxHHTkgWKkFnYYII,1391
mypy/typeshed/stdlib/@python2/xml/sax/saxutils.pyi,sha256=a2PjJ6EkGG9psk1w9QgffM-z8wstZNthDcuYhP89Gr4,2464
mypy/typeshed/stdlib/@python2/xml/sax/xmlreader.pyi,sha256=G_y975cphG0gL_WbGTW0RfS79jIgXxQD4CQ-1L21Riw,2431
mypy/typeshed/stdlib/@python2/xmlrpclib.pyi,sha256=tQhcyucplG6BZrTdFTcrX9b7a-aYcjQb2hTiYNJtJb4,9516
mypy/typeshed/stdlib/@python2/zipfile.pyi,sha256=gKR4e_7ZxHIhdgA2Ik2ocaUNKxCA_63GgT1lMvNIoCI,3480
mypy/typeshed/stdlib/@python2/zipimport.pyi,sha256=8p3KTSPaPOz0FzagtyoQyi5S8g_FLnbtl_Z3rBMMTFw,618
mypy/typeshed/stdlib/@python2/zlib.pyi,sha256=oMLItBYekHeS7lBk6chjQ4rPG1z8Dcsbz0pK0zvPI38,1214
mypy/typeshed/stdlib/VERSIONS,sha256=vRuO1CoCIpMplRU6V7qZxx-NzTEXJT_psUOKKehK69I,4744
mypy/typeshed/stdlib/__future__.pyi,sha256=1MRtcsqJaHbyDXSVOPgAqJ9BeUB3691edVikyspI4yQ,1236
mypy/typeshed/stdlib/__main__.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
mypy/typeshed/stdlib/_ast.pyi,sha256=atD1XjzOLvXbPiLgv3D4hPhAb3fDiP-QD7uczYCEceI,8447
mypy/typeshed/stdlib/_bisect.pyi,sha256=vGn1jOu4_wQrdLj7Zhaz7PnOAZaAoo_roHMYPicTL4Y,1230
mypy/typeshed/stdlib/_bootlocale.pyi,sha256=0g5u3OybUgqbQUaK32XpV2OY1vMr_tMyvv-xWePyn60,63
mypy/typeshed/stdlib/_codecs.pyi,sha256=1MDu-MZBg8q1yslUytFchT7iwLugAEl4FLhBS_YAFTc,4721
mypy/typeshed/stdlib/_collections_abc.pyi,sha256=sm4_vVumr_ZjkaVH6Nj7bV9w1dYJpXxWDw1fAtgT77k,1999
mypy/typeshed/stdlib/_compat_pickle.pyi,sha256=gcQlheXNmaXQDN02BMH_fzcSHWjbkUsPO3-smFIabew,382
mypy/typeshed/stdlib/_compression.pyi,sha256=-UD5lkBbvX-_kDllNI0QB5nwo781cEJkECg0eIjyC-s,902
mypy/typeshed/stdlib/_csv.pyi,sha256=TytYuNtnQHFlGq9l1RUNhEWuh7z_eVz3dMep_KVbyLY,1255
mypy/typeshed/stdlib/_curses.pyi,sha256=dnyDnp72DzhAAHWPfnd5YIcST1rv3iBC9Z37l8JpeuE,15042
mypy/typeshed/stdlib/_decimal.pyi,sha256=gKb8JF9qlgs-k7lWkc6x13adH918J9IBSh2vJJO2-Es,22
mypy/typeshed/stdlib/_dummy_thread.pyi,sha256=ysYcxOvYihvkLhPAGb-gAHSSFkXIhUbggs7Aicspdkg,775
mypy/typeshed/stdlib/_dummy_threading.pyi,sha256=wHCbsib-GYbvIT9sL-DpkTtFTo3N2-xeZ69UxLKeNZM,4888
mypy/typeshed/stdlib/_heapq.pyi,sha256=Pz-P4eIgvgSJXPXpiisXhlSRy3aOOMjKxWO_c1MkObM,308
mypy/typeshed/stdlib/_imp.pyi,sha256=5XzYk35c9xkFXl5QBzjFMhKTf3sOuBDUIg6wrRU7vpg,699
mypy/typeshed/stdlib/_json.pyi,sha256=olHX0WaqupfNdCNpCnVVv9JVeG8hE1xASY1y8pPsf64,1095
mypy/typeshed/stdlib/_markupbase.pyi,sha256=EdHIjbTk78O122yQ_5PuGw0F5K-7a7395HjFwkbHpqY,230
mypy/typeshed/stdlib/_msi.pyi,sha256=tpiRdsCnhbE9FkiGAEmMIzBffNhmSW0TUi97sqoifuQ,2109
mypy/typeshed/stdlib/_operator.pyi,sha256=cW8gndiQv2Ge6SxDU1TYCmdDJ1jjCpRO4Blp3IzYBJ4,1310
mypy/typeshed/stdlib/_osx_support.pyi,sha256=ZED3NqTfGbYOcCxqQepuqZP57zNwM8jbBRGMa3Wy3vo,1737
mypy/typeshed/stdlib/_posixsubprocess.pyi,sha256=0Pwh7GNUYY4cf5w8Bv8lyZc-f9Tswa2yYrIOVF1EJJA,550
mypy/typeshed/stdlib/_py_abc.pyi,sha256=r_uuZdkN5rSBE6yg9EuJeZIkQGI7I9XC1pvg51wuD9M,370
mypy/typeshed/stdlib/_pydecimal.pyi,sha256=droa2p0DPjlLpftTkyseRtRiWWwFEFuBmsKmAEBykHI,157
mypy/typeshed/stdlib/_random.pyi,sha256=hqLwdKR0g1-2oAbX1TeYW0Z7f-7J2c_5ZkZ4gk515go,386
mypy/typeshed/stdlib/_sitebuiltins.pyi,sha256=yjauTn1qmYT-ObLT7hZOOSfHo3c4yOnyLw1VbmTWvEk,521
mypy/typeshed/stdlib/_socket.pyi,sha256=PZqL_Padoq-Ep1d6iYxv_fjrqwR6AeQCZcHtPiJcZks,16347
mypy/typeshed/stdlib/_stat.pyi,sha256=rOfqArOAol_zPagL6lyRodbmL93Qsb1vl1UMNz9x5QE,1179
mypy/typeshed/stdlib/_thread.pyi,sha256=sW-3ZbJe180sS7vJbgyBUatP4S066EbMoYyTCJNA1vo,1481
mypy/typeshed/stdlib/_threading_local.pyi,sha256=134APITj1puVo4mA1eGTH_x1nYt75EBOrU0sl8Nudco,483
mypy/typeshed/stdlib/_tkinter.pyi,sha256=SmZwZEEhS3ES6SqaBCZq4i9yGa3lAdkFKjXlhtKIVjE,2730
mypy/typeshed/stdlib/_tracemalloc.pyi,sha256=hHX3zlYOvw7sq8c-80z9kgHGx4AMlZqozgmNz9-qVWU,543
mypy/typeshed/stdlib/_typeshed/__init__.pyi,sha256=m4kO43ZKeGyARKsM4QUTuTS-3RrOfWk8F8FUFYA5vK8,5925
mypy/typeshed/stdlib/_typeshed/wsgi.pyi,sha256=YCopwjybCnTbvdaF66Tq6ZYN0zgsrpbcYFCsofSfNg0,1236
mypy/typeshed/stdlib/_typeshed/xml.pyi,sha256=AVf9HdlLmXCHk3bM9uSH58OBpd2xLIEHvVHRW8L5BVg,519
mypy/typeshed/stdlib/_warnings.pyi,sha256=EYyZ8vDOHavfCq2qCBkN6Np22F_bw6YnzUWbW1jZVh0,1032
mypy/typeshed/stdlib/_weakref.pyi,sha256=mA4khZlbqHRuzAaFsw3QqhcYIIlgvi2y3wDSNFW-YjE,1209
mypy/typeshed/stdlib/_weakrefset.pyi,sha256=KHRFWik2fAZIeEshk9YS-vbWnDpt6RXDbH-sfpQL2Vo,2354
mypy/typeshed/stdlib/_winapi.pyi,sha256=rq4M03k-La6LBz469d6xxkAe0_73UR90MuS2mxKB7U0,4442
mypy/typeshed/stdlib/abc.pyi,sha256=gi8YyWZukrCYDSKslScUqLWsjJb7aZ2d1ci_0HSQZ48,1001
mypy/typeshed/stdlib/aifc.pyi,sha256=p09za5I-CAQis591UmZwNkCfA4YznVboPNuaIhx_cwo,3266
mypy/typeshed/stdlib/antigravity.pyi,sha256=6osWHq-nRG8ayI4MedcfKxCiC5hYWneWhhbMywhLPvo,76
mypy/typeshed/stdlib/argparse.pyi,sha256=LIYlr5ZA8-HSxQjR-Oc78BQCkuzxnIeFJnQFSmRa5_0,17783
mypy/typeshed/stdlib/array.pyi,sha256=Ad4P3TvOefHy8OyrMpDyL5QPjm92R3xYCBGl84MVrZU,3097
mypy/typeshed/stdlib/ast.pyi,sha256=AdKIhRpk5KSAVUAn1X0mbcIY9ccV5yav-i_iDH0SUiM,8994
mypy/typeshed/stdlib/asynchat.pyi,sha256=Wgk-AoOd36DC8HoAbWcjHWTP0Tz02C_Rtq0hQP3TAe4,1099
mypy/typeshed/stdlib/asyncio/__init__.pyi,sha256=vQqBti5lCesBtJosU6ISR_TaGXUnon5V2niuGmIAFgM,4242
mypy/typeshed/stdlib/asyncio/base_events.pyi,sha256=znzM6JTh7kF80qZpdVcIDa3kOKL9SoTaM-FniDjOhj8,14768
mypy/typeshed/stdlib/asyncio/base_futures.pyi,sha256=PQH3nZQ1z5EECQEJfFvTHRfbBAhO7o_Wf-dR9FtZwEY,720
mypy/typeshed/stdlib/asyncio/base_subprocess.pyi,sha256=na-QFf_tpljEQ_KqsXS5_o8C7hJe43-L-yD9bq2CfSM,3195
mypy/typeshed/stdlib/asyncio/base_tasks.pyi,sha256=1qMENIsXTar5-dVXn33qy8hpWzOtFOs_I-kf5I92dsI,404
mypy/typeshed/stdlib/asyncio/compat.pyi,sha256=dOjM_tSAjZHUChFWeXdiK5zGJt7Gn5XXat-EqFF2P_k,156
mypy/typeshed/stdlib/asyncio/constants.pyi,sha256=lIsw7OkZUE_CNOswnhp-6ZdNa9Ed-NK9XSCLgxdnUcU,309
mypy/typeshed/stdlib/asyncio/coroutines.pyi,sha256=jMoqaq7rZQbBqpd3L2mPZ2ISHG_-FMI-5Na9xVku2MM,506
mypy/typeshed/stdlib/asyncio/events.pyi,sha256=Wmmjfy1TPaV2nChriVXHxZC6FS7iC95QmvAndyIh6to,18682
mypy/typeshed/stdlib/asyncio/exceptions.pyi,sha256=o_Xq6N7YA9Uw2sB3n15c2WLeyYNug4w2SFuwWmvhoWA,528
mypy/typeshed/stdlib/asyncio/format_helpers.pyi,sha256=4ycFFz5v621PZUCVmICBdeS4WGdHmeuir1PdBTy8qq0,893
mypy/typeshed/stdlib/asyncio/futures.pyi,sha256=21PPnidEukt8mk2gl8noD6hQDb1AJ0riFYn0N8Be0D8,2524
mypy/typeshed/stdlib/asyncio/locks.pyi,sha256=jSPrsGGcPxftL6qLZH3JKwWzeCb10jYnpX6Hp8IKWfQ,2758
mypy/typeshed/stdlib/asyncio/log.pyi,sha256=Ql97njxNKmNn76c8-vomSAM7P-V14o-17SOIgG47V-U,39
mypy/typeshed/stdlib/asyncio/proactor_events.pyi,sha256=gAzH9mf69aBfIOPROHVOgYlLpvhmUyK6qaz8u6ab4J0,3111
mypy/typeshed/stdlib/asyncio/protocols.pyi,sha256=cdSqexyRfNkl6moTCi2VCSt45bYm_oq-aDdHW8fRs3I,1138
mypy/typeshed/stdlib/asyncio/queues.pyi,sha256=J5278Q9rl3DVnjnnyJ65eqnAFK6XA87OXwRHst1mR_0,1153
mypy/typeshed/stdlib/asyncio/runners.pyi,sha256=zYfQQoW7siEy3d0ItNzQYKnf46oRuiDiQk-hs4asmLw,301
mypy/typeshed/stdlib/asyncio/selector_events.pyi,sha256=c-7XuzkfkaHFZ8jOmsJkkKKkVpnDZJTM5NVeEM830HI,184
mypy/typeshed/stdlib/asyncio/sslproto.pyi,sha256=Ia44Zrk-ArO2tGAVjYe5wIrPG2kNlqTtnUReZd_2VMs,5339
mypy/typeshed/stdlib/asyncio/staggered.pyi,sha256=PYGCE_fEoCcpV0GiBreITBbXe9fEu5fawG_P7RvUTkg,336
mypy/typeshed/stdlib/asyncio/streams.pyi,sha256=ttBNeBHg_91hP0mtZ1Z8jod9nHq_YJ20NziPolHm90Q,3916
mypy/typeshed/stdlib/asyncio/subprocess.pyi,sha256=OHnvtZv32j9O-nmtBF0hsMgjoNQEEObyf2Wa0eadGMs,5876
mypy/typeshed/stdlib/asyncio/tasks.pyi,sha256=0JJ2ol2hwT9Fbj-4m3brIwK1gTXItHCZ_tbjOk6Seq4,11597
mypy/typeshed/stdlib/asyncio/threads.pyi,sha256=xNqf8z1pbCIoeaO4w4YTzcuqOoGKbb1bJ7KjXpP7vWU,194
mypy/typeshed/stdlib/asyncio/transports.pyi,sha256=eLZ-D3EC6tCCZJcPsDP1gJ4iS4JvDHqchjxyTfgG_fk,1839
mypy/typeshed/stdlib/asyncio/trsock.pyi,sha256=S86euvlo4JygB2bkeW2c4-xdvwzxwu64a9FHYlMdBTM,4524
mypy/typeshed/stdlib/asyncio/unix_events.pyi,sha256=1tD7ZKk8z5I6rIqiPmaqltb9Akhoagz4Ul7YeodLa4s,2285
mypy/typeshed/stdlib/asyncio/windows_events.pyi,sha256=MnlVq1b99XAHpNrIdECJK-LOBNpHv78IU2fUjsX0lPc,3552
mypy/typeshed/stdlib/asyncio/windows_utils.pyi,sha256=IBlSXDAXjvfJgk17hFDYEaIvn4QygOvAShTCujTAf_4,984
mypy/typeshed/stdlib/asyncore.pyi,sha256=BqNh17l4gt4ub_LQMiAsTYzfmgSE7FWcLwLm8fVa1LU,3668
mypy/typeshed/stdlib/atexit.pyi,sha256=92pS0QbOUpAVPZYAUzJ8CsysZPXj0O3SucaGvHKKDTQ,383
mypy/typeshed/stdlib/audioop.pyi,sha256=ev5gpf-9_-tfb0sPOYTm_VveiHB9-FV819gDzO7USL0,2100
mypy/typeshed/stdlib/base64.pyi,sha256=K1GrdXAbgU8gPx7Z2tSoaVGpMWlROxPQQ-i73X9HzgU,1448
mypy/typeshed/stdlib/bdb.pyi,sha256=1uwnNjJqazo26Tz4FU7IZfzkC88c-Mxs9SfnTcA4Y0k,4459
mypy/typeshed/stdlib/binascii.pyi,sha256=QT37oien-uvhFzSu9HJECOhKV7h5lnS33THqAbJr3nw,1162
mypy/typeshed/stdlib/binhex.pyi,sha256=KPO4jyuFjgvRV1ryvanL0j0cIThoIoDisYOz3TBm_nw,1147
mypy/typeshed/stdlib/bisect.pyi,sha256=sQn9UUS0Cw5XZMEGcEj8Ka5VKPVobL43Pex_SagjXg8,67
mypy/typeshed/stdlib/builtins.pyi,sha256=t1wUcNjN-A8TS3Xl-5BkCvLaFmQJOYHDkOEEob5s62Q,66384
mypy/typeshed/stdlib/bz2.pyi,sha256=GGkEGx-6O1g7d2MqJhmu08WzkMdVW5ZSfzCv-d6Pyok,4455
mypy/typeshed/stdlib/cProfile.pyi,sha256=GPYaVZq6OEWa5AWq0H3eJjR_YQI85tZIzWdSPTcR8Pw,1394
mypy/typeshed/stdlib/calendar.pyi,sha256=qJ--kOGxRP8iXw6zDK2o0vyRlnDG9Pyg86ke3Ug3do8,5526
mypy/typeshed/stdlib/cgi.pyi,sha256=mzKVX19WoqLP0f8XY_lNUkkqMPyVWF8xwDQ6U9LvNGk,3685
mypy/typeshed/stdlib/cgitb.pyi,sha256=Q64H68Jt8wHn6acXsdkn_Hn_l28mAbKKycNGB3GocAQ,1412
mypy/typeshed/stdlib/chunk.pyi,sha256=juLgoUjtZJj3tExr1grorsVNUnVZtTgoJ8N9gj8opXs,613
mypy/typeshed/stdlib/cmath.pyi,sha256=uF2Ct4uuJRjSLJ00y_ytGOkMllGe-wVXnsp0g8iAIqY,1278
mypy/typeshed/stdlib/cmd.pyi,sha256=mOugiqxNSdn6JFVX3yDtoCF7VDpFF_TRXIzP4GSFpm8,1596
mypy/typeshed/stdlib/code.pyi,sha256=SPb0HFp2D47V1sw8isj8tXPZJLx-sED9opxdCui4wSE,1325
mypy/typeshed/stdlib/codecs.pyi,sha256=ISbf_etDRZockZI4dKkqnvNMQYfwA1Qug4TWqsuF-p0,11741
mypy/typeshed/stdlib/codeop.pyi,sha256=p8xGn0wBvQpLp_nqlAvBA_4_kC64B9iO9UJiUTpwBU0,455
mypy/typeshed/stdlib/collections/__init__.pyi,sha256=DkwMpT7mtG5rKi6QbPDLsC5X2JpjNh0z2aKgQTk7Ca8,14651
mypy/typeshed/stdlib/collections/abc.pyi,sha256=kBiZAN0VPf8qpkInbjqKZRRe0PXrZ7jxNmCGs4o5UOc,79
mypy/typeshed/stdlib/colorsys.pyi,sha256=ffGxm7Mzi52QsLEPjCt8h59VZ50HpXW49vd9KHJ2voU,552
mypy/typeshed/stdlib/compileall.pyi,sha256=sxK9MKzwcLe3k_JS-oFBygq4HowemPfXKYvty8tWbB4,3281
mypy/typeshed/stdlib/concurrent/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/concurrent/futures/__init__.pyi,sha256=ZyXXp9i057jbhjo8WeR9SYBT0dhFnwyZ-L1cJP4pQWg,629
mypy/typeshed/stdlib/concurrent/futures/_base.pyi,sha256=C3-gSIvV28XJhVXNwBuYvpb-XJKCWxflOyHMpXa_XcY,4731
mypy/typeshed/stdlib/concurrent/futures/process.pyi,sha256=JTtExRt_j1yMm7fAW8_7fvV1MN6EUgapEfU8Xrd6W88,5996
mypy/typeshed/stdlib/concurrent/futures/thread.pyi,sha256=NzQmir4DzTG9X1Alu8FIiXoZKjU6E0Bj9qvK3077_kk,2321
mypy/typeshed/stdlib/configparser.pyi,sha256=QbvjG5E4Jtyairgl9_k0RHxsEfvoGuTgEJqyuokVgrk,9870
mypy/typeshed/stdlib/contextlib.pyi,sha256=MVV3qQFbc7BIV1HJUeVY3bzH_Q5FL71M83U_HIYwPbA,5769
mypy/typeshed/stdlib/contextvars.pyi,sha256=YyJfewudeLC6NPzymlVW2-7y2OiIWwjQGe6q3IJVU1o,1661
mypy/typeshed/stdlib/copy.pyi,sha256=z9Rn-9wQqR2LIeAqGthqnMYHQ1KjdRmzSQkxCLF7Sbs,309
mypy/typeshed/stdlib/copyreg.pyi,sha256=TsuSQW2Z1bvp_Gp5gpWNk0wcra8jtYgzzbhdp_CdXYc,818
mypy/typeshed/stdlib/crypt.pyi,sha256=2AFM7UJOXWhqYaj3efBvwqfvtZw17iYI5TpJeSR7fIU,454
mypy/typeshed/stdlib/csv.pyi,sha256=VR-MPb9YvHH6lz_TPEAZdijnxg_zeEj7gtb0mLPiv9c,2915
mypy/typeshed/stdlib/ctypes/__init__.pyi,sha256=hjD60EeIQX4AqBmu939baV2YPmbCVDNkCjv51ngjYJk,11255
mypy/typeshed/stdlib/ctypes/util.pyi,sha256=Lf_nOeoLmPYs5DJ00UmxbAlTex7Z8bwByM3bSg4wg1w,129
mypy/typeshed/stdlib/ctypes/wintypes.pyi,sha256=4mLfzJ8kXytQo4DDsO5HX13sZWXUcs-XdwPygO6MOE0,4642
mypy/typeshed/stdlib/curses/__init__.pyi,sha256=e6zyQJFe4QJgc6mUK3ZqOqRTKGXq_QxJLXLs3vyVQHU,370
mypy/typeshed/stdlib/curses/ascii.pyi,sha256=0k1CT-7YSPh8S1xrCeQUNxXjc3ymwmQAIFG3Sqbp1RQ,1127
mypy/typeshed/stdlib/curses/panel.pyi,sha256=Wsl42xkXk8GQesNABDijIoBVX5Nx8dGm6prO1-gxlyU,801
mypy/typeshed/stdlib/curses/textpad.pyi,sha256=t84r8Htx35C4vIky8zoSME_DkyQseFa5Wn4ugh9zG1M,431
mypy/typeshed/stdlib/dataclasses.pyi,sha256=36pktjoYMAH9J-S_yRDDNhGRvN3_R3rkjIGvVuCQvS0,6410
mypy/typeshed/stdlib/datetime.pyi,sha256=8GYy8_Arlh3uc8AgmqhGOguYzMjbDHVmtWT2i6hhzgU,10563
mypy/typeshed/stdlib/dbm/__init__.pyi,sha256=-xPnL3D6FYVEHK3Q8rZBW6Rsy6GypIel7isV9HzyTOA,1715
mypy/typeshed/stdlib/dbm/dumb.pyi,sha256=mbE93udi-gzMFmabMF5YfbWGFfsH7_qTZxbXCrRboh4,1019
mypy/typeshed/stdlib/dbm/gnu.pyi,sha256=vtHOBfVWXL5UPrcoeehL04SE-SBz7NMtKxhAEMBjfws,1404
mypy/typeshed/stdlib/dbm/ndbm.pyi,sha256=oSGdmGpD7aT7SpI2vSe6x4rgkmqzrFNUyHVZtBuif5s,1223
mypy/typeshed/stdlib/decimal.pyi,sha256=F4hogGf7NAu9Q-t1TicxTHMGSUdSMBxjNXcX9SRyaEk,12925
mypy/typeshed/stdlib/difflib.pyi,sha256=ceu-LawU1OdyLJJ8wmSwW5jqwehrhkAk_GB-gKzxtME,3971
mypy/typeshed/stdlib/dis.pyi,sha256=TDjgfoMs6zeOQxf_YFgiAI9jhxciadpVHmFlJ1R0b_M,2327
mypy/typeshed/stdlib/distutils/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/distutils/archive_util.pyi,sha256=1ff4uoRWsrpVg5cYQi3crY_vlCot2vmbpa3ioIO7A7I,548
mypy/typeshed/stdlib/distutils/bcppcompiler.pyi,sha256=fge2cMbG4jp--o0I2zNcwykh24tJWZtk6leQgAH2NJw,78
mypy/typeshed/stdlib/distutils/ccompiler.pyi,sha256=wEXxIftdzshRF1tZC0F7kHk4N52BHZ_WgJl1ANwCfrs,6257
mypy/typeshed/stdlib/distutils/cmd.pyi,sha256=e4UnqqRyLt5ey5O8clpoD3E_MVsOD8V0coeyhnYyoc0,2726
mypy/typeshed/stdlib/distutils/command/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/distutils/command/bdist.pyi,sha256=AdLrhg0m_8Mj6qknF1ygbDdOUHC3EN5-yVdBsvK0iVk,540
mypy/typeshed/stdlib/distutils/command/bdist_dumb.pyi,sha256=8QnGTsOKkYvgBHnflEcmI4VD-CTLUZD68d-ZvZM-vMc,450
mypy/typeshed/stdlib/distutils/command/bdist_msi.pyi,sha256=Ym-C5gnLogJf9wkiehHzarT0n3829S7r0TVpzCkoczE,1487
mypy/typeshed/stdlib/distutils/command/bdist_packager.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/distutils/command/bdist_rpm.pyi,sha256=FddCkOBAFRN42HORJjs6QYs-TbECyhtmI1nIwbm58mM,1104
mypy/typeshed/stdlib/distutils/command/bdist_wininst.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/distutils/command/build.pyi,sha256=BBENKWeYOUx87wVwVreSFz_7pAE2PRDuxQApH7GyxP4,711
mypy/typeshed/stdlib/distutils/command/build_clib.pyi,sha256=SU1h7-fsfz_tsQMhvQh7ahYlfpq3tHhSmNjLO3Cuefg,668
mypy/typeshed/stdlib/distutils/command/build_ext.pyi,sha256=OSCfp1m8mpqPORFdqxzxjYvs1Dl32FZ3bBH-Wb2zXWE,1257
mypy/typeshed/stdlib/distutils/command/build_py.pyi,sha256=9LqrgBVHsoBUsJf_Ss-dvacJWRHSa4ZZStAnRSW5rDY,1444
mypy/typeshed/stdlib/distutils/command/build_scripts.pyi,sha256=tG65N9cNLUcejDyGrpqbazEXgRnv3LPkSnJp10rlvCk,574
mypy/typeshed/stdlib/distutils/command/check.pyi,sha256=-wIW24_6BIIiXpCuIBxVYgecjKHDrHnXjkWiL5Xs8mU,926
mypy/typeshed/stdlib/distutils/command/clean.pyi,sha256=voT57WZHfvCRRrAoZWwS8_RYcmBejf7HMIiE0omvDjo,377
mypy/typeshed/stdlib/distutils/command/config.pyi,sha256=UTjNhL7JLfvTORzwCVut9zJpCtSqdcjI2NJEAPzriag,2644
mypy/typeshed/stdlib/distutils/command/install.pyi,sha256=JlekYYAFgdDDeVrqnltDnmPyoTxQ1zixcJeRhHQanI0,1696
mypy/typeshed/stdlib/distutils/command/install_data.pyi,sha256=7icf2pxCX6WjLoXqQAzo0In50qVzQWhRmBUeVo4N6xM,436
mypy/typeshed/stdlib/distutils/command/install_egg_info.pyi,sha256=BfFJ1Su5HgAXYwpvq8_1cPWO2gkiLho12YzNSmLRnAA,490
mypy/typeshed/stdlib/distutils/command/install_headers.pyi,sha256=rT-1sQqlc8J2nqs_gf9RKmNBOorb5EAhC6qs8nUpfis,387
mypy/typeshed/stdlib/distutils/command/install_lib.pyi,sha256=tHZ-f7Ulj7Z-K6tolL4Mtpa4jD9OmeG7-Kv5UU8QDq4,598
mypy/typeshed/stdlib/distutils/command/install_scripts.pyi,sha256=okwcG_xRAomZj1zWLUHMhHoJpycTEMmgaI0ibbfNOeI,426
mypy/typeshed/stdlib/distutils/command/register.pyi,sha256=g3BtI8UnF_GS2j6zznNhbWdJvGoBACA8E3KuVp10Css,569
mypy/typeshed/stdlib/distutils/command/sdist.pyi,sha256=tJkV01XU2RKeqVvBWOtHk6d4kwPPFtoObcAX9Q0QHng,1118
mypy/typeshed/stdlib/distutils/command/upload.pyi,sha256=TsjsCOSOxh4S4M_SonwMai2DG68WTyPeuymzmqtszeY,462
mypy/typeshed/stdlib/distutils/config.pyi,sha256=Bmpm5-txSuUYd92XnDnfpAevSl9bk5YfXO-I_wXC2QI,497
mypy/typeshed/stdlib/distutils/core.pyi,sha256=XR8l38YoLvTOwIrvF5wkv9TI30u8sq2ivXzUEtWjsmk,1643
mypy/typeshed/stdlib/distutils/cygwinccompiler.pyi,sha256=Y7qhVOqXrkPT0yyQnudNCtTzBYC2lzS38HB5Mh45zEI,138
mypy/typeshed/stdlib/distutils/debug.pyi,sha256=MPYhYD0EJVkAAiL_6t7_Xfd2ctbMia2QEsy0D-HhLDY,19
mypy/typeshed/stdlib/distutils/dep_util.pyi,sha256=VlkTIegc0OT0zm8mu7OeOS7ZkbanjF2fS3y7HfDGV4w,220
mypy/typeshed/stdlib/distutils/dir_util.pyi,sha256=rekcu7OG7pq6ytOjVAHx7UFAHq7YbfjraauxGAFjm3E,530
mypy/typeshed/stdlib/distutils/dist.pyi,sha256=r_fUpXcjNboDShzwmNJzBoih2ts9K8OzR7Tj7ID5erw,2430
mypy/typeshed/stdlib/distutils/errors.pyi,sha256=l1W_FgoP9L-D-hEPFA2BzZuybjN0lV4WBXl0VJ-k7J8,852
mypy/typeshed/stdlib/distutils/extension.pyi,sha256=mKzya9jWqs1Io06oAHcjqgQWgjP4K6-XtTiAEl96PKg,779
mypy/typeshed/stdlib/distutils/fancy_getopt.pyi,sha256=nsF2ck91O1KuhXEfg1urFGrqj0bmk36WB27aXt8qFOA,903
mypy/typeshed/stdlib/distutils/file_util.pyi,sha256=-gB5ps7zIe-Z5nWJnyKY8nL7704vGGJ8Khp9mm0WTx0,419
mypy/typeshed/stdlib/distutils/filelist.pyi,sha256=p_jYwiywy4IRI_9mgeD7E2kY7H_agdiQ2KfhjXrhRnc,2158
mypy/typeshed/stdlib/distutils/log.pyi,sha256=CXViHmBVIadN4DWrSddT9lN1O1WaNjpxvaz-KUCD8C0,845
mypy/typeshed/stdlib/distutils/msvccompiler.pyi,sha256=qQLr26msfhjz-omJutWcRHik3shLh1CIt7CDI3jBd3I,78
mypy/typeshed/stdlib/distutils/spawn.pyi,sha256=9w33gVmcXOLRunbKpykMpL7QsTPqu_fcdBY71Uw8UO8,186
mypy/typeshed/stdlib/distutils/sysconfig.pyi,sha256=vYfZr4kgF872DDrMgvGWreEEe9usiQ6oC6qWz948SLo,550
mypy/typeshed/stdlib/distutils/text_file.pyi,sha256=w5ltlYSFNcMn7XGj4tNcau4nZENaRqCsmFozg4TTEQE,679
mypy/typeshed/stdlib/distutils/unixccompiler.pyi,sha256=R3VKldSfFPIPPIhygeq0KEphtTp0gxUzLoOHd0QoWW8,79
mypy/typeshed/stdlib/distutils/util.pyi,sha256=eK9lrRuP1LSrfTMK5vChYu0bfgDDI4OmscDpTtX3154,1509
mypy/typeshed/stdlib/distutils/version.pyi,sha256=n7J7bFXRifiv7I0wBxlUSIqKeB8TQee0Hs1CHkEBj4A,1352
mypy/typeshed/stdlib/doctest.pyi,sha256=s-7C0Ima1fZuX0kukvN_X9y10_A1qLQLc9344Sx8s30,6733
mypy/typeshed/stdlib/dummy_threading.pyi,sha256=ZI04ySfGgI8qdlogWtA8USUTFGfzm32t2ZxL5Ps53O8,79
mypy/typeshed/stdlib/email/__init__.pyi,sha256=5hwilWCw-1AiwWEMUfgVza8eTnH1vsqSL4Dkjx44w4c,757
mypy/typeshed/stdlib/email/_header_value_parser.pyi,sha256=uKERUATddytwIxW3O_EJ89zx4fXn1ru-k4lA_IfuSn0,11621
mypy/typeshed/stdlib/email/charset.pyi,sha256=VNDVOVU4D3ym7MEPzoMQ-RA39KYTkLK4UziegfvafhE,1025
mypy/typeshed/stdlib/email/contentmanager.pyi,sha256=1onMqjP_pwFFABKkXAJMpLssp2eCajYb_DmfdGW88dg,489
mypy/typeshed/stdlib/email/encoders.pyi,sha256=WIE0oEGqiDZZCnaaUganOj3RIHvNpdH1H6_uYsb9BCU,214
mypy/typeshed/stdlib/email/errors.pyi,sha256=LctV4c73sdvQ5YBWqunrLbpecNg_ypBxlvrvn36gUnw,1532
mypy/typeshed/stdlib/email/feedparser.pyi,sha256=-El1uWq32_69H0WtIpGT6eiEsyCQRcHuGUxKSlE7OdI,823
mypy/typeshed/stdlib/email/generator.pyi,sha256=59j1Jt4ZdZcCslZOJaOe2I3_tRcYgCJLzAPTCvg5OTE,1124
mypy/typeshed/stdlib/email/header.pyi,sha256=i-ap7tM64r4rMZgKlHCFIT3eF02Yl5Ye--wbErYzO_8,947
mypy/typeshed/stdlib/email/headerregistry.pyi,sha256=TIM2iwjNcZysLLAJT3f4DgaAe00oAT5cEPAPi2V_W9o,4425
mypy/typeshed/stdlib/email/iterators.pyi,sha256=0uXOP2moxvBXALg1IV5hhEqtltmX0CR4gLOmrO0dOZk,253
mypy/typeshed/stdlib/email/message.pyi,sha256=UdOEQQ8uE-1iTDy77Otum4dDPMRLiOe8nfEbWdFBSek,4863
mypy/typeshed/stdlib/email/mime/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/email/mime/application.pyi,sha256=_axeLUCYX8gDHMyFPG5-KfbXFUiIUNZCy3Ml0ZAW_iI,490
mypy/typeshed/stdlib/email/mime/audio.pyi,sha256=fglgMzpwv-rwwWyI38_YTlrzMJoEjbPFlr0g2QoxJXI,490
mypy/typeshed/stdlib/email/mime/base.pyi,sha256=LcHKigd9Vn7J1jGfosYDINcTLD5NW2j6IUD_-5KMRNs,322
mypy/typeshed/stdlib/email/mime/image.pyi,sha256=jO1y5OujU4sK011QdIM4ob4bUGDm9hOtKCZoOoaTWHs,490
mypy/typeshed/stdlib/email/mime/message.pyi,sha256=ar9OHOLDjN1wOSdZEkmHuqpNiieyyfc_o-fiOUUl8zc,261
mypy/typeshed/stdlib/email/mime/multipart.pyi,sha256=rsVmqWPB5_8KKNiqfOHW_863buPxG6kXGJ-CxYwEHSc,498
mypy/typeshed/stdlib/email/mime/nonmultipart.pyi,sha256=gAN-hOmjxz3lcG8FK3lbvlEOoz_yhG9rKIslq3WLil4,76
mypy/typeshed/stdlib/email/mime/text.pyi,sha256=aVkHKxjavgc9rHzo4Ou675SU6j05F3CE_Pf78EIgrrQ,249
mypy/typeshed/stdlib/email/parser.pyi,sha256=mDAoe7fCBjxzhKzhAMex9VWum8V9SFkDgytLE8wRp4E,1342
mypy/typeshed/stdlib/email/policy.pyi,sha256=iZvvg21RfWyU0V2YvrkS3uSeqAW-oSTQpgRsmyXjc6Y,2117
mypy/typeshed/stdlib/email/utils.pyi,sha256=E7s4sK1vID3-AGl9o7g7LweN6TfWPBXIpOZIZJGdsuI,1798
mypy/typeshed/stdlib/encodings/__init__.pyi,sha256=2D-QXTIRwIP6ZO83fnFP_7bqpaurM5QUncpijNC9IL8,306
mypy/typeshed/stdlib/encodings/utf_8.pyi,sha256=JC-NWt5975dTGZ_jFgyN9L1vDi1cBs9mG22sq03F8eI,825
mypy/typeshed/stdlib/ensurepip/__init__.pyi,sha256=FoVvAUG58THyHoGydJ6UqAjY_seVDTasfYEMP0Dt7ZY,221
mypy/typeshed/stdlib/enum.pyi,sha256=qS2vJwUYX2K7cyZm1OsWsjQLp4eIZXuC7EBsan0vK_8,3526
mypy/typeshed/stdlib/errno.pyi,sha256=KDjlJTkt1sdcrwGgLkPMZlSwz1Dp0Xkt6PqEnqcLZWY,2011
mypy/typeshed/stdlib/faulthandler.pyi,sha256=jn6gMdF0GEZljfFTc1splgef8zIo99X1H44qgWxU8sE,644
mypy/typeshed/stdlib/fcntl.pyi,sha256=1t21wjTcXR-CY84_LC4ftB0j4CPRqOazx9mSd9hjgHk,2167
mypy/typeshed/stdlib/filecmp.pyi,sha256=gSS55pIcNgetY2gI63BcRMR-eoHCVXI_yGKrKVveAkM,1800
mypy/typeshed/stdlib/fileinput.pyi,sha256=0bFzAqXHmRNUSyDAdXrIOdD3wGie4u-W4lHZv7Y5ZI4,3681
mypy/typeshed/stdlib/fnmatch.pyi,sha256=CBhAk0IbZY8XnTOCLXMzSg3RVJEm2NBodBaTT5ARtG4,251
mypy/typeshed/stdlib/formatter.pyi,sha256=hPFg5I_KSosDU-53JzOCKCw9oilS_4RoBTnp_XzIVdk,4572
mypy/typeshed/stdlib/fractions.pyi,sha256=lTMsJVDSZNJegI0UR9shOW1TYnd47i_6JP3gX9lkSNI,5182
mypy/typeshed/stdlib/ftplib.pyi,sha256=D6LiySREww6x_V0FIK6ua25WEb7KXpGc02I1g54Xllo,6070
mypy/typeshed/stdlib/functools.pyi,sha256=r9VpFPXQt-AZxA98atHjl7eYSlxtuuDBONPDmajfCIo,5290
mypy/typeshed/stdlib/gc.pyi,sha256=j1_JkfwrOg66vhkjBNnDBoFnpFGWqjUpTpLP_8Ey06s,1103
mypy/typeshed/stdlib/genericpath.pyi,sha256=uo-6KZhUSrX3zyUpX_jQ78so4FHNL6fGm6uCRj2ahiY,1417
mypy/typeshed/stdlib/getopt.pyi,sha256=CvA6kqMssIBslhRgrpKqCKNDzf4HhN8wnBllXyYbYEk,382
mypy/typeshed/stdlib/getpass.pyi,sha256=EBFBghUOq0KoxHM-9UHo2PprQx6L2yQ97ZPArsfHLX4,165
mypy/typeshed/stdlib/gettext.pyi,sha256=aHOnCU1JVGKYBlfrNXyRiMKV9pabCmE5hQU0poir6oA,3143
mypy/typeshed/stdlib/glob.pyi,sha256=6-6qHyIrpgnKLILbD__6_yklTRn3CNSye2gzTupiS3o,839
mypy/typeshed/stdlib/graphlib.pyi,sha256=Uuil0CiNznHzwYs8aUMUI5igEv5saCD4J-mLiLF6ZnM,579
mypy/typeshed/stdlib/grp.pyi,sha256=QPUGBA3EvQuJhKefJjzsobAgvv7k8tJbuiaYoba6UNk,275
mypy/typeshed/stdlib/gzip.pyi,sha256=UqbXdcYJHcqzLrLN-c7LdnSJbK9Nn7CVn2SH2vEEeIc,4737
mypy/typeshed/stdlib/hashlib.pyi,sha256=izweaLuR1hXIZZ4c6HN2c4lgZ0qJGBtl4mXoVEMy-gY,4216
mypy/typeshed/stdlib/heapq.pyi,sha256=jerv1xGGBVmxKa01kmmada5r2A2QNGK2mhvk8th_MVc,775
mypy/typeshed/stdlib/hmac.pyi,sha256=04QHyCL_kwkoCWM3LUzT3my8KXcoIAwUsL4vbecOukA,1358
mypy/typeshed/stdlib/html/__init__.pyi,sha256=qKsbjN2OJn_AoUr9a4zdFC8juUG9ii6rqYp3EQTSG-Q,122
mypy/typeshed/stdlib/html/entities.pyi,sha256=Vx5z1IJs3qkJUV1Vp8ZbX-bCAZNbpeCZncj4u7H1bWc,111
mypy/typeshed/stdlib/html/parser.pyi,sha256=WWfn3V4Q3Min8DXhsUXeCW8tBwfkxHftxAei8VlryB4,1620
mypy/typeshed/stdlib/http/__init__.pyi,sha256=bE3i99Lkeat-Seo0TTAm-dDauYTTNtih512Tt6ij9G8,1940
mypy/typeshed/stdlib/http/client.pyi,sha256=tWerDVnIkK8IS-zOw3DCQcU6t52cQg1vtp0jmGxpX_U,7150
mypy/typeshed/stdlib/http/cookiejar.pyi,sha256=uK8YPTlxsEDhUDL1M4cdgSuawUiDR_qUU1qUuwGlEhY,7350
mypy/typeshed/stdlib/http/cookies.pyi,sha256=3FmN2oH5t74g4zRwNBGuuUebFrM4K4NLT-X1-1w3jL0,2053
mypy/typeshed/stdlib/http/server.pyi,sha256=_xACdjVRCnx8cxxzDyEfSLgG7wv3rva34YaPilTBN-0,3590
mypy/typeshed/stdlib/imaplib.pyi,sha256=usYcWytUgU5M0qOnH9lEKI2vNH8MWrkeyYF9T-uwn_I,7517
mypy/typeshed/stdlib/imghdr.pyi,sha256=_Fk1rP8rcifFLvNn8Dx6h2LUj8D-J3A4jLK8KCtsXdQ,453
mypy/typeshed/stdlib/imp.pyi,sha256=_VIqxYJPcijABpMQKBmxz6oFSlsTQ3K4HMjK9iu6eBE,2299
mypy/typeshed/stdlib/importlib/__init__.pyi,sha256=_45EcpyGptqjv_TUEts4xkQrBLxRAMBTrAAP0iqrzTk,717
mypy/typeshed/stdlib/importlib/abc.pyi,sha256=Aoubzhm1Rq_P-TgTtFmP5gkuFJzpbAH0QpxcWGAoHCo,6572
mypy/typeshed/stdlib/importlib/machinery.pyi,sha256=yQy0TYKwVQUETXnNMQnJE_TsFJIAFjXKCN-BbEPArtw,4618
mypy/typeshed/stdlib/importlib/metadata.pyi,sha256=8FssyGyJkk9_p0AInHmR3aiB-c7Js3pfAj5TbeZ8Rd8,3766
mypy/typeshed/stdlib/importlib/resources.pyi,sha256=4w700Yxgwh9VZ0CF4tt8yLxxKopLdDtqYP24GzYTd3o,1168
mypy/typeshed/stdlib/importlib/util.pyi,sha256=E2IrayBbEMEMF1NRhJ1e254sRVdm93L2z23iOz5_W0c,1797
mypy/typeshed/stdlib/inspect.pyi,sha256=qiKWJ0DaQgLQwdDap8La120SfoRIDbnsNjzNho3DO08,12793
mypy/typeshed/stdlib/io.pyi,sha256=R0nfE013VSfEpIB1EYAzV0m6GrScWOd9ISJ6V-Wjgfs,7313
mypy/typeshed/stdlib/ipaddress.pyi,sha256=pDE2dTRzCLHZ6VwgYNg8RzBbKIW_rVqU9K0gAuhszww,5202
mypy/typeshed/stdlib/itertools.pyi,sha256=A8wHDoMPB1dTowMS1UtAq7DGVc8QjjJgZ5ZUIVcD7x0,7846
mypy/typeshed/stdlib/json/__init__.pyi,sha256=eYuXLQ5djJvoOncz5Odob7CAwfvOdGvdFACXUusWKrw,1865
mypy/typeshed/stdlib/json/decoder.pyi,sha256=lZmkkIhhgjFjH6Jd6FDziHcS5sHPjDFvrS-dJtrxa1Q,1040
mypy/typeshed/stdlib/json/encoder.pyi,sha256=lUEpHusliNBrmjg4YJl_7WEyv4m1Gyapc5d9L_6vuRU,882
mypy/typeshed/stdlib/json/tool.pyi,sha256=d4f22QGwpb1ZtDk-1Sn72ftvo4incC5E2JAikmjzfJI,24
mypy/typeshed/stdlib/keyword.pyi,sha256=QWSfvjFoFizINm-VW9w_ztmJB_FngXpsw6mdtAKkn9g,203
mypy/typeshed/stdlib/lib2to3/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/lib2to3/pgen2/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/lib2to3/pgen2/driver.pyi,sha256=eZk0aRx4AnIfCWnr6JBOh64Tc86a_uvzZz6RLKPrvDU,919
mypy/typeshed/stdlib/lib2to3/pgen2/grammar.pyi,sha256=4ZbEq44FxmC166ZaHD8x6p0Fjp-tPKvr3Q2G-3fayy8,719
mypy/typeshed/stdlib/lib2to3/pgen2/literals.pyi,sha256=0DInLrE0ANPoF_0imzG3kNu36HgPXkma-nBCxUw8iCw,155
mypy/typeshed/stdlib/lib2to3/pgen2/parse.pyi,sha256=rfMDkvhVEMCK630xdk91BuqddaitOQAuPif_qmacng4,1041
mypy/typeshed/stdlib/lib2to3/pgen2/pgen.pyi,sha256=SFVJ3D6fY6b57C9UfqfZtOffGEyRAK3REc5SPmO-Dks,2077
mypy/typeshed/stdlib/lib2to3/pgen2/token.pyi,sha256=P06jKUktDqCIA4t7vJPCGu3lRSYnUPDWemMdUYrlyV4,942
mypy/typeshed/stdlib/lib2to3/pgen2/tokenize.pyi,sha256=mi3VpA7IZzl3KRw2mce_kUxpAN_weyvVMfGPDLlzcOQ,861
mypy/typeshed/stdlib/lib2to3/pygram.pyi,sha256=lyhgf6kpNVMPN6g6W-DSxt8E2_IyVguf7JjKzVhAmzc,2208
mypy/typeshed/stdlib/lib2to3/pytree.pyi,sha256=ltVmPVDay8wdGHBcOrjb1YE38TqpPQFsJFbo7GC7k6g,3043
mypy/typeshed/stdlib/lib2to3/refactor.pyi,sha256=uM1Q_-G0L6apIPpwe2jqchMZvfXWejEw12ozImGynHk,3216
mypy/typeshed/stdlib/linecache.pyi,sha256=dQiCoM_SF8dThmVXCxXW41Tp-WbswStKk5iqlyu3mlU,721
mypy/typeshed/stdlib/locale.pyi,sha256=nVLuhzfMmMzG5ppAs1C978WfdyHP5Ccer4StD89zjRo,2493
mypy/typeshed/stdlib/logging/__init__.pyi,sha256=8Vb31UUikx7mHYwB-yGs80A6MPa2ioJ11lgyrq5SooI,24873
mypy/typeshed/stdlib/logging/config.pyi,sha256=Fw2DAcaGnJfW8MCsDL0WNyJMw_H2iM1hJUHkVXbdx4E,2298
mypy/typeshed/stdlib/logging/handlers.pyi,sha256=NBRte9zJB5Or3e91uDZY3fTwEwpZ7x341LXDE5ixujU,9748
mypy/typeshed/stdlib/lzma.pyi,sha256=ohdohB08oLG9MmgwKvoP0DD5SJNNO-DIQJE-3NaOyjs,4474
mypy/typeshed/stdlib/macpath.pyi,sha256=-zxf5B4vQj-qbf9lTsl3i7TqvN5FqfjxT1bqXuo9CA4,1889
mypy/typeshed/stdlib/macurl2path.pyi,sha256=AX2ReRSdgi3Mw_8zm0RXsnGeunJ4Ke2bfSwFkXTC4p8,140
mypy/typeshed/stdlib/mailbox.pyi,sha256=X0OMmuG3hCooIWMhEI98SZQYbDPJTsqTF380g_xaBrU,8066
mypy/typeshed/stdlib/mailcap.pyi,sha256=6yKgSgZ1Fqf6K_3aBQ6gx1eRrb511o5lY1YlT7TTOyM,301
mypy/typeshed/stdlib/marshal.pyi,sha256=zXZaX_H3VD9clK-iiNZz7f5GKDdG4_TriqlTPCBR6oM,253
mypy/typeshed/stdlib/math.pyi,sha256=4wy37AyZ1caYFReVrppwDC1jZxEdJQjnz4i9clEwm3E,4353
mypy/typeshed/stdlib/mimetypes.pyi,sha256=RP9K0WGXmm2QIMuTDHXYSSADGXR5jVXfiIsUcTVzG8M,1554
mypy/typeshed/stdlib/mmap.pyi,sha256=yh5F32Kc5g3O9l4Wo7CaNbnfpynY-dsp-qk7C0jDaUA,3602
mypy/typeshed/stdlib/modulefinder.pyi,sha256=GCeC_NtcruWYsR4f5RnLEv-Uz7dMSybjQi7eoR16Ipk,3565
mypy/typeshed/stdlib/msilib/__init__.pyi,sha256=q4wFJTq7pY0dpwgvMBGBqVkOUGPHvLhT2QBjitYZsuQ,5927
mypy/typeshed/stdlib/msilib/schema.pyi,sha256=hRHjm9DavaKkp9xDvvtbMaYjuRkOaPouAiUp9YGvPHU,2141
mypy/typeshed/stdlib/msilib/sequence.pyi,sha256=Pq5ddslRWZPAc2ukv8lAYs8XUawgVbcejgd7fcnuuZU,356
mypy/typeshed/stdlib/msilib/text.pyi,sha256=x-zzqJqTLplVuFS1So0hQKd7mktGzDVH2D7cxdku1w0,155
mypy/typeshed/stdlib/msvcrt.pyi,sha256=MBrjMpwb7WkMhaUk8IkHw-Y59OJ9di8CsVaLPGuH4f0,767
mypy/typeshed/stdlib/multiprocessing/__init__.pyi,sha256=AaYpWuqsQ1sQAyKs5ERA-FfS1z3KZ2GxBdIkObSLvLM,4230
mypy/typeshed/stdlib/multiprocessing/connection.pyi,sha256=T9QT-rQN2m_kSMm3w1ddDckXu0PsPKG1TOWDFXvRRcY,2552
mypy/typeshed/stdlib/multiprocessing/context.pyi,sha256=nEpWnp6bxWs0DDA3Blq-6zhXfe3ANRCk9DZn0v7rOI4,7292
mypy/typeshed/stdlib/multiprocessing/dummy/__init__.pyi,sha256=ILX1ArjL54B25LzJAGE16ZFTTm-Aqpl7kpR1n4Qr7No,1535
mypy/typeshed/stdlib/multiprocessing/dummy/connection.pyi,sha256=a7tRD2Fp2pFS-dQ_5zh7e2iMyNiF7leCVD_sJEe_8CE,1267
mypy/typeshed/stdlib/multiprocessing/managers.pyi,sha256=bMz8k8gZDMHdCgsHLFFrrqS6n5e6pWDZcf-KNnC9Ubg,4618
mypy/typeshed/stdlib/multiprocessing/pool.pyi,sha256=om1HCsEmjflxwJo-NIzumHWkxjKQS0b353Vj_xZoCTg,4500
mypy/typeshed/stdlib/multiprocessing/process.pyi,sha256=603FalTyMiHgCJLSE3nW4tD1adn8WmpxJzfwSlFJ49s,1150
mypy/typeshed/stdlib/multiprocessing/queues.pyi,sha256=4muTsmxuUSCoyNLUrfZ642JfPbFb_t-iLxaXAFF7e-s,1344
mypy/typeshed/stdlib/multiprocessing/shared_memory.pyi,sha256=hvvDEq_YlvP7MUK9sEAoe8klqmyN3PEW9VUaeY0-94k,1283
mypy/typeshed/stdlib/multiprocessing/sharedctypes.pyi,sha256=fnUU8PfJ5haLW2EocxY-t-pASZ9OxgnY-z8hrxhjYKQ,3754
mypy/typeshed/stdlib/multiprocessing/spawn.pyi,sha256=YA4qqR096tUqaILrx7fLVbOYCye5omYXwqLj2u-w3WY,662
mypy/typeshed/stdlib/multiprocessing/synchronize.pyi,sha256=7DS74fClmOjChvSvdoQIQFVEdV8fdH6VbBgW9Fj1tBc,1762
mypy/typeshed/stdlib/netrc.pyi,sha256=ETUsJZB0_cNvWXudFWq_VJSAkr3iE92N1pY7X5twucI,589
mypy/typeshed/stdlib/nis.pyi,sha256=_PX4Hv3cRAYtnylJuj5xycYHt9R9ebgMugjH_fo7iEk,292
mypy/typeshed/stdlib/nntplib.pyi,sha256=Vgdp19paLrYc4DosWQ8jg1rhir2AJnl0fonRowaSRvA,4256
mypy/typeshed/stdlib/ntpath.pyi,sha256=fpGOBWuCf4IIknesrK9eUvua-hEjfDv_YJ6PfH2T4y0,1929
mypy/typeshed/stdlib/nturl2path.pyi,sha256=E4_g6cF1KbaY3WxuH-K0-fdoY_Awea4D2Q0hQCFf3pQ,76
mypy/typeshed/stdlib/numbers.pyi,sha256=N4XvzsxIm3oQicN24MH3iUFfRsrTkUJ3CyV2fUXJEGc,3784
mypy/typeshed/stdlib/opcode.pyi,sha256=Rukocuf8AtWrWNgxPDwcbKYkrGMQFYsg1C3ERBljQwM,516
mypy/typeshed/stdlib/operator.pyi,sha256=r2o6H3qYUgZQ8MAseYTfL60vDX-nWliltJXN2tUJY_M,6750
mypy/typeshed/stdlib/optparse.pyi,sha256=gYe4mph2LC2bVX0wHjaZgjcuBTERoYatFSelEoGjs3o,9638
mypy/typeshed/stdlib/os/__init__.pyi,sha256=WcS6HuAD8zfUlAWzrG0wL_iLpVamzlMYdQgQPeKpM6c,30557
mypy/typeshed/stdlib/os/path.pyi,sha256=mrTaREnQH5vSttIo26tiiF--4136y9AhvzntR71xSDU,99
mypy/typeshed/stdlib/ossaudiodev.pyi,sha256=S7sqPEWiSn8FWrWVOEnd9Etmx2b6FEwzDUXUqqzvy-k,3059
mypy/typeshed/stdlib/parser.pyi,sha256=p21AJ5tU5uGEtysCOA4shQVXNDcO7AbmdndzWnM6R3w,983
mypy/typeshed/stdlib/pathlib.pyi,sha256=EMEykYeBo87mXNo0EGa6AjdFCQrN0VS8FZAOSseZGjw,7150
mypy/typeshed/stdlib/pdb.pyi,sha256=9eW7SWsljm3obvBmdmu9FJF44lUJvugzR82orzSAJSo,7194
mypy/typeshed/stdlib/pickle.pyi,sha256=ltD3BM0qkSvtPYnV0T9E9PCm3QALGPRAiI-FIa9fjTY,5363
mypy/typeshed/stdlib/pickletools.pyi,sha256=Wgfbt3SMN0GT0LQbQPxt-4ROwhYOSVT78S1Qv-B2tAY,3695
mypy/typeshed/stdlib/pipes.pyi,sha256=gk5tg9Oqd-V6bQ0OaqCuFTDMMwyysCQ1AMMK1U690qI,514
mypy/typeshed/stdlib/pkgutil.pyi,sha256=pXJu8Y-sI3xDzO9LHQ2J0uakrFtjAe8fUCMnwJ_q2jw,1283
mypy/typeshed/stdlib/platform.pyi,sha256=t9z34_BMdWjXIeuSQjOC3MDYSr4U6_mXFX2WkPjZoRI,2245
mypy/typeshed/stdlib/plistlib.pyi,sha256=1s2__EAjDbDKsiwENmMOTAmJwcc_hCIbjPrIkxsBUXw,2393
mypy/typeshed/stdlib/poplib.pyi,sha256=_D9hsNvuSDidZuBQ4joGvtaL-Aw6T5DEaGRfPanknxY,1959
mypy/typeshed/stdlib/posix.pyi,sha256=JzF-99nbR99BuPyqBQtfYW4xF-Ja2Sca7imUZAZJTqM,4031
mypy/typeshed/stdlib/posixpath.pyi,sha256=lyMG9V577yU-zK8CbNGH3nzHgZj9aKx184l5geyNNDE,2849
mypy/typeshed/stdlib/pprint.pyi,sha256=yAwgRN6Scn9GqbvOmxepXJfCp3M_OOZ2utBp1ngTiis,3607
mypy/typeshed/stdlib/profile.pyi,sha256=zVfDOskuTRiBllWBBOUwgPL3p15mrkRi0ymgQJY6dO8,1305
mypy/typeshed/stdlib/pstats.pyi,sha256=LCdwMJni1FD3zLqyi0SdDVFRULLAS7hm-ApM9A4lGrQ,2148
mypy/typeshed/stdlib/pty.pyi,sha256=OGiV1eGisH7Ovy0H2AbZpGxQRprLadPV_ojbgZZD07k,403
mypy/typeshed/stdlib/pwd.pyi,sha256=RPorKkKi0TrMZ9RyKfOfMqdsVMsxczBHDTFW2EXaYt8,460
mypy/typeshed/stdlib/py_compile.pyi,sha256=ITCew02nP5__SGbyJQEDz-2M_HQot2K0LLMXK-1Nh64,1343
mypy/typeshed/stdlib/pyclbr.pyi,sha256=T-mqX9rJP4y8bctYB17Ms1kNWYq8vujwbDh3soRVe6g,1046
mypy/typeshed/stdlib/pydoc.pyi,sha256=5JwbJdMcjyKRvBu2rJWj77BCzGXoGOxTje58ey2_JPQ,10054
mypy/typeshed/stdlib/pydoc_data/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/pydoc_data/topics.pyi,sha256=e6t5ek6ktCuHqVHsBY_gFm5Lzj4BupyBch13u0t2JVc,23
mypy/typeshed/stdlib/pyexpat/__init__.pyi,sha256=Wf6s6FvhKKLB_fcv_ZTMLNLkvfWbMVXyW6OnkIxYDC8,3235
mypy/typeshed/stdlib/pyexpat/errors.pyi,sha256=nJ_fokvIjdkb0XhNkHiUK0hTIRf-heLEaeYfES5Pmxc,1200
mypy/typeshed/stdlib/pyexpat/model.pyi,sha256=LlBMaLvt3TBD1JQSAQTYUrHKAfKNEeQYaDw5GsJA--g,205
mypy/typeshed/stdlib/queue.pyi,sha256=H-2ddPNW_h9EOykCaJwpf5L2s_SoPjTWvazRcsJUx3k,1862
mypy/typeshed/stdlib/quopri.pyi,sha256=0zH--hEadPU_f_GNqwlxU8vrs2tqmPaLCO94BeZXCyo,343
mypy/typeshed/stdlib/random.pyi,sha256=n5idXr4gurMWRsceaRMia6VhpJ2nxjNXOEQfqraZ3JU,3999
mypy/typeshed/stdlib/re.pyi,sha256=peckkRpZ6QvL-mbQUum1U-fAZVxEt5NW9Dn3nzWPwH8,4319
mypy/typeshed/stdlib/readline.pyi,sha256=08SHT78IdI8zqTAzlp3j8_Zx_5jOkWXXmeOh9xXyBE8,1641
mypy/typeshed/stdlib/reprlib.pyi,sha256=bmh0EB6JH4wqoDNXRmTlB6mEm9MbkhFUfxU-QAHBxkw,1223
mypy/typeshed/stdlib/resource.pyi,sha256=Sl9o9uiZlQqycDin5grzLoaRRSuTyyp5mQVAIXQKyi8,1439
mypy/typeshed/stdlib/rlcompleter.pyi,sha256=K9JK_gvYMTZWBeAvmpAIrcAVuZXaTaLtWGRIu1kReds,182
mypy/typeshed/stdlib/runpy.pyi,sha256=0yXR9db0aO9xJ429RS2hXi1yTGXhVIdf1ar7R9vImDU,730
mypy/typeshed/stdlib/sched.pyi,sha256=A1Q8ljQHpPww1GdkjCW0PhI61dE49jJO7mBVka0hIio,949
mypy/typeshed/stdlib/secrets.pyi,sha256=0iMn-z8QeJshAPQjuPQHAV1mOd5eGHpGKUu9IVILslk,448
mypy/typeshed/stdlib/select.pyi,sha256=zRTWIKQQhiiG7x_Ihn8_wfW5jPIFGCN3NeTMdIKx_rA,4401
mypy/typeshed/stdlib/selectors.pyi,sha256=CTvRmHYgbI35cqlVMXd7XoUZesVJw1mRAcSlv6gAA9c,3633
mypy/typeshed/stdlib/shelve.pyi,sha256=oUW7LKwCKEsB952kzmHkvfNLGMRhbQlVkhO0kJy7xEs,1560
mypy/typeshed/stdlib/shlex.pyi,sha256=Rjbe6Arm91mVc62KfHMgiFFxMUf_Llk9bPcbilRL7jA,1300
mypy/typeshed/stdlib/shutil.pyi,sha256=lOEeTYecj_QlSfSC97adXHmpjO14BC6psJUBDlriSPA,4275
mypy/typeshed/stdlib/signal.pyi,sha256=oZHuS221cmlAdubPhhBIut8X7pxNleuWKHT5Yq796to,5274
mypy/typeshed/stdlib/site.pyi,sha256=saJFyRrUu3W-oqnzVZtWnbwiAqDrvHtBLxBXeGMqO7U,1346
mypy/typeshed/stdlib/smtpd.pyi,sha256=4z0LuhsaMKh_27hHzLm2y3tL8tcu60JKB6qqVrvBiG8,2681
mypy/typeshed/stdlib/smtplib.pyi,sha256=E-I9jqDL20eKPq5XBjVs0XOrpj4ywLhEam5uoocVtN0,5392
mypy/typeshed/stdlib/sndhdr.pyi,sha256=3yGaM6-3mv4VfZn23CHfQY2GcRMvMpRnAX9N_Kk7NNw,322
mypy/typeshed/stdlib/socket.pyi,sha256=n0ppzyUTXeA8COiQAXuZyBoy2nHW-CORAFVeCXwHVBI,21525
mypy/typeshed/stdlib/socketserver.pyi,sha256=ZviXoQepmLJCTt6f7GJ8z4iAhE_vpHItRrhDCD1setI,6556
mypy/typeshed/stdlib/spwd.pyi,sha256=2KNiq3TrCvUrfeVkE4oIlMBDWpCVsl1KvNc54Z3TQ6s,305
mypy/typeshed/stdlib/sqlite3/__init__.pyi,sha256=aJu9MCNl8y9HCykdUpo1Z-LSiP3mxRSxrWkCsMxemYI,43
mypy/typeshed/stdlib/sqlite3/dbapi2.pyi,sha256=bePFjdXa_i8MQY1xaLLiTrL_ibWklN61NBrTbMzMkAM,8602
mypy/typeshed/stdlib/sre_compile.pyi,sha256=ourRyTFTKeGLJvQ_kJza1z0KnRjb0NoR5bmanXYGsLg,803
mypy/typeshed/stdlib/sre_constants.pyi,sha256=ug4LpnfmXbKkGxMds2hL-hlm69At51wWqAaIjiyOQeo,3407
mypy/typeshed/stdlib/sre_parse.pyi,sha256=SWUsnrImy8YBhfnNj8yFua7MO--zBQK7Hd8AhKKsp08,3740
mypy/typeshed/stdlib/ssl.pyi,sha256=2An5KL6MlQarsSJb8cMd4d4GHcaeGileGO1Hq9MMeic,17948
mypy/typeshed/stdlib/stat.pyi,sha256=pI_H3c_qcgI0dBHwzvBAePU98ZwVIenNi_KSgTg8MMM,1953
mypy/typeshed/stdlib/statistics.pyi,sha256=6Gvp1GpEozYnsaHQjCCrqW0P_jvjP357eHpYF-nQ3G0,3746
mypy/typeshed/stdlib/string.pyi,sha256=7u5AjZPiGRGvZaXlJJTTuEXBgqvFmbEJZbWCsWkqFTM,1623
mypy/typeshed/stdlib/stringprep.pyi,sha256=N1KenXZOlrlZA_U26UJV4nbZFbTmYU8hr-xa1vFujC0,771
mypy/typeshed/stdlib/struct.pyi,sha256=GBKUpBBvCLKEIGSqrbwjCa-dpp6OkOksRGydlYdel4Q,1163
mypy/typeshed/stdlib/subprocess.pyi,sha256=NfOkNRnRDFgUItIhXNi_ghss3Uc8ZMMiwXKMBfY6kpc,34545
mypy/typeshed/stdlib/sunau.pyi,sha256=pe66DYWvRBL388095LRx5i8iI7AulMeAY5PBQjBikbY,2715
mypy/typeshed/stdlib/symbol.pyi,sha256=b-6r19I9m-Tjh23ydPwXfUsqWb1XOVt4E54q9vwOJ9c,1358
mypy/typeshed/stdlib/symtable.pyi,sha256=FDzXz6N84sEoa22Od5vY7U5ZrNuq4P-mcyc0UMVwJeM,2106
mypy/typeshed/stdlib/sys.pyi,sha256=JkcIS77scY_uA-Zm2E6kmRp_tq_Gte869KHBw8VrXGc,7048
mypy/typeshed/stdlib/sysconfig.pyi,sha256=GWx9XZi8iqMahum9Qod9-fjLVWG8OtM-ipdhaz2jTVM,827
mypy/typeshed/stdlib/syslog.pyi,sha256=WbINKcwKTg5WFsn4f06K3-jriyoYXzqnOU9HwbAFbZY,821
mypy/typeshed/stdlib/tabnanny.pyi,sha256=65BJE-plKsoruZnNKis0xJxyZOtTzW2-T30-mRTTocE,454
mypy/typeshed/stdlib/tarfile.pyi,sha256=r2cs6exAIWxxgBHZ-65ktiuYoug5oJ9Oar96Hr269OU,12343
mypy/typeshed/stdlib/telnetlib.pyi,sha256=1nzq9RGlbWaf9yLWfeo-f2NoPGKJ5-0B40yw2woxLPg,2657
mypy/typeshed/stdlib/tempfile.pyi,sha256=hvNp3EcT2JHXg8DNtaO6JOZQZ3efvNtjjNWp6V5W2Yc,13941
mypy/typeshed/stdlib/termios.pyi,sha256=KSUOE_2JuoIlv1RFyZcW1Uwfx95Dfv-p5HatapvwdqM,3526
mypy/typeshed/stdlib/textwrap.pyi,sha256=mFCfBdnwCjYNGIRY7qDBt1nLYo6W_YWwmRVgnbgZ-bo,3089
mypy/typeshed/stdlib/this.pyi,sha256=qeiwAiqbPK8iEcH4W--jUM_ickhZFNnx8cEvTqVPvCY,25
mypy/typeshed/stdlib/threading.pyi,sha256=wHCbsib-GYbvIT9sL-DpkTtFTo3N2-xeZ69UxLKeNZM,4888
mypy/typeshed/stdlib/time.pyi,sha256=tM3AsENmWhDEFzRKPVAtZGM7M5BZF9Dgfwi_02z_Lbc,3410
mypy/typeshed/stdlib/timeit.pyi,sha256=9LmXsURlSWDSdOSn5cP7AxtJW58xIa3mfFgOQVFiuXI,1106
mypy/typeshed/stdlib/tkinter/__init__.pyi,sha256=P1ndH_hmvxWHWhgxEyNU5WPSEe4-UfQ6ALSctmPw6zQ,127686
mypy/typeshed/stdlib/tkinter/colorchooser.pyi,sha256=jIe4kP5dWAlu0UJXoH8TdUYy71_zizIjdnfcAKdrfHU,254
mypy/typeshed/stdlib/tkinter/commondialog.pyi,sha256=UbKDE-dDIPx4mNwQDzT0jjH-cg7jHJAY4UgjHOQbKtg,260
mypy/typeshed/stdlib/tkinter/constants.pyi,sha256=WfBSeKTB52NfPVFdsdaRrnsmDOvT3FoP8j4QK1T3yxU,1886
mypy/typeshed/stdlib/tkinter/dialog.pyi,sha256=_sjux0ZqFe84IqnCJN-pucJ7l724kO_3NaMkolfSAHE,266
mypy/typeshed/stdlib/tkinter/dnd.pyi,sha256=OZUJoZY9UYWhoxCLn2mMuS2ubxaWQ-Nt-8bEFzfXG2w,639
mypy/typeshed/stdlib/tkinter/filedialog.pyi,sha256=q0FhPYr2YOVowMkJ7JdG0yZb4IKP0Pnv2W1WF3hVrcg,4728
mypy/typeshed/stdlib/tkinter/font.pyi,sha256=FvU-P0tfkqXZqSwLc1P_5fQTVC5Qt2cxv3MeaMMLQT4,3875
mypy/typeshed/stdlib/tkinter/messagebox.pyi,sha256=NXw5AATWb1Oln9tVSbJyWGJUibBetEq72wEisGzBShE,1103
mypy/typeshed/stdlib/tkinter/scrolledtext.pyi,sha256=NgoSpqeFdXP7FUGc9OO3an9AdJj3sfUfCeferzScEvg,301
mypy/typeshed/stdlib/tkinter/simpledialog.pyi,sha256=Z4__ksER07zkcafrBhpey9hSbyCJ__SLXp_XsQCJsG0,941
mypy/typeshed/stdlib/tkinter/tix.pyi,sha256=ijR5J3U97cK5k_Tej87uEnpixynpON5dRZaUCCat1F8,14742
mypy/typeshed/stdlib/tkinter/ttk.pyi,sha256=Dh7fFzqu8EqJOmXdcaXIf2IYWUOSDZUCTXgsfVUbMOU,43520
mypy/typeshed/stdlib/token.pyi,sha256=5UcnKiRziw4j85eJHncUmwCRjyQCkTGaR_NbDtrWbvk,1278
mypy/typeshed/stdlib/tokenize.pyi,sha256=deLrep3zMW9m1YddzAQPcPTC5Sxf49IWQff8txRV-Wo,3009
mypy/typeshed/stdlib/trace.pyi,sha256=ghcuFbnt_tma5Ny246EIcrZQyduWO-gwihMvciIXe8k,2710
mypy/typeshed/stdlib/traceback.pyi,sha256=1lqQ6AqtHo9FLQdh7n0Np3s-1DdJgUTNe5SKoRooO8A,5485
mypy/typeshed/stdlib/tracemalloc.pyi,sha256=MFkFf0wPIDBbudkQTQY7o_NmR72t2AWCmX-b7pjqwHA,2714
mypy/typeshed/stdlib/tty.pyi,sha256=A25_a1yrTL55nQAsEpOWKsni215-75a4rAoFJ7g7Qr0,275
mypy/typeshed/stdlib/turtle.pyi,sha256=gHjMh5ryCvVDzwJemj9_VJX7vq-1OO5eCyk11jDtvkY,18793
mypy/typeshed/stdlib/types.pyi,sha256=41-nw9BfkURdmO6r-CUzIifnmvujBHiaF31_RWHdYlM,13605
mypy/typeshed/stdlib/typing.pyi,sha256=b-iytLrVP9YPKhlId864IrNCpA0nMIte4224RY4er14,26320
mypy/typeshed/stdlib/typing_extensions.pyi,sha256=KbrmHOa-MLNfJ_vwCSz3B48lzNRYEMEqjMyo07EPFXY,3741
mypy/typeshed/stdlib/unicodedata.pyi,sha256=DKDMTPdhIR82sUuxG87MG97NO3ze9dP8CyOSWIcWv1M,1685
mypy/typeshed/stdlib/unittest/__init__.pyi,sha256=3DkoXgwfSLRfxiU-bwm4_JA4j64_5OTd46M02Wp6f6M,658
mypy/typeshed/stdlib/unittest/async_case.pyi,sha256=8FL7FHo9f5ZAO1veWlhC6r1WXpC4JBSZ9jAGr5VBrhs,372
mypy/typeshed/stdlib/unittest/case.pyi,sha256=rdj_dXLR8IpxB_MiMshNNuCXeuMe61lm9w7lMgIzLXc,12393
mypy/typeshed/stdlib/unittest/loader.pyi,sha256=TqxozgLjLQrA267LOmJu9nehw8VSuSz6ZpjUlW2y5aM,2042
mypy/typeshed/stdlib/unittest/main.pyi,sha256=i93thnUNy53rlnB3d718y4B7j2u9dUjYMjnR0vYzEso,1606
mypy/typeshed/stdlib/unittest/mock.pyi,sha256=NMevaBFUKaRqsz0qN0KCSp2soYR0kjH-P8MJwM-qp20,13137
mypy/typeshed/stdlib/unittest/result.pyi,sha256=UJ0XmDgBMVVOXWRHNDcYivijKzwxH4H3vzjLjI1jTII,1803
mypy/typeshed/stdlib/unittest/runner.pyi,sha256=YOFrpTQDe1aKGF99Jd0pOfNxNn9ZlYsDEeO4BUo_MxU,1300
mypy/typeshed/stdlib/unittest/signals.pyi,sha256=8nTBV8vxdHan7AkzbIwRt3oIUVOVJcKTPkAE4EVlJxI,476
mypy/typeshed/stdlib/unittest/suite.pyi,sha256=20iSwZCox3rWsSEiZ0CjA1FCyiqa-xMJ2lj4fXYlkTk,886
mypy/typeshed/stdlib/unittest/util.pyi,sha256=uUsZCDDcIHICkkRioHngzxaL5IWcaOa30oBUmZPjXdY,900
mypy/typeshed/stdlib/urllib/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/urllib/error.pyi,sha256=Ar5u6vjpYo32c_6sieSqEKjva3D6b0FRed_YPhwuY1I,582
mypy/typeshed/stdlib/urllib/parse.pyi,sha256=hMHzI9qTTEm3133s6MFgOyG0zMIO08y8KSewN75wXmU,4878
mypy/typeshed/stdlib/urllib/request.pyi,sha256=IREgnZLMU8Aep-Rey-DqanS8ctxQAAcO3cgYbroT7wc,15639
mypy/typeshed/stdlib/urllib/response.pyi,sha256=eO5jO7DeH6FFcuDNrkNnRiU5fZ4wNs9hmrRtSV201xA,2223
mypy/typeshed/stdlib/urllib/robotparser.pyi,sha256=jSmtNTQynoAXRcuWSmWOjxXbn6530Ibs0MFguAgukm4,678
mypy/typeshed/stdlib/uu.pyi,sha256=WZbPab3cqO0aHWvL_vRkoJv6VSp-42KOb2890aZzQik,514
mypy/typeshed/stdlib/uuid.pyi,sha256=svfxtpXl3aLiaIDC2AlmkmC8M2CNbwGQ5-qZYasEj8s,2593
mypy/typeshed/stdlib/venv/__init__.pyi,sha256=kIRnmh9Uw_LIloTFsxiLakR0zaXH0I11k3mGfNW1KYU,2501
mypy/typeshed/stdlib/warnings.pyi,sha256=9VVVsxEs9d6FdE_uq9d7Xpe0YdJz0ApFU4HAdPooxmo,2319
mypy/typeshed/stdlib/wave.pyi,sha256=Z5cg_uTXWhaun0231E9HGjobHUY131uGOELI3eRBNfc,2444
mypy/typeshed/stdlib/weakref.pyi,sha256=Xs_A9oWuyAj0ILbNf3ngJhNuaHebQyYsKhoqoCFgvSc,3739
mypy/typeshed/stdlib/webbrowser.pyi,sha256=s4Gh5_dFM-4TIwptdN70mskTVI4WqZulNn9auBHIUGc,3178
mypy/typeshed/stdlib/winreg.pyi,sha256=KmpBP4LrchbOHsu1addqyuP6_se_INuqjftHr85AvQ0,3751
mypy/typeshed/stdlib/winsound.pyi,sha256=v1Yto63zOzzxynR5IiKyNr6ft1zcU0bKN00y8aB6SJI,782
mypy/typeshed/stdlib/wsgiref/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/wsgiref/handlers.pyi,sha256=EOj-AwY4JkpDdO3ARg2pT3-WifRX9vwav5mtSS9dPWc,3040
mypy/typeshed/stdlib/wsgiref/headers.pyi,sha256=qF3DAR1ICXJLHpdA7gvKGH6h_b_SRuOrBfYSXgeK0PU,982
mypy/typeshed/stdlib/wsgiref/simple_server.pyi,sha256=-jpjVWgoth_cOcTury3H5KOou9GJOeovIvvl_7mS3T8,1388
mypy/typeshed/stdlib/wsgiref/types.pyi,sha256=1qhS0qVWoV0IVfe3b1y4Mzhou65EPtwb_QLmWfX5_I4,71
mypy/typeshed/stdlib/wsgiref/util.pyi,sha256=1QepJ62pVomYu4okeNdwWSzBaCwVMdceDzzloAwEFwY,786
mypy/typeshed/stdlib/wsgiref/validate.pyi,sha256=GI6pvtkbNpjd4da7njOpoK4ntDye_u-GZV5FGY2DjQ0,1606
mypy/typeshed/stdlib/xdrlib.pyi,sha256=QGJpJnRtsdKoz2A2pYyqPKLk3uB4ca0KylELJTUM2O4,2309
mypy/typeshed/stdlib/xml/__init__.pyi,sha256=BqMXnsXiYPoalMzEakn6mYDxgyW5N2UPF0Ao7xPuGVY,30
mypy/typeshed/stdlib/xml/dom/NodeFilter.pyi,sha256=bi0L5SEOxk4FyEhf18oU-I8Msf9S9o_tJt-mVc93f28,457
mypy/typeshed/stdlib/xml/dom/__init__.pyi,sha256=gjfWhkwyNoY8SeH6cztWZ9W8w9E4CLgCpHeP8vnHM5c,1844
mypy/typeshed/stdlib/xml/dom/domreg.pyi,sha256=-u_bTyCZb0Y9pX6y_dorG9re5eW1GmKJWpvFXXtiDMg,409
mypy/typeshed/stdlib/xml/dom/expatbuilder.pyi,sha256=wI_eu1G8yaaquRHmZ9mYRgjy4zNNhJC385TjSMoamRg,77
mypy/typeshed/stdlib/xml/dom/minicompat.pyi,sha256=uAErUhWN2yCKcmC5ygb_BJBoL6iTWtyROrtgLCLvhW0,517
mypy/typeshed/stdlib/xml/dom/minidom.pyi,sha256=b2mz_6JJpp0W-4qEmNiNLCZmGqNaqAYu61OGkhHKNtc,11131
mypy/typeshed/stdlib/xml/dom/pulldom.pyi,sha256=IYG2-yWEBGK29jVGbWzhe1gLziJlTH8hAk4iApW0la0,3183
mypy/typeshed/stdlib/xml/dom/xmlbuilder.pyi,sha256=Zih-KjswK4cRJKEbQ-6flIfo9sCwRtVVJ_v4GvfV2uo,173
mypy/typeshed/stdlib/xml/etree/ElementInclude.pyi,sha256=4mtRHXaGJ0iG_UD9q2K1ZMtf5ji_-K8hBNimhTgYZcw,793
mypy/typeshed/stdlib/xml/etree/ElementPath.pyi,sha256=ba74ums7v_dYCq7PG5GioBeVnghQHb80eU5kkw_gso4,1529
mypy/typeshed/stdlib/xml/etree/ElementTree.pyi,sha256=5Si5zLGn-ao3CN4yVNpchaUFwxEgFoo6Fz6c5vw42Tk,11217
mypy/typeshed/stdlib/xml/etree/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/xml/etree/cElementTree.pyi,sha256=D25RVU5Y1Sai47EQ49UTStrYaY39HMqT1HUOZrSioRg,50
mypy/typeshed/stdlib/xml/parsers/__init__.pyi,sha256=FHZYB9bXDrj4RiKUgrctpkuf7_Rms9PqQrGyjkn0EE4,34
mypy/typeshed/stdlib/xml/parsers/expat/__init__.pyi,sha256=qmz8tuPGbZ2rBfRrfYANxDZNxn9BTQXdd9AugF5wDW0,22
mypy/typeshed/stdlib/xml/parsers/expat/errors.pyi,sha256=mH9YRZuV4quzksDMLEmxiisAFgNhMOhl8p07ZzlS2XE,29
mypy/typeshed/stdlib/xml/parsers/expat/model.pyi,sha256=M7GVdd-AxOh6oGw6zfONEATLMsxAIYW2y9kROXnn-Zg,28
mypy/typeshed/stdlib/xml/sax/__init__.pyi,sha256=QoSay4Uf7_7TtcSjc63_gjv72mYYOo30WozZTJcr5pg,1342
mypy/typeshed/stdlib/xml/sax/handler.pyi,sha256=lzRvfmcsC4Px11th3C-OB53OJgrSxHHTkgWKkFnYYII,1391
mypy/typeshed/stdlib/xml/sax/saxutils.pyi,sha256=KaeYvEr7I0H6UQo8lv_rs-BOcBK0bYuyAkuOcVWdnNA,2487
mypy/typeshed/stdlib/xml/sax/xmlreader.pyi,sha256=C-hnh3vr1B4SqGslst3YF-JYUa5A57BT_uSHQGx88bM,2424
mypy/typeshed/stdlib/xmlrpc/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypy/typeshed/stdlib/xmlrpc/client.pyi,sha256=VH9_b_LdmDqnJHUa82Va9CzHNfmykNXKWL2CNfekZnI,12211
mypy/typeshed/stdlib/xmlrpc/server.pyi,sha256=B-scD1mAWtdgJbiavkCoVG01IGROIzOTrNALxxONrm0,6647
mypy/typeshed/stdlib/xxlimited.pyi,sha256=zqyKhfgs72-I3F0kZrsp-D-6-1CETkv6Es6kVRlXjsk,294
mypy/typeshed/stdlib/zipapp.pyi,sha256=nPc9HszSXImgXn0tzETj-XYORpx_wfeD2Ql99XR8JOE,647
mypy/typeshed/stdlib/zipfile.pyi,sha256=EaRLc2oFgV2_OgqUPcGn9kHgex377mcGTtW1yCv5jUA,9173
mypy/typeshed/stdlib/zipimport.pyi,sha256=SWeqbz-jX9INp7mUzkjhHAsL8mppy-spFELtmHQpObA,1018
mypy/typeshed/stdlib/zlib.pyi,sha256=hsHHgJhzbPxaz2e_GORT8U0Q2fxutZqNEms0ICNnCPw,1325
mypy/typeshed/stdlib/zoneinfo/__init__.pyi,sha256=Vvfes6fr9h1G6En6aAuzUpauQoRiwCdJtQopb0stBFs,1143
mypy/typeshed/stubs/mypy-extensions/mypy_extensions.pyi,sha256=_9wWZoRJ3ihHqqToG59Df7Kz9gom6wnTsKdyOqOE15w,2188
mypy/typestate.py,sha256=W4aKdnrCXylE9EGuoX0c-k9FvqOObjaMF82Mcsf7dK4,12955
mypy/typetraverser.py,sha256=o_MuL1Q4pH9hLSV7i8Sqr7PJxRneNXymxDgRE9TQuvI,3182
mypy/typevars.py,sha256=JRlWtlIrAIKVJDtesH5R5ajZJDzRuGRX0EWz6Whj1Fo,2146
mypy/util.py,sha256=L8mA1qb_tTMK_yTG_wRZSWPqJXGmcA4gepZ54jeHRf8,26928
mypy/version.py,sha256=_EKeyeU0058iMEeaHmusKtPKzLKngwAJ45RjF87Q2Bo,22
mypy/visitor.py,sha256=_JNWQ_3BU5n43Ao9RW5t3ZVJfaiEFafsrOpMnMStE9I,14619
mypy/xml/mypy-html.css,sha256=-e3IQLmSIuw_RVP8BzyIIsgGg-eOsefWawOg2b3H2KY,1409
mypy/xml/mypy-html.xslt,sha256=19QUoO3-8HArENuzA1n5sgTiIuUHQEl1YuFy9pJCd3M,3824
mypy/xml/mypy-txt.xslt,sha256=r94I7UBJQRb-QVytQdPlpRVi4R1AZ49vgf1HN-DPp4k,4686
mypy/xml/mypy.xsd,sha256=RQw6a6mG9eTaXDT5p2xxLX8rRhfDUyCMCeyDrmLIhdE,2173
mypyc/README.md,sha256=71rWWVCKNZROxknCxVPlbZW_jdc2eA1-VU1gYDUq9yU,4137
mypyc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypyc/__main__.py,sha256=5q9ChuiYQ3Gy7-z8PJbqsVXD6SOOv0xOWTvvrX7XHJE,1467
mypyc/__pycache__/__init__.cpython-312.pyc,,
mypyc/__pycache__/__main__.cpython-312.pyc,,
mypyc/__pycache__/build.cpython-312.pyc,,
mypyc/__pycache__/common.cpython-312.pyc,,
mypyc/__pycache__/crash.cpython-312.pyc,,
mypyc/__pycache__/errors.cpython-312.pyc,,
mypyc/__pycache__/namegen.cpython-312.pyc,,
mypyc/__pycache__/options.cpython-312.pyc,,
mypyc/__pycache__/rt_subtype.cpython-312.pyc,,
mypyc/__pycache__/sametype.cpython-312.pyc,,
mypyc/__pycache__/subtype.cpython-312.pyc,,
mypyc/analysis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypyc/analysis/__pycache__/__init__.cpython-312.pyc,,
mypyc/analysis/__pycache__/blockfreq.cpython-312.pyc,,
mypyc/analysis/__pycache__/dataflow.cpython-312.pyc,,
mypyc/analysis/__pycache__/ircheck.cpython-312.pyc,,
mypyc/analysis/blockfreq.py,sha256=sn8FdPTO1rdHCKzmjcYZwJtgqgf-MdcXrY4q8DhC-Do,992
mypyc/analysis/dataflow.py,sha256=ywompm29qnjBxdRPXRm5DzOAkbAcS1v9KsM1_SWNaMo,19277
mypyc/analysis/ircheck.py,sha256=vypRdp8a6Ts8NCsPYs_6TP5AeOFGHSwUcVvgxYXl2c8,4571
mypyc/build.py,sha256=EOSxuA8y8aBi3RMCvTThn3Aud4NWV_xqkvoEWdXTsj4,21255
mypyc/codegen/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypyc/codegen/__pycache__/__init__.cpython-312.pyc,,
mypyc/codegen/__pycache__/cstring.cpython-312.pyc,,
mypyc/codegen/__pycache__/emit.cpython-312.pyc,,
mypyc/codegen/__pycache__/emitclass.cpython-312.pyc,,
mypyc/codegen/__pycache__/emitfunc.cpython-312.pyc,,
mypyc/codegen/__pycache__/emitmodule.cpython-312.pyc,,
mypyc/codegen/__pycache__/emitwrapper.cpython-312.pyc,,
mypyc/codegen/__pycache__/literals.cpython-312.pyc,,
mypyc/codegen/cstring.py,sha256=-82sg-FJG5gv5U-zXzXeTW8SGbV3pN9NGOdfiu7zv5g,1998
mypyc/codegen/emit.py,sha256=0nTgy4soKU6Q6sDan3mRfaYgq78xlBDW16pR8M0fGkM,37646
mypyc/codegen/emitclass.py,sha256=qUhFe_Wrr7m_P7PVfVTm8awAnHy8AihfUg1RRQ23cQY,39904
mypyc/codegen/emitfunc.py,sha256=mbsE5XoHBRtg3ut1ru5lOim83dveCtw-0prj1setV2w,25255
mypyc/codegen/emitmodule.py,sha256=tUnh9TOTuT5E4EsSo42b9bXC5kX-ShO9riEQXpT_nkc,47239
mypyc/codegen/emitwrapper.py,sha256=YvJPWyPJTeLvpQkIlpsflm9OLysj3MRMMIVoAd66zuk,37259
mypyc/codegen/literals.py,sha256=nMX6KE5RC2wiXZpg1YPgT2cZMqkqSIA93K1ciT5USqs,9500
mypyc/common.py,sha256=C1kiADNvCu8US5uCuiXz0QEOqGiPd5cSr54POiko5As,4101
mypyc/crash.py,sha256=3tjR8gkf3ZaefQZJuEz6-r6V5zpsMceRfFTPc5V05pU,935
mypyc/doc/Makefile,sha256=i2WHuFlgfyAPEW4ssEP8NY4cOibDJrVjvzSEU8_Ggwc,634
mypyc/doc/__pycache__/conf.cpython-312.pyc,,
mypyc/doc/bool_operations.rst,sha256=wJPFzR26eSaqxtPopRiGw9f_zC-q1-RIE8W2u36b9Ro,373
mypyc/doc/compilation_units.rst,sha256=TXycr64XYsR8fCkDo5EI0wugy_UA8bQieM_Mh8dhdKw,826
mypyc/doc/conf.py,sha256=_69NFukl7tDqyOtP2j6XbvgvPSWJs5zjT_ULtVkO_Fc,2310
mypyc/doc/cpython-timings.md,sha256=XX2UCU8eITd8NbQ6hPaw5WyOMiIfugZFCIUYtOKhHrM,830
mypyc/doc/dev-intro.md,sha256=eBXcAyUG0isZa3U-xxlsz6lQ6rHPF5YVXW-DG1-Rq-o,22563
mypyc/doc/dict_operations.rst,sha256=Zit3bGFlkfbdqOfp15JPcXN5npbIQUwBN8u6guHM7Ew,870
mypyc/doc/differences_from_python.rst,sha256=khW1d4lejUM1lO-N9CBCJz76D5WvMBAp4uDM2Wvf7u4,8271
mypyc/doc/float_operations.rst,sha256=9_czNAfSWFcXwPhGooOLKfT-f7vzFuVzrxdEI9p58EU,433
mypyc/doc/future.md,sha256=b7HAYGmE6hoLMUG_sGZK7EvNAZa-yG2NYSCdFAO4ekw,1431
mypyc/doc/getting_started.rst,sha256=xLaHU3akQxo0uqys7zW79lZeoZ_EkRvh-lgNhOCFHjI,6601
mypyc/doc/index.rst,sha256=KNYFFV8PbpOOiOiP-jot5xXVFE0kLnT55BaosAPtBVo,1116
mypyc/doc/int_operations.rst,sha256=f-a7Ln8wF9h14dZ2t1ykiePkc8kTkldPK_zzHIczoaU,812
mypyc/doc/introduction.rst,sha256=9-PhLuxYDrZl8XewjlSJJq_hWHZtOBhI1UTeSYHEtg8,5818
mypyc/doc/list_operations.rst,sha256=e1kZyc2y0UZ-oZot9QBJc5C6krJB1vKpWfgyIAs7DMY,1029
mypyc/doc/make.bat,sha256=tZPNHaGTFKQ9uaCFT2-S8sWSF1cVAtozIflJjAL6Fsg,795
mypyc/doc/native_classes.rst,sha256=3GicdfJOGKAlVO45FoxznNCDzWf1O_PpcrT0Tl6c4aQ,5233
mypyc/doc/native_operations.rst,sha256=GlwS6Vdxvk_O_b2Y5uitTkr7VSBe9ONbc_WZ9p3P7ok,1175
mypyc/doc/performance_tips_and_tricks.rst,sha256=V7xyh-C8LsqsmcpGh1ssay0dHSxQUBX6UZdf7OCRv_o,8299
mypyc/doc/set_operations.rst,sha256=u5mposJ9vbUi6uyv1lrK-86O4MUEUVybUNzcTWny_4o,672
mypyc/doc/str_operations.rst,sha256=N6xjFRMuet6GEadaJNRaF6Kd4IfZ_XHQSOUBiu0jv9Q,738
mypyc/doc/tuple_operations.rst,sha256=UA1ry-YZQDoXGxVmzRYehyvVs_HMHZfjIKMXhq5vQLA,732
mypyc/doc/using_type_annotations.rst,sha256=2TmnWFw3O5ddsddLZzIHOjLgEnv3G4NlJ6DKrWOlIfQ,11071
mypyc/errors.py,sha256=gPnDdplch0WYd2_TTflR63FQQmfvGXhYbcqqvK9sg68,853
mypyc/external/googletest/LICENSE,sha256=lwLefkEXqOKyDa-rEf_aWMGYrt4GZAZJa-9nDUCiITg,1475
mypyc/external/googletest/README.md,sha256=U653TFRb1xzoRcOl0nc8aQsaa9MOKNq3eeUg-MG-3VA,10524
mypyc/external/googletest/include/gtest/gtest-death-test.h,sha256=oEAXpmp8n6BNppNEOdWJ5SFx6TsTHx9Ooilzu6pvd7A,11523
mypyc/external/googletest/include/gtest/gtest-message.h,sha256=ZinCUfHjDlLyLei-DRUMLEO3v2x1iO6Dse8IaRvcIQo,9186
mypyc/external/googletest/include/gtest/gtest-param-test.h,sha256=qfY-n6X0BZJmdAgfgtEl2e89jco1EKIeNCZ4SFxLOlI,77062
mypyc/external/googletest/include/gtest/gtest-param-test.h.pump,sha256=NpyIfKfS1aiReGrDVwF0g1kZfb7H46dnOx2V1LL6OzQ,20042
mypyc/external/googletest/include/gtest/gtest-printers.h,sha256=4xoLRppoMmDFco3penSRrfIfeniw8rwNrUbEKd4nF1k,36806
mypyc/external/googletest/include/gtest/gtest-spi.h,sha256=URXVqM7TaiC4zsA0gS97DSrCDVEaFH-b7qmw7yfZS1Y,9952
mypyc/external/googletest/include/gtest/gtest-test-part.h,sha256=UbiqNBwPxmdu8nwpcwv9ist_EVH_6z1iWdwC-E4m1lc,6509
mypyc/external/googletest/include/gtest/gtest-typed-test.h,sha256=Z86zBBVbIko-TQUfa3UTwrPFUEqBfdSbUbyCFZ3PXyA,10459
mypyc/external/googletest/include/gtest/gtest.h,sha256=YR-JIlT5RmQ_sfoFo4LZMgPrbcnQTS8RZmHY9bprHfc,85459
mypyc/external/googletest/include/gtest/gtest_pred_impl.h,sha256=O4O-7-rsRr_-QPxziHCzEKhlF_9DahV-TH5dzVGUrWU,15145
mypyc/external/googletest/include/gtest/gtest_prod.h,sha256=Spmj2YakW01tmzr1SAnwFcVKqYJ0eTpK4XP1AQ0K0zw,2324
mypyc/external/googletest/include/gtest/internal/custom/gtest-port.h,sha256=bxpA0nM8YLVd-LFDycgUfpSw88hFonF-tFxCnY-VizI,3143
mypyc/external/googletest/include/gtest/internal/custom/gtest-printers.h,sha256=UhZH8767CA5tdvbOuXaTdySmVroCsqSR6ga4drHSk7w,2099
mypyc/external/googletest/include/gtest/internal/custom/gtest.h,sha256=d9pZKYTaGQGi8ZrlaG8z8j5_5ma27M7WyQYH9CsfV9k,1995
mypyc/external/googletest/include/gtest/internal/gtest-death-test-internal.h,sha256=pH-Yt0nFOuGSo9UOMpouliTV_jLfQt9pISjxeiNz_qs,13429
mypyc/external/googletest/include/gtest/internal/gtest-filepath.h,sha256=ITSxHGDTFSN-jrr5WsTsR6X8SK41zCG-I4v3XmTdUSM,9603
mypyc/external/googletest/include/gtest/internal/gtest-internal.h,sha256=k3o-3UCdXmdGL6iR6BnultJQSm8q-y9ynBkCBdh2f_I,47284
mypyc/external/googletest/include/gtest/internal/gtest-linked_ptr.h,sha256=E1eNfe1J3hQOvx15nt5TXy7Xr7DDxhUcHepURGNjE6w,8424
mypyc/external/googletest/include/gtest/internal/gtest-param-util-generated.h,sha256=M080D-k0YIwk0URIfMIuVmNX4wl24cks6FoARFPdr-k,192177
mypyc/external/googletest/include/gtest/internal/gtest-param-util-generated.h.pump,sha256=1vBEXfV8A9hDH8UZGz7O0OIC4c_tOkW7xHjMBip_gX4,9107
mypyc/external/googletest/include/gtest/internal/gtest-param-util.h,sha256=s2epfRNAs6GAYFD44u0YEjMEFTCj0vL3LguF_gB4dLg,27892
mypyc/external/googletest/include/gtest/internal/gtest-port-arch.h,sha256=0w_3w9C720YzqfrUfRKHLFV9e_40sgYTM8gzDM7ceiE,3471
mypyc/external/googletest/include/gtest/internal/gtest-port.h,sha256=UzvP2W4v_SY3iKh56J_tICcS7xWdxvPwOpTfJdzSK3c,90022
mypyc/external/googletest/include/gtest/internal/gtest-string.h,sha256=b3V_AjXC4N96oGvKZNDQWlsoJsHFzHT5ApjUaN9QtEQ,6968
mypyc/external/googletest/include/gtest/internal/gtest-tuple.h,sha256=tWJY6_-meMw_DO-_yLRK7OBuCZw-mfaZQBHzvMLWFOw,28617
mypyc/external/googletest/include/gtest/internal/gtest-tuple.h.pump,sha256=dRNxezLu4o3s-ImlghK6aHwlH5Lw1eyNDwsLRvRId6g,9620
mypyc/external/googletest/include/gtest/internal/gtest-type-util.h,sha256=fCjK3R_2eofApDo6BtW-2YGaegpfKQIvtpK5iRDs4fM,185666
mypyc/external/googletest/include/gtest/internal/gtest-type-util.h.pump,sha256=hnSm--oNlLE4imhstBWvnV1NwaSc8pLhRXefDCFO-f0,9317
mypyc/external/googletest/make/Makefile,sha256=uEze2Zn577H-Noy4YpRoBUKk0MUWRaEvioyWKyp95f4,2045
mypyc/external/googletest/src/gtest-all.cc,sha256=VorBGfXmQY8fvPvfGF1yRlfX81ObR4ItoimsXQFWJrI,2161
mypyc/external/googletest/src/gtest-death-test.cc,sha256=dMzpg4yQnBrtozU4BLDHPLXS-cvedFhLT_vCGmw1rQo,50942
mypyc/external/googletest/src/gtest-filepath.cc,sha256=05oi5GoRLWlzPzaB5j4YmOkBneI5sctPTGGtesLotYA,14553
mypyc/external/googletest/src/gtest-internal-inl.h,sha256=CZx7w7raKAVq1INo4ziPFuZSvurmXTbq5ppdim7D4Qc,45475
mypyc/external/googletest/src/gtest-port.cc,sha256=zGE4VEMYbGEqFw0YfZdtnq2qJ7RigoOWwHWyNsEdQKk,42985
mypyc/external/googletest/src/gtest-printers.cc,sha256=TisATnhXjqHwvy05beB8qTuRYuF0h8etw09mslZLwN0,12625
mypyc/external/googletest/src/gtest-test-part.cc,sha256=CIP7dtg-ULF6U-ylbW3n5l_MHTmB_Lc24Sm59dAyfAk,4163
mypyc/external/googletest/src/gtest-typed-test.cc,sha256=vzF19TTkXlZeegs7mur5dLCnLRqDwoChUKAfvGV39AI,3960
mypyc/external/googletest/src/gtest.cc,sha256=paFL0Z5CjSmSTB-FqAR5zi7AcVShmdLpL7_rTS0Fz-8,195751
mypyc/external/googletest/src/gtest_main.cc,sha256=oTO8TSAEXgIZqqJEFhoAJuN0h0pVsRZ6JZGYjr-_x18,1765
mypyc/ir/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypyc/ir/__pycache__/__init__.cpython-312.pyc,,
mypyc/ir/__pycache__/class_ir.cpython-312.pyc,,
mypyc/ir/__pycache__/func_ir.cpython-312.pyc,,
mypyc/ir/__pycache__/module_ir.cpython-312.pyc,,
mypyc/ir/__pycache__/ops.cpython-312.pyc,,
mypyc/ir/__pycache__/pprint.cpython-312.pyc,,
mypyc/ir/__pycache__/rtypes.cpython-312.pyc,,
mypyc/ir/class_ir.py,sha256=wFSbewKWeaKrhV86jRe8j_lWA4TYNHsPQmNwB4xrtkQ,18382
mypyc/ir/func_ir.py,sha256=59kzUTdDiP2OfvOfg0b9kvrdq8uGvTZndeX6vuNYfTc,10190
mypyc/ir/module_ir.py,sha256=8Rn0_PYTbIt2kaDk8GX7dCClbkV4uwo74ImyFIz5z4g,3202
mypyc/ir/ops.py,sha256=mFvwIqhpfuauPv8gx_RGE-zT_JtbXr7aO6_TY-n2H1E,40455
mypyc/ir/pprint.py,sha256=sZ1wUEHFl2h5vxlPBkhtIzN11VYLoHx1skvE6JDcV0M,15190
mypyc/ir/rtypes.py,sha256=vVDpxDAFjUhsXGbfBy1X3fbdZP8ol3Bl9YV9KOc-3zo,28771
mypyc/irbuild/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypyc/irbuild/__pycache__/__init__.cpython-312.pyc,,
mypyc/irbuild/__pycache__/builder.cpython-312.pyc,,
mypyc/irbuild/__pycache__/callable_class.cpython-312.pyc,,
mypyc/irbuild/__pycache__/classdef.cpython-312.pyc,,
mypyc/irbuild/__pycache__/constant_fold.cpython-312.pyc,,
mypyc/irbuild/__pycache__/context.cpython-312.pyc,,
mypyc/irbuild/__pycache__/env_class.cpython-312.pyc,,
mypyc/irbuild/__pycache__/expression.cpython-312.pyc,,
mypyc/irbuild/__pycache__/for_helpers.cpython-312.pyc,,
mypyc/irbuild/__pycache__/format_str_tokenizer.cpython-312.pyc,,
mypyc/irbuild/__pycache__/function.cpython-312.pyc,,
mypyc/irbuild/__pycache__/generator.cpython-312.pyc,,
mypyc/irbuild/__pycache__/ll_builder.cpython-312.pyc,,
mypyc/irbuild/__pycache__/main.cpython-312.pyc,,
mypyc/irbuild/__pycache__/mapper.cpython-312.pyc,,
mypyc/irbuild/__pycache__/nonlocalcontrol.cpython-312.pyc,,
mypyc/irbuild/__pycache__/prebuildvisitor.cpython-312.pyc,,
mypyc/irbuild/__pycache__/prepare.cpython-312.pyc,,
mypyc/irbuild/__pycache__/specialize.cpython-312.pyc,,
mypyc/irbuild/__pycache__/statement.cpython-312.pyc,,
mypyc/irbuild/__pycache__/targets.cpython-312.pyc,,
mypyc/irbuild/__pycache__/util.cpython-312.pyc,,
mypyc/irbuild/__pycache__/visitor.cpython-312.pyc,,
mypyc/irbuild/__pycache__/vtable.cpython-312.pyc,,
mypyc/irbuild/builder.py,sha256=ZJYGShKnihj_0YwBPVrqoaVOaTvF8eLVTrQ2rFlXKSA,53945
mypyc/irbuild/callable_class.py,sha256=EzSjQoA84s9vu46ZWCkLHBHdPxwpLOGX0hiSfHmBMTQ,7375
mypyc/irbuild/classdef.py,sha256=dZsiAg9WjmljzV5CWvVIO7lJgNczXxOeeqpbuAhQMwk,31277
mypyc/irbuild/constant_fold.py,sha256=edJCnpYxlYuP_7ZGxYz7PashgmeUFMS1idJbgSnONTY,3029
mypyc/irbuild/context.py,sha256=rgjJU9cNWYUxV6pst7swfJxIrrl8XCAcrmXSnPSH9TI,6751
mypyc/irbuild/env_class.py,sha256=qky7zMbX_Nt_qQM-UvPHc4KY1GPaqdmycejo8J3sUow,8727
mypyc/irbuild/expression.py,sha256=VUyLHC0f6vz1M9T9ZWlXCpjmzU2Yc_ZWEYni6YYIVJQ,31000
mypyc/irbuild/for_helpers.py,sha256=lN3JCZAAgrcGn29xqndZ2Ggwxj5jk9nPcRuG9nktwi4,36080
mypyc/irbuild/format_str_tokenizer.py,sha256=qKrwuomelWDkdSCapQ3c6Z-u7TRmVAs3fSBHna7-XWU,8831
mypyc/irbuild/function.py,sha256=WXS55swhgjdU3B_1jvi4DSJobaT9qwBU-DtsUbHRp38,44014
mypyc/irbuild/generator.py,sha256=C_3W38F2Gi7Zhis261u7nnP0tLlNbdlFyJhm0Qk1o6g,12353
mypyc/irbuild/ll_builder.py,sha256=v5z-kICvYJdniBwJh3400yLEk19TPAUhbdRraLoKqZk,71664
mypyc/irbuild/main.py,sha256=mfsM9h2BjB2E8X1f8dp7ubtvOKQ6PFvVwLH5FTI_JIo,4413
mypyc/irbuild/mapper.py,sha256=nArszAWdODLSL2L6qnRR8OpyWKXYThkWRPofw7KdJgk,7364
mypyc/irbuild/nonlocalcontrol.py,sha256=i1551yO4l5HIKbjKkraf6RxP1aJSjrczDhl8BIKCTD0,7008
mypyc/irbuild/prebuildvisitor.py,sha256=T2__qY8VHwfrYFvvkGqSslZGqvMj4cTLDQU7eP6nDyc,7050
mypyc/irbuild/prepare.py,sha256=IF7AUVe8x3oQTOXKS0ZM5ODH9jA2ZO8RL1uTc0Wys3k,18433
mypyc/irbuild/specialize.py,sha256=SWWiGFg-xg8PCGfXyHxksGEtsqq4bRUCXRwdMm2RcGA,21577
mypyc/irbuild/statement.py,sha256=llBCgefv-hIblijTFP1erwiKBWhpYpttMO1SwcZmzmU,26114
mypyc/irbuild/targets.py,sha256=Q17oh3uL5tV8u9yo1JiOFtzret-5wpheEOTqAhIRyPY,1798
mypyc/irbuild/util.py,sha256=FLKWlXIRZRHYtlGlpsoQxiQYHTcTVlHTbaAKx6rLU0I,5279
mypyc/irbuild/visitor.py,sha256=719n_pvuyzObJOLReTqOt-WyrDlnC8GHhnqd8o-TlAo,12514
mypyc/irbuild/vtable.py,sha256=B17QX06mUEJZE3lMUtnAmvGYlP9RYiy_nqNVUC9I6is,3157
mypyc/lib-rt/CPy.h,sha256=C69cL-RgsvHoY3Au0SUi4lnGNhbnGKgLyWL5nIG57yE,21985
mypyc/lib-rt/__pycache__/setup.cpython-312.pyc,,
mypyc/lib-rt/bytes_ops.c,sha256=AkPGQIQpraMtmaa1GvzONaJge8FycZ7f8Es2fW4gf5M,4915
mypyc/lib-rt/dict_ops.c,sha256=U7HaGRTweeOyODVwRS9TXtaGRkYw3cjxRiQf34RNVws,12504
mypyc/lib-rt/exc_ops.c,sha256=0v6MmPsGzI50nJ9hGm4jbgJm_qbFXcsfhj71bmhq8N4,7100
mypyc/lib-rt/generic_ops.c,sha256=TTrf48uY9BmMUr5W42cW0y8PcLdyr_CqUZ9VAlubgYY,1717
mypyc/lib-rt/getargs.c,sha256=AQTvAS7LNn7ajr4ZwcZrhKHu9sD_gu2B2aC0cjY8Jis,15739
mypyc/lib-rt/getargsfast.c,sha256=vIz_q6NoTtIEYYdF4jHjpRfVHga6g3H6xKgOYSNZ6r0,18911
mypyc/lib-rt/init.c,sha256=yeF-Uoop1jMX1-ntOOO8-YQiW_7evfdAjyVkWAw-Ap8,473
mypyc/lib-rt/int_ops.c,sha256=R17nK_zCSH9_ghQ1T4xiLcYDLZlX7-xQflq0sWdR-OY,15721
mypyc/lib-rt/list_ops.c,sha256=fA4L6xBjJu6dbRTG0PaBC6YOz1p59YwFh-n0NtrIBj4,6813
mypyc/lib-rt/misc_ops.c,sha256=5bNG28ELktTR48sYSwjafMvEo-N1bwDxclWV2OsHF3s,24945
mypyc/lib-rt/module_shim.tmpl,sha256=jOXsy3GTuo7uA4hBCNWWAZrwSz03Gc6EHiJR--t7PkA,522
mypyc/lib-rt/mypyc_util.h,sha256=wriGFAbDWxPgMxC4LPiM26CtaTeqlNj19v5fwSweH50,1991
mypyc/lib-rt/pythoncapi_compat.h,sha256=rBQkNQcRLvRHa17lsX5y8BG3s5rIE-TG001s67Dwu7c,10221
mypyc/lib-rt/pythonsupport.h,sha256=WZZ16a0qU4eGjKD_9KFG9-bkTz1NFwcrsKRPi4dvG-w,12155
mypyc/lib-rt/set_ops.c,sha256=-fImDML6Aobq7-NCbb28O19g6C2YyHkGJ6NF1gueHJM,351
mypyc/lib-rt/setup.py,sha256=3zdKv77tVp_wgN9WaBPKHQDymzW3GO_-UeKfxhq4beY,889
mypyc/lib-rt/str_ops.c,sha256=fnnrTqDsiwB-4-_1yQS6C6GgCfn97R1lGTXxXatTCJM,8075
mypyc/lib-rt/test_capi.cc,sha256=m9BcwiGWaegosfT5WOIhnvAlv0tMMTAIxCTUNDRMRd0,19484
mypyc/lib-rt/tuple_ops.c,sha256=xodLF2mCIIknpFZy-KHoL2eEVdKUh5m8rmTl0V2hQnE,1985
mypyc/namegen.py,sha256=BOvoibxIY0O5NsXEgttbUYbakxdpf4eT_UYspGNWFPk,4382
mypyc/options.py,sha256=N1CFwenm1F8-ymUKMjbTpaqOePROmHc7bdyeFwi0Tlg,1114
mypyc/primitives/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypyc/primitives/__pycache__/__init__.cpython-312.pyc,,
mypyc/primitives/__pycache__/bytes_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/dict_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/exc_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/float_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/generic_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/int_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/list_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/misc_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/registry.cpython-312.pyc,,
mypyc/primitives/__pycache__/set_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/str_ops.cpython-312.pyc,,
mypyc/primitives/__pycache__/tuple_ops.cpython-312.pyc,,
mypyc/primitives/bytes_ops.py,sha256=npy830ah9Z5QT-eWPnzO5cxtbliDjOTn0isKUAbKVyg,2342
mypyc/primitives/dict_ops.py,sha256=xu3jlHUuvoUuNPxqjRU3mHi0tundLDDNxu8VddLuonM,7529
mypyc/primitives/exc_ops.py,sha256=SlPxkP2hv2Ug1Eti_jRXu7fQwzh1ogZ3Ip8IBP9dCas,3263
mypyc/primitives/float_ops.py,sha256=X5Il-BwEe-DvPeexE4gqgzM-z5GPYvGMR5KnHI1C6jA,565
mypyc/primitives/generic_ops.py,sha256=UNZPYPBO6eSsmzzHsDvwNz9L4Pr_Dql88iCUcd-EhTo,9905
mypyc/primitives/int_ops.py,sha256=jgYvyqG6trfAgMTpzKwfxuDPkpA7kUpeEghOFNq0Zmw,6265
mypyc/primitives/list_ops.py,sha256=jk1Bk7f9rRu6ypTyeMmDVU8KYi4SgglzGyks5_NJPDk,5366
mypyc/primitives/misc_ops.py,sha256=g8D1qUxb0GxyGmOyQNgyV8dnkfIQkEy1YwU1vQHzCcI,7340
mypyc/primitives/registry.py,sha256=BT9Ie6RYTtvywRwbL5jfVyYpMZsOaeE9FimH-xovFjE,10583
mypyc/primitives/set_ops.py,sha256=P21qguVZnBwrPHnwFSj7aPOnotzTiPDDmgDLWLl5ObI,2394
mypyc/primitives/str_ops.py,sha256=QYs0bQzC4QFiOJmKRSuCNlVEbULJhg4d_adJwBVRLRk,5638
mypyc/primitives/tuple_ops.py,sha256=yuFo3ucZhAkFli7rOmaMmgVBHDtTz2TpjvuFmwE1RYw,2168
mypyc/rt_subtype.py,sha256=2pqdx-w0W1HfW2LBUGfNA4RwbNunRa08Jp906ivfLPQ,2324
mypyc/sametype.py,sha256=duZhY7iGMGnSHnLPCtb1mil93G2m1Ywk6kMWmSrNVps,2255
mypyc/subtype.py,sha256=Z0_4oo6Td4Ru1VFl_fs31h_YGAazU3bRz5S8eL33WQs,2531
mypyc/test-data/analysis.test,sha256=1t-1MxefLi3XHzan_hF-tFbW0sp3K4YjCZH6lCTiFpA,13799
mypyc/test-data/commandline.test,sha256=Ak1aHz2LwFsj9UWpnfJulxWCVo0-foxNjG5-AYVj1jI,5480
mypyc/test-data/driver/__pycache__/driver.cpython-312.pyc,,
mypyc/test-data/driver/driver.py,sha256=sCjkobEgMymA0WmxTmevPmOorrE32aT5QfU2RG0qDr4,1239
mypyc/test-data/exceptions-freq.test,sha256=aG4yfV5Ms7C8OTaJk4DeHV69z5N_a7PGMwA8SMTc15A,2129
mypyc/test-data/exceptions.test,sha256=0hScn-hOMII-Dl6iQ5XOwIIpklVcG9kGF846u7ZDhyk,9779
mypyc/test-data/fixtures/__pycache__/ir.cpython-312.pyc,,
mypyc/test-data/fixtures/__pycache__/testutil.cpython-312.pyc,,
mypyc/test-data/fixtures/ir.py,sha256=FkeEiKLH-xqS9bBd_uuXj3I4xvc8Xk7MHcxv8nLKkDQ,10980
mypyc/test-data/fixtures/testutil.py,sha256=LiqUs59ltlPLugsjuH2pgPLrNVM4JqFLQ--rO83g0ek,1562
mypyc/test-data/fixtures/typing-full.pyi,sha256=mJGBA1lWQ6r5q_9AOglBk0to2Iw_uWTCHkyqlFl7d0g,4902
mypyc/test-data/irbuild-any.test,sha256=Hs9GNmDqtk__NgbMt5d45QkBjbMyejKHs7NejU3Bxwc,3234
mypyc/test-data/irbuild-basic.test,sha256=ZpMRYOoJSzwoahaop0seAnpi9W8JHhhpiEYgUDcmaWg,74142
mypyc/test-data/irbuild-bytes.test,sha256=IwGw3WkQ8ItsUb01ypVrTTp8-LhPq_ZzX7bXEtQNd54,3673
mypyc/test-data/irbuild-classes.test,sha256=Lpld5jLtoLs8S8Frxj5Vv86fAo3PZDFsvIu5EmtHlWc,23823
mypyc/test-data/irbuild-constant-fold.test,sha256=7aONf9m-ZuNDRry4fIJtmnw2SyExNj-4V4Kmbp1eKNE,5494
mypyc/test-data/irbuild-dict.test,sha256=j4rchLROWQP9bFHZXxymAzTEGvr32_qCi0f3cy2bH4A,8778
mypyc/test-data/irbuild-dunders.test,sha256=xYeXfZkCz1bKSzxb9O-9NLoxiZVXns6awpGVPkYPPkQ,3248
mypyc/test-data/irbuild-generics.test,sha256=mg5fqMUo-0AKQKP0iISDPaLYdp72GdJ22TTMSMDPgyo,2674
mypyc/test-data/irbuild-int.test,sha256=YIkIkauVLY9XqN4VDsC4ilCrKTBwjSmkbzMUb2DBRtg,1846
mypyc/test-data/irbuild-isinstance.test,sha256=VGhZhKY5MB6kjQlQJrgTCbn1sLVwAlMCOePcyVjBUfw,1646
mypyc/test-data/irbuild-lists.test,sha256=Kb4NsrRBp8xGHrFWCRfrKxDg2l4IvJ2XHWJvXTCcLfM,8338
mypyc/test-data/irbuild-nested.test,sha256=BLSkxjE5jXMW3a62RSmz2c4quoICSVF8HAJj2oVdh9o,20165
mypyc/test-data/irbuild-optional.test,sha256=10PVJSEnxuMQHojIFsbfy5XMQACk0iV4ZVY5Sxh7jyw,9262
mypyc/test-data/irbuild-set.test,sha256=bnUShKjLuSRhZlnmkMxCueXXUbM60hrLCzg_CiNptTg,12427
mypyc/test-data/irbuild-singledispatch.test,sha256=TkvBZ0NbWXCSOahOWq9HlvDso8CXnFPUZwFm6J4mDJw,5791
mypyc/test-data/irbuild-statements.test,sha256=PDE6OLrvwSU8YgmHuk80B-jNREH8nKSo9ZFezUfVZsE,20481
mypyc/test-data/irbuild-str.test,sha256=1v-C1-3G-0TU3Pi5NUIFwPJdcSC3y943Nx07MAlapRo,6839
mypyc/test-data/irbuild-strip-asserts.test,sha256=8T999Nn-NlSH9HNaPSzO_UTg_ynGq9aehbVAuA1dkvk,161
mypyc/test-data/irbuild-try.test,sha256=YrkM1dgsMrRxWQrpdsDxZZGv5mrJPYkgHalCQ3Vjnu8,8447
mypyc/test-data/irbuild-tuple.test,sha256=Idvaa-ms6k0wLCvmJToB2YpPaXjJjGBJvPe3G46XYNI,8670
mypyc/test-data/irbuild-unreachable.test,sha256=-R0axRfoCFsgBQxyWc0Jf8d9w4A0J9eLXh3SXjZ2UrE,2116
mypyc/test-data/irbuild-vectorcall.test,sha256=qI1hHq_07SsI3kkUFYxYvhzNODC1oiMJQqlw0nXwLoQ,3193
mypyc/test-data/refcount.test,sha256=ufEyDLUF5d-D5jjAuyssRJ4T9RQwCqmd27nLkZi-J6M,14709
mypyc/test-data/run-attrs.test,sha256=FmF5xIdOOOPBgLy1EZjkuLzz3O_xhU5ysnUwW8OfRdk,7472
mypyc/test-data/run-bench.test,sha256=dCKvWAGTVQrMCYG8cHzGtum05nTBTK6IcFHpDVHP0u0,5633
mypyc/test-data/run-bools.test,sha256=HeWIxKdx9oXkXtN92blNTSTgUu117BUTGixbwEJ6hOo,1645
mypyc/test-data/run-bytes.test,sha256=FqS1Q64h1KxZ7QzEV9RvRTeYCY2Mf9et5wnJQnAovv4,8619
mypyc/test-data/run-classes.test,sha256=OwW7Y8IXrQYcNLCJer3Nra7gYroKcWkoK0jk2TO_BrQ,36655
mypyc/test-data/run-dicts.test,sha256=pmwLEvggNBNIitjBuejmyDSCfvSyZX_OxpKcwqoItrw,7214
mypyc/test-data/run-dunders.test,sha256=GGKhh_OqqE4jUVoiH_IUgHjzOAMBKKke1qAXBnOdyvs,16872
mypyc/test-data/run-exceptions.test,sha256=7QzUa1VjaR1xgTNevaUNNaw443rxjoKawSlFT41rQiw,8038
mypyc/test-data/run-floats.test,sha256=5TFjFKB4f48rOgcwKrrUxE4RnyWO0YU7jDw3_q1P8eA,784
mypyc/test-data/run-functions.test,sha256=C6F5TcU0r6jwDB6yLHKTHBw89Wpv7Rb8Nu4RvAypVWo,28637
mypyc/test-data/run-generators.test,sha256=XVWNPSGEvDkxz4vI243UnqSp22vEQQ7j75S7kw2rPco,12745
mypyc/test-data/run-imports.test,sha256=AfAo9WTsyYuWPIvbXafhuMVMtyRwstkRLCNxxERTvWs,4036
mypyc/test-data/run-integers.test,sha256=5x7YDjt_f6exevI381diWWlMs5PFStiFXEydTSLsPJo,13031
mypyc/test-data/run-lists.test,sha256=7Bz64_ki8wEUAuj5KWtSScGe9vUzZnO_aOhWYvvsBlQ,8695
mypyc/test-data/run-loops.test,sha256=wxKsZ5bnVvejxoWOOJyGJ25KuEIUkO4Y1Du1ns_Fbv4,9311
mypyc/test-data/run-misc.test,sha256=2oEGlgeEGnEGjo9_TBV5dnP7QdE2FR5h_pjLIuwWWes,24521
mypyc/test-data/run-multimodule.test,sha256=GRpsJViv-2-TgFefZ2runMKfyS9xqEl2bmaH6djiIYY,12906
mypyc/test-data/run-mypy-sim.test,sha256=gEFbly0ydcW6ks-ZlIIRdLrszt1UTH1bqpYiXX5611o,3984
mypyc/test-data/run-primitives.test,sha256=bmhNzK4hCC3CVg8-bLao3Mbnt55y3RZdStSUOWj9Qwc,8028
mypyc/test-data/run-python37.test,sha256=rwMbsO44_2vjVLIEcbYfTNivu9aDS8nbsG1_gwMHr20,3587
mypyc/test-data/run-python38.test,sha256=80qSVb1outZVHu1ekex9Ugem_k0mpbY1VAiehbuyl3Y,1970
mypyc/test-data/run-sets.test,sha256=5GKj9pnTK2l9K-2SBvREPdeVg3lC3yjUD9LZVQ-ne_w,2292
mypyc/test-data/run-singledispatch.test,sha256=BMMFI5qw566gqCb8y0r_OCYFkJvCogUC9hzhhNx-Avc,14527
mypyc/test-data/run-strings.test,sha256=03lDbV2Wx4q-YoS2vJiSsgen0HtKIN6Wy2sIfWy_0Ac,23953
mypyc/test-data/run-traits.test,sha256=35wAgnsnDI6rxnF2UYrhW46igBFtJpAdHGKkEcWOsJc,5989
mypyc/test-data/run-tuples.test,sha256=y1xo2TDYF0DnpYc8nSrLioJ4jQ02vVkm-AYUPQCQIq8,4621
mypyc/test/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypyc/test/__pycache__/__init__.cpython-312.pyc,,
mypyc/test/__pycache__/config.cpython-312.pyc,,
mypyc/test/__pycache__/test_analysis.cpython-312.pyc,,
mypyc/test/__pycache__/test_cheader.cpython-312.pyc,,
mypyc/test/__pycache__/test_commandline.cpython-312.pyc,,
mypyc/test/__pycache__/test_emit.cpython-312.pyc,,
mypyc/test/__pycache__/test_emitclass.cpython-312.pyc,,
mypyc/test/__pycache__/test_emitfunc.cpython-312.pyc,,
mypyc/test/__pycache__/test_emitwrapper.cpython-312.pyc,,
mypyc/test/__pycache__/test_exceptions.cpython-312.pyc,,
mypyc/test/__pycache__/test_external.cpython-312.pyc,,
mypyc/test/__pycache__/test_irbuild.cpython-312.pyc,,
mypyc/test/__pycache__/test_ircheck.cpython-312.pyc,,
mypyc/test/__pycache__/test_literals.cpython-312.pyc,,
mypyc/test/__pycache__/test_namegen.cpython-312.pyc,,
mypyc/test/__pycache__/test_pprint.cpython-312.pyc,,
mypyc/test/__pycache__/test_rarray.cpython-312.pyc,,
mypyc/test/__pycache__/test_refcount.cpython-312.pyc,,
mypyc/test/__pycache__/test_run.cpython-312.pyc,,
mypyc/test/__pycache__/test_serialization.cpython-312.pyc,,
mypyc/test/__pycache__/test_struct.cpython-312.pyc,,
mypyc/test/__pycache__/test_subtype.cpython-312.pyc,,
mypyc/test/__pycache__/test_tuplename.cpython-312.pyc,,
mypyc/test/__pycache__/testutil.cpython-312.pyc,,
mypyc/test/config.py,sha256=sEK1EjymMUP-qMv2Fme6mCAAE4cK3A1YvekHaQUeJjk,370
mypyc/test/test_analysis.py,sha256=O_H-K9QPs8Bt5cBDE2FWI_goM3PWW-dx7BajXOQPq40,3363
mypyc/test/test_cheader.py,sha256=i6-f0J0dJb3vjCYo9Rw7tAMQcq8hNxi1trKoSo2k6fI,1613
mypyc/test/test_commandline.py,sha256=lcswLCZq-3rwInhFUwPFQVb3m8YjHarNWTiD6bm07rw,2393
mypyc/test/test_emit.py,sha256=I40F7uZtnUD0eqBSMESAZ8RViO6JfG0FsMrsDplM8YI,1127
mypyc/test/test_emitclass.py,sha256=BLpcA_RonflAJztBei3vuo3YoDazogy4xCu0FuT-5_k,469
mypyc/test/test_emitfunc.py,sha256=mX1b9F_-497DTmdcgtGgtlwHpHuOTBDGtBcvQcv_7RU,22950
mypyc/test/test_emitwrapper.py,sha256=eFjyzS99qOpZiQhKGURFM1zjMMGDJzCo3xehAbCnD1A,2021
mypyc/test/test_exceptions.py,sha256=o3US1u6-qn5td6seGYik40RDrozD2a4gQNCgCJ40wK8,2153
mypyc/test/test_external.py,sha256=iQWahW9N89km93OYlsnsNGxRiOgmNvkEfkXXI-mG18w,1885
mypyc/test/test_irbuild.py,sha256=ijiUxiqOOeXij14ibKJYLt4scAuG1wfjts1vxlYGeKk,2336
mypyc/test/test_ircheck.py,sha256=L4w-MEDfrB-KS2XYAdQUc07SbBD1vQIxzgBLd5PfhyQ,2977
mypyc/test/test_literals.py,sha256=yLu792ZZupzB4gc63t6hLOfBdVXt-jYTjYoK8-Skypw,3299
mypyc/test/test_namegen.py,sha256=FLGjyXUb49f0jQlZJWv6Ftir7M_LhP9Arrw-6-xTrQU,1873
mypyc/test/test_pprint.py,sha256=r3lzXFclezcA7uWTIxWMiSn7z7IRvFBc1mRozlJJgE8,1269
mypyc/test/test_rarray.py,sha256=UV8AZhSvP6ioXacQSOQ8fiOH8BbrZmLDEzH99vdowzw,1435
mypyc/test/test_refcount.py,sha256=KDoRYh3XaqJFXdjnGs770lihmTriuwJGT_ierju_kKo,2063
mypyc/test/test_run.py,sha256=ceaUI2GOra9Ff3iv55CCyXvSRm0vjm1DjjPBiPT6eSk,14473
mypyc/test/test_serialization.py,sha256=TwLt137iB5UAX7AVHPxqwHFO-binVXh7dn9-IfEJfXg,3954
mypyc/test/test_struct.py,sha256=ATmYV2ejx15PLiQ05c1qoBRvOhsJOITrpeiOC5fz6F4,4094
mypyc/test/test_subtype.py,sha256=KbfWJsj29f9g89E6675ociKTXd__-ZeC_J8rI-4kLLI,972
mypyc/test/test_tuplename.py,sha256=MSAgrfpwFc96pWFfNVJtqNgfXsH0YZPYuXv5fjb6m10,974
mypyc/test/testutil.py,sha256=g6ysuz8aIlsdNXs1xhnQszlBHr7kroN85TOQFquDQiU,9219
mypyc/transform/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mypyc/transform/__pycache__/__init__.cpython-312.pyc,,
mypyc/transform/__pycache__/exceptions.cpython-312.pyc,,
mypyc/transform/__pycache__/refcount.cpython-312.pyc,,
mypyc/transform/__pycache__/uninit.cpython-312.pyc,,
mypyc/transform/exceptions.py,sha256=QuIPu_yScOX3w5Z36vSPEcfTjjcrOoNweo1oRwqPUaI,3962
mypyc/transform/refcount.py,sha256=Wzpmd-0ibFVcWXLr7U5L_yWyPmLPc3C9KkDEa3_PiTw,10403
mypyc/transform/uninit.py,sha256=99GOcKaFa85x5nDbYVaUhXOchKhItpG4jo5grThwr0M,3440
