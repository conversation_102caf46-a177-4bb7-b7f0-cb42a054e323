<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="cura_app" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
    <module name="Cura" />
    <option name="ENV_FILES" value="" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="CURA_ENGINE_SEARCH_PATH" value="C:\Users\<USER>\.conan2\p\b\cura_c53e6af90401e\p\res\extruders" />
      <env name="CURA_RESOURCES" value="C:\Users\<USER>\.conan2\p\b\cura_c53e6af90401e\p\res" />
      <env name="PATH" value="C:\Mac\Home\Desktop\CuraProject\Cura\build\generators\cura_venv\Lib\site-packages\PyQt6\Qt6\bin;C:\Users\<USER>\.conan2\p\b\curae3072c54810b88\p\bin;C:\Users\<USER>\.conan2\p\b\arcusf00e1e4633b02\p\bin;C:\Users\<USER>\.conan2\p\b\savitba5a3c8dca2ce\p\bin;C:\Users\<USER>\.conan2\p\b\nest29b6fd26283806\p\bin;C:\Users\<USER>\.conan2\p\nlopt303d2f3e60ce9\p\bin;C:\Users\<USER>\.conan2\p\b\clippc283f5b8659ec\p\bin;C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p\bin;C:\Program Files\Parallels\Parallels Tools\Applications;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\STMicroelectronics\st_toolset\asm;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\CMake\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\include\cpython;c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages;C:\Program Files\STMicroelectronics\STM32Cube\STM32CubeProgrammer\bin;C:\Program Files\dotnet\x64;C:\Program Files (x86)\COSMIC\FSE_Compilers\CXSTM8;C:\Program Files (x86)\COSMIC\FSE_Compilers\CXSTM32;C:\Program Files\nodejs\;C:\gcc-arm-none-eabi-10.3-2021.10\bin;C:\OpenOCD-20240916-0.12.0\drivers;C:\OpenOCD-20240916-0.12.0\bin;C:\w64devkit\bin;C:\Program Files (x86)\STMicroelectro;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312-arm64\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312-arm64\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2023.3.3\bin;;c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\include\cpython;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\dotnet\x64;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\JetBrains\PyCharm 2025.1.2\bin;;;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-arm64\bundled\scripts\noConfigScripts;C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p\bin;windows\arduino\amd64;windows\arduino\CP210x_6.7.4;windows\arduino\FTDI USB Drivers\amd64" />
      <env name="PYTHON" value="C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p\bin\python.exe" />
      <env name="PYTHON_ROOT" value="C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p" />
      <env name="PYTHONPATH" value="C:\Mac\Home\Desktop\CuraProject\Cura\build\generators\cura_venv\Lib\site-packages\PyQt6\Qt6\bin;$PROJECT_DIR$/../Cura\build\generators\cura_venv\Lib\site-packages;C:\Users\<USER>\.conan2\p\b\urani905142587c488\p\plugins;C:\Users\<USER>\.conan2\p\b\urani905142587c488\p\site-packages;C:\Users\<USER>\.conan2\p\b\pyarc5f73a3826e606\p\lib;C:\Users\<USER>\.conan2\p\b\dulci50655dc2b7370\p\lib\pyDulcificum;C:\Users\<USER>\.conan2\p\b\pysav60ff667e08561\p\lib;C:\Users\<USER>\.conan2\p\b\pynesc51129833fb96\p\lib" />
      <env name="PYTHONUNBUFFERED" value="1" />
    </envs>
    <option name="SDK_HOME" value="$PROJECT_DIR$/../Cura\build\generators\cura_venv\Scripts\python.exe" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <option name="SCRIPT_NAME" value="$PROJECT_DIR$/cura_app.py" />
    <option name="PARAMETERS" value="--external-backend" />
    <option name="SHOW_COMMAND_LINE" value="false" />
    <option name="EMULATE_TERMINAL" value="false" />
    <option name="MODULE_MODE" value="false" />
    <option name="REDIRECT_INPUT" value="false" />
    <option name="INPUT_FILE" value="" />
    <method v="2" />
  </configuration>
</component>