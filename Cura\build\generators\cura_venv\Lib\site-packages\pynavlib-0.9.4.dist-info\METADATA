Metadata-Version: 2.2
Name: pynavlib
Version: 0.9.4
Summary: Python binding for the 3Dconnexion's Navigation Library.
Author: 3Dconnexion
License: Copyright 2024 3Dconnexion
        
        Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the “Software”), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
Project-URL: Documentation, https://github.com/3Dconnexion/pynavlib
Project-URL: Changes, https://github.com/3Dconnexion/pynavlib/blob/main/Changes.md
Project-URL: 3Dconnexion, https://3Dconnexion.com
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: MacOS
Requires-Python: <3.14,>=3.10
Description-Content-Type: text/markdown
License-File: LICENSE

# PyNavlib

PyNavlib is a Python extension module that binds original C++ accessors of the **3Dconnexion Navigation Library** into a Python code. The binding covers most of the C/C++ API defined within 3Dconnexion’s SDK header *<SpaceMouse/CNavigation3D.hpp>* and a minimal subset of propietary data types. It was created using pybind11 library.

## About 3Dconnexion Navigation Library
The Navigation Library is a part of 3Dconnexion software. It is responsible for handling data exchange between the driver and a client application. The 3Dconnexion SDK provides C/C++ API to interact with the NavLib from within client application's code or plugin. From now on, with PyNavlib binding, it is also possible for the NavLib to be used by Python applications.

### Installation

The package was released on PyPI and can be installed using pip.
~~~~
pip install pynavlib
~~~~

### General usage
The package contains pre-built extension module responsible for binding C++ NavLib accessors to Python. The extension attempts to dynamically load the Navigation Library which is being installed on the system along with 3DxWare. The binding is accessible via interface module called **pynavlib_interface**. 

~~~~
from pynavlib import pynavlib_interface as pynav
~~~~

PyNavlib's main class is called **NavlibNavigationModel**. It is a base class which provides an interface that has to be implemented by derived NavLib client class. The interface consists of setter and getter methods (no underscore preffixed), which are responsible for sending and receiving relevant 3D view data. 

~~~~

class NavlibClient(pynav.NavlibNavigationModel):

    def get_camera_matrix(self) -> pynav.NavlibMatrix:
        return pynav.NavlibMatrix()
    
    def get_model_extents(self) -> pynav.NavlibBox:
        return pynav.NavlibBox(pynav.NavlibVector(-10., -10., -10.), pynav.NavlibVector(10., 10., 10.))

    def get_is_view_perspective(self) -> bool:
        return False

    ...

    def set_camera_matrix(self, matrix) :
        matrix_as_list = matrix._matrix
        print(f'set_camera_matrix:\n{matrix_as_list[0]}\n{matrix_as_list[1]}\n{matrix_as_list[2]}\n{matrix_as_list[3]}\n')
    
    def set_view_extents(self, extents) :
        _min = extents._min
        _max = extents._max
        print(f'set_view_extents: {[_min._x, _min._y, _min._z]}, {[_max._x, _max._y, _max._z]}')
    
    def set_view_fov(self, fov) :
        pass

    ...
~~~~

To see the full set of overridable setter and getter methods refer to the *pynavlib_interface.py* source.



### Examples

