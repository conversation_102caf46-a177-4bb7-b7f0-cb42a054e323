# Cura 5.11 Development Environment Setup Script
# For ARM Mac Windows VM environment

param(
    [switch]$SkipConanInstall,
    [switch]$TestOnly
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Cura 5.11 Development Environment Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Check current directory
if (!(Test-Path "conanfile.py")) {
    Write-Host "Error: Please run this script in Cura project root directory" -ForegroundColor Red
    exit 1
}

# Step 1: Conan install (optional skip)
if (!$SkipConanInstall -and !$TestOnly) {
    Write-Host "`nStep 1: Running Conan install..." -ForegroundColor Green
    Write-Host "This may take several minutes..." -ForegroundColor Yellow

    $conanResult = & conan install . --build=missing --conf tools.env:CONAN_CMAKE_GENERATOR=Ninja -c tools.cmake.cmaketoolchain:generator=Ninja

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Conan install failed!" -ForegroundColor Red
        exit 1
    }

    Write-Host "✓ Conan install completed" -ForegroundColor Green
}

# Step 2: Setup environment and test
Write-Host "`nStep 2: Setting up development environment..." -ForegroundColor Green

# Activate Conan environment
Write-Host "Activating Conan environment..." -ForegroundColor Yellow
& .\build\generators\virtual_python_env.bat

# Test environment
Write-Host "`nStep 3: Testing development environment..." -ForegroundColor Green

$testScript = @'
import sys
import os

def test_environment():
    print("=" * 50)
    print("Cura Development Environment Test")
    print("=" * 50)

    # Test Python version
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 10):
        print("X Python version too low")
        return False
    print("✓ Python version check passed")

    # Test basic modules
    try:
        import numpy, scipy, shapely
        print("✓ Basic scientific modules OK")
    except ImportError as e:
        print(f"X Basic module import failed: {e}")
        return False

    # Test Cura modules
    try:
        import pyArcus, pynest2d, pySavitar, pyDulcificum
        print("✓ Cura modules OK")
    except ImportError as e:
        print(f"X Cura module import failed: {e}")
        return False

    # Test PyQt6
    try:
        from PyQt6.QtCore import QT_VERSION_STR
        from PyQt6.QtWidgets import QApplication
        print(f"✓ PyQt6 working (Qt {QT_VERSION_STR})")
    except ImportError as e:
        print(f"X PyQt6 import failed: {e}")
        return False

    print("=" * 50)
    print("✓ All tests passed! Cura dev environment ready!")
    print("=" * 50)
    return True

if __name__ == "__main__":
    success = test_environment()
    sys.exit(0 if success else 1)
'@

# Write test script to temp file
$testScript | Out-File -FilePath "temp_test.py" -Encoding UTF8

# Run test
$testResult = & python temp_test.py

# Remove temp file
Remove-Item "temp_test.py" -ErrorAction SilentlyContinue

if ($LASTEXITCODE -ne 0) {
    Write-Host "`nEnvironment test failed!" -ForegroundColor Red
    exit 1
}

# Step 4: Show usage instructions
Write-Host "`nStep 4: Usage Instructions" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "Development environment setup complete! Usage:" -ForegroundColor Green
Write-Host ""
Write-Host "1. Open this project in PyCharm" -ForegroundColor White
Write-Host "2. Use this Python interpreter:" -ForegroundColor White
Write-Host "   .\build\generators\cura_venv\Scripts\python.exe" -ForegroundColor Yellow
Write-Host ""
Write-Host "3. Or in command line:" -ForegroundColor White
Write-Host "   .\build\generators\virtual_python_env.bat" -ForegroundColor Yellow
Write-Host "   python cura_app.py" -ForegroundColor Yellow
Write-Host ""
Write-Host "4. PyCharm run configurations auto-generated in .run directory" -ForegroundColor White
Write-Host ""
Write-Host "Note: Run virtual_python_env.bat in each new terminal" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`nCura development environment setup complete!" -ForegroundColor Green
