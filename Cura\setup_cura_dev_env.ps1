# Cura 5.11 Development Environment Setup Script
# For ARM Mac Windows VM environment with PyQt6 DLL fix
# Automatically handles PyQt6 DLL path issues in virtual environments

param(
    [switch]$SkipConanInstall,
    [switch]$TestOnly,
    [switch]$SkipLicense,
    [switch]$ForceRebuild,
    [string]$Profile = "default",
    [string]$BuildType = "Release",
    [switch]$Enterprise,
    [switch]$Internal,
    [switch]$Staging,
    [switch]$Verbose
)

# Color functions
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }

Write-Info "=========================================="
Write-Info "Cura 5.11 Development Environment Setup"
Write-Info "=========================================="

# Check current directory
if (!(Test-Path "conanfile.py")) {
    Write-Error "Error: Please run this script in Cura project root directory"
    exit 1
}

# Check if build folder exists and handle force rebuild
if ((Test-Path "build") -and $ForceRebuild) {
    Write-Warning "Force rebuild requested. Removing existing build folder..."
    Remove-Item "build" -Recurse -Force -ErrorAction SilentlyContinue
}

# Function to check if license is already accepted
function Test-LicenseAccepted {
    param($PackageName)
    
    $conanHome = $env:CONAN_HOME
    if (!$conanHome) {
        $conanHome = "$env:USERPROFILE\.conan2"
    }
    
    $licensePath = "$conanHome\licenses\$PackageName"
    return (Test-Path $licensePath)
}

# Function to get list of packages that need license acceptance
function Get-PackagesNeedingLicense {
    Write-Info "Checking which packages need license acceptance..."
    
    $packagesNeedingLicense = @()
    
    # Common packages that typically require license acceptance
    $potentialLicensePackages = @(
        "qt",
        "pyqt6", 
        "intel-oneapi-mkl",
        "intel-oneapi-tbb"
    )
    
    foreach ($pkg in $potentialLicensePackages) {
        if (!(Test-LicenseAccepted $pkg)) {
            $packagesNeedingLicense += $pkg
        }
    }
    
    return $packagesNeedingLicense
}

# Step 1: Handle license acceptance
if (!$SkipLicense -and !$TestOnly) {
    $packagesNeedingLicense = Get-PackagesNeedingLicense
    
    if ($packagesNeedingLicense.Count -gt 0) {
        Write-Warning "The following packages may require license acceptance:"
        $packagesNeedingLicense | ForEach-Object { Write-Host "  - $_" -ForegroundColor Yellow }
        Write-Info "You can skip this with -SkipLicense parameter if needed"
        
        $response = Read-Host "Continue with license acceptance? (y/N)"
        if ($response -notmatch '^[Yy]') {
            Write-Info "License acceptance skipped. Use -SkipLicense to avoid this prompt."
            $SkipLicense = $true
        }
    } else {
        Write-Success "All required licenses already accepted"
    }
}

# Step 2: Conan install (optional skip)
if (!$SkipConanInstall -and !$TestOnly) {
    Write-Info "`nStep 1: Running Conan install..."
    Write-Warning "This may take several minutes..."
    
    # Build conan command with options
    $conanArgs = @(
        "install", ".",
        "--build=missing",
        "-c", "tools.cmake.cmaketoolchain:generator=Ninja"
    )
    
    # Add profile if specified
    if ($Profile -ne "default") {
        $conanArgs += "-pr", $Profile
    }
    
    # Add build type
    $conanArgs += "-s", "build_type=$BuildType"
    
    # Add enterprise/internal/staging options
    if ($Enterprise) { $conanArgs += "-o", "cura/*:enterprise=True" }
    if ($Internal) { $conanArgs += "-o", "cura/*:internal=True" }
    if ($Staging) { $conanArgs += "-o", "cura/*:staging=True" }

    # Add skip licenses download option
    if ($SkipLicense) {
        $conanArgs += "-o", "cura/*:skip_licenses_download=True"
        Write-Info "Skipping license downloads to avoid network issues"
    }
    
    # Add license skip if requested or if we detected network issues
    if ($SkipLicense) {
        $env:CONAN_NON_INTERACTIVE = "1"
        Write-Info "Skipping license retrieval (non-interactive mode)"
    }
    
    if ($Verbose) {
        Write-Info "Running: conan $($conanArgs -join ' ')"
    }
    
    $conanResult = & conan @conanArgs
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Conan install failed!"
        Write-Warning "This is often due to network issues during license retrieval."
        Write-Info "Solutions:"
        Write-Info "  1. Run again with -SkipLicense to skip license retrieval"
        Write-Info "  2. Use -ForceRebuild to clean and retry"
        Write-Info "  3. Check your internet connection"

        # Auto-retry with SkipLicense if this was a license-related failure
        if (!$SkipLicense -and $conanResult -match "license|SSL_connect|github.com") {
            Write-Warning "Detected license/network failure. Retrying with -SkipLicense..."
            $env:CONAN_NON_INTERACTIVE = "1"
            $conanArgs += "-o", "cura/*:skip_licenses_download=True"
            Write-Info "Retrying conan install with license skip..."
            $conanResult = & conan @conanArgs

            if ($LASTEXITCODE -ne 0) {
                Write-Error "Conan install failed even with license skip!"
                exit 1
            } else {
                Write-Success "✓ Conan install succeeded with license skip"
            }
        } else {
            exit 1
        }
    }
    
    Write-Success "✓ Conan install completed"
    Write-Success "✓ PyQt6 DLL path automatically configured in conanfile.py"
}

# Step 3: Setup environment and test
Write-Info "`nStep 2: Setting up development environment..."

# Check if virtual environment was created
if (!(Test-Path "build\generators\virtual_python_env.bat")) {
    Write-Error "Virtual environment not found. Conan install may have failed."
    exit 1
}

# Activate Conan environment
Write-Info "Activating Conan environment..."
& .\build\generators\virtual_python_env.bat

# Step 4: Test environment
Write-Info "`nStep 3: Testing development environment..."

$testScript = @'
import sys
import os

def test_environment():
    print("=" * 60)
    print("Cura Development Environment Test")
    print("=" * 60)
    
    success = True
    
    # Test Python version
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 10):
        print("✗ Python version too low")
        success = False
    else:
        print("✓ Python version OK")
    
    # Test basic modules
    try:
        import numpy, scipy, shapely
        print("✓ Basic scientific modules OK")
    except ImportError as e:
        print(f"✗ Basic module import failed: {e}")
        success = False
    
    # Test Cura modules
    try:
        import pyArcus, pynest2d, pySavitar, pyDulcificum
        print("✓ Cura modules OK")
    except ImportError as e:
        print(f"✗ Cura module import failed: {e}")
        success = False
    
    # Test PyQt6 (the critical test)
    try:
        from PyQt6.QtCore import QT_VERSION_STR
        from PyQt6.QtWidgets import QApplication
        print(f"✓ PyQt6 working perfectly! (Qt {QT_VERSION_STR})")
        print("✓ PyQt6 DLL path issue resolved!")
    except ImportError as e:
        print(f"✗ PyQt6 import failed: {e}")
        print("✗ PyQt6 DLL path issue not resolved")
        success = False
    
    # Test Cura app
    try:
        import cura
        print("✓ Cura package importable")
    except ImportError as e:
        print(f"⚠ Cura package import issue: {e}")
    
    print("=" * 60)
    if success:
        print("✓ ALL TESTS PASSED! Development environment ready!")
        print("✓ PyQt6 DLL issue automatically resolved!")
    else:
        print("✗ Some tests failed. Check errors above.")
    print("=" * 60)
    return success

if __name__ == "__main__":
    success = test_environment()
    sys.exit(0 if success else 1)
'@

# Write test script to temp file
$testScript | Out-File -FilePath "temp_test.py" -Encoding UTF8

# Run test
$testResult = & python temp_test.py

# Remove temp file
Remove-Item "temp_test.py" -ErrorAction SilentlyContinue

if ($LASTEXITCODE -ne 0) {
    Write-Error "`nEnvironment test failed!"
    Write-Info "The PyQt6 DLL fix may not have been applied correctly."
    exit 1
}

# Step 5: Show usage instructions
Write-Info "`nStep 4: Usage Instructions"
Write-Info "=========================================="

Write-Success "Development environment setup complete!"
Write-Info ""
Write-Info "PyQt6 DLL path issue has been automatically resolved!"
Write-Info "The fix is built into the conanfile.py and will persist across rebuilds."
Write-Info ""
Write-Info "Usage:"
Write-Info "1. Open this project in PyCharm"
Write-Info "2. Use this Python interpreter:"
Write-Warning "   .\build\generators\cura_venv\Scripts\python.exe"
Write-Info ""
Write-Info "3. Or in command line:"
Write-Warning "   .\build\generators\virtual_python_env.bat"
Write-Warning "   python cura_app.py"
Write-Info ""
Write-Info "4. PyCharm run configurations auto-generated in .run directory"
Write-Info ""
Write-Warning "Note: Run virtual_python_env.bat in each new terminal"
Write-Info "=========================================="

Write-Success "`n🎉 Cura development environment ready with PyQt6 DLL fix!"
