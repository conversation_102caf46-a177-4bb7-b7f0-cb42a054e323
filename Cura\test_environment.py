#!/usr/bin/env python3
"""
测试 Cura 开发环境设置
Test Cura development environment setup
"""

import sys
import os

def test_python_version():
    """测试 Python 版本"""
    print(f"Python 版本: {sys.version}")
    assert sys.version_info >= (3, 12), "需要 Python 3.12 或更高版本"
    print("✓ Python 版本检查通过")

def test_basic_imports():
    """测试基本模块导入"""
    try:
        import numpy
        print(f"✓ NumPy {numpy.__version__} 导入成功")
    except ImportError as e:
        print(f"✗ NumPy 导入失败: {e}")
        return False

    try:
        import scipy
        print(f"✓ SciPy {scipy.__version__} 导入成功")
    except ImportError as e:
        print(f"✗ SciPy 导入失败: {e}")
        return False

    try:
        import shapely
        print(f"✓ Shapely {shapely.__version__} 导入成功")
    except ImportError as e:
        print(f"✗ Shapely 导入失败: {e}")
        return False

    return True

def test_cura_modules():
    """测试 Cura 相关模块"""
    try:
        import pyArcus
        print("✓ pyArcus 导入成功")
    except ImportError as e:
        print(f"✗ pyArcus 导入失败: {e}")
        return False

    try:
        import pynest2d
        print("✓ pynest2d 导入成功")
    except ImportError as e:
        print(f"✗ pynest2d 导入失败: {e}")
        return False

    try:
        import pySavitar
        print("✓ pySavitar 导入成功")
    except ImportError as e:
        print(f"✗ pySavitar 导入失败: {e}")
        return False

    try:
        import pyDulcificum
        print("✓ pyDulcificum 导入成功")
    except ImportError as e:
        print(f"✗ pyDulcificum 导入失败: {e}")
        return False

    return True

def test_qt_modules():
    """测试 Qt 模块（在 ARM Mac 的 Windows 虚拟机中预期会失败）"""
    try:
        from PyQt6.QtCore import QT_VERSION_STR
        print(f"✓ PyQt6 QtCore 导入成功，Qt 版本: {QT_VERSION_STR}")
        return True
    except ImportError as e:
        print(f"⚠ PyQt6 导入失败（在 ARM Mac 的 Windows 虚拟机中这是正常的）")
        print(f"  原因: PyQt6 DLL 是 x64 架构，无法在 ARM 虚拟机中运行")
        print(f"  这不影响在 PyCharm 中进行 Cura 开发")
        return False

def test_environment_variables():
    """测试环境变量"""
    python_path = os.environ.get('PYTHONPATH', '')
    if python_path:
        print(f"✓ PYTHONPATH 已设置")
        print(f"  路径包含: {len(python_path.split(';'))} 个目录")
    else:
        print("⚠ PYTHONPATH 未设置")

    python_root = os.environ.get('PYTHON_ROOT', '')
    if python_root:
        print(f"✓ PYTHON_ROOT: {python_root}")
    else:
        print("⚠ PYTHON_ROOT 未设置")

def main():
    """主测试函数"""
    print("=" * 60)
    print("Cura 开发环境测试")
    print("=" * 60)
    
    test_python_version()
    print()
    
    print("测试基本 Python 模块...")
    basic_ok = test_basic_imports()
    print()
    
    print("测试 Cura 相关模块...")
    cura_ok = test_cura_modules()
    print()
    
    print("测试 Qt 模块...")
    qt_ok = test_qt_modules()
    print()
    
    print("检查环境变量...")
    test_environment_variables()
    print()
    
    print("=" * 60)
    if basic_ok and cura_ok:
        print("✓ 核心环境设置成功！")
        if not qt_ok:
            print("⚠ Qt 模块无法加载（在 ARM Mac 的 Windows 虚拟机中这是正常的）")
        print("你现在可以在 PyCharm 中开发 Cura 了！")
    else:
        print("✗ 环境设置存在问题，请检查上述错误信息")
    print("=" * 60)

if __name__ == "__main__":
    main()
