scipy-1.11.3-cp312-cp312-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.11.3.dist-info/DELVEWHEEL,sha256=fDB9TEegnNqsY6gP8oR4Wi-dFEXYeahykP02zMnbvdo,444
scipy-1.11.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.11.3.dist-info/LICENSE.txt,sha256=V2zwXZiTpJ27DTrfQLvw1BNuiVsbb856rnRDh7lLyjQ,47742
scipy-1.11.3.dist-info/METADATA,sha256=7eK1XkTjS4mYVpFviUWdpEUOkuxGYy6nd6WZCOMdNto,60407
scipy-1.11.3.dist-info/RECORD,,
scipy-1.11.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.11.3.dist-info/WHEEL,sha256=50PeAbplA6PkI0hYOYoeacB9US1R6EguyfOnsccH0WU,85
scipy.libs/libopenblas_v0.3.20-571-g3dec11c6-gcc_10_3_0-c2315440d6b6cef5037bad648efc8c59.dll,sha256=KPsyRkaXuLjtheb2qxII1vf_QQ7c_p0oaehKJeL_eRs,35938219
scipy/__config__.py,sha256=dS4diYEJLD_k47knVyddtRLVdWDt6Im4XYaCnWbCum4,5052
scipy/__init__.py,sha256=NU0wJrle2QFEkcR8qTYowq_vXeWGNak6SXsE_JDHL28,7056
scipy/__pycache__/__config__.cpython-312.pyc,,
scipy/__pycache__/__init__.cpython-312.pyc,,
scipy/__pycache__/_distributor_init.cpython-312.pyc,,
scipy/__pycache__/conftest.cpython-312.pyc,,
scipy/__pycache__/version.cpython-312.pyc,,
scipy/_distributor_init.py,sha256=JOqSUAVCNcvuXYwzfs41kJFHvhLdrqRfp3ZYk5U71Pc,1247
scipy/_lib/__init__.py,sha256=lcHbxe9IkuoUZjXA8VX6-SYYCREBLq1cqoPo2mR6V1w,367
scipy/_lib/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-312.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-312.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-312.pyc,,
scipy/_lib/__pycache__/_docscrape.cpython-312.pyc,,
scipy/_lib/__pycache__/_finite_differences.cpython-312.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-312.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-312.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-312.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-312.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-312.pyc,,
scipy/_lib/__pycache__/_util.cpython-312.pyc,,
scipy/_lib/__pycache__/decorator.cpython-312.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-312.pyc,,
scipy/_lib/__pycache__/doccer.cpython-312.pyc,,
scipy/_lib/__pycache__/uarray.cpython-312.pyc,,
scipy/_lib/_bunch.py,sha256=ukSSaQ7HflykyjpuTaVbgzTqthbtpEgLrEC8CnD_17o,8341
scipy/_lib/_ccallback.py,sha256=hZTLy5-y0nlAIhIHN0KsrFWPRiPKWgP29EHF-WQMFGw,7210
scipy/_lib/_ccallback_c.cp312-win_amd64.dll.a,sha256=U8D7zDh1jnxA75ysNvhoGHoGYl8HP3xMXLvgvRiRXnM,1608
scipy/_lib/_ccallback_c.cp312-win_amd64.pyd,sha256=iL_ifxREoOy1GeQRNeYekvroK46PO27oQC54WD-bJME,59392
scipy/_lib/_disjoint_set.py,sha256=3EIkZl2z9UajnPRVjSD5H8JszidKURpxHCO580OJGC8,6414
scipy/_lib/_docscrape.py,sha256=6-MJIfn2ZhyggatDvyBbQLAgpwwNRUPo5raoj3iSReU,22227
scipy/_lib/_finite_differences.py,sha256=Uu28sJ1PNvgW0Y7ESs0voon5AVYtj0QRrbqsVPjoL70,4317
scipy/_lib/_fpumode.cp312-win_amd64.dll.a,sha256=Hdqrm-E3Nst5f-FrTeJ_vak6dUr3frOTrPLCC-QBzpY,1560
scipy/_lib/_fpumode.cp312-win_amd64.pyd,sha256=mKMqCXrpNvDYDoS9CVKZO-7flyEC7esNRQxXvNE3dKo,15872
scipy/_lib/_gcutils.py,sha256=ULLIREutjgyo8dLsvU2hhI0lDGeu5LqX6557X60xPi0,2774
scipy/_lib/_pep440.py,sha256=xSupJHSD_X9EOOUla1iKcNZ9lwxW6qraE3cbHHLunBY,14492
scipy/_lib/_test_ccallback.cp312-win_amd64.dll.a,sha256=J-fRqUYow6j45wm4lzyTfdLryiP-wLCnZ5DLFjGv0S8,1640
scipy/_lib/_test_ccallback.cp312-win_amd64.pyd,sha256=_waJBi3-nRByr8QYiTLZrzZfnSp8z34srkbnfE6I3R0,52224
scipy/_lib/_test_deprecation_call.cp312-win_amd64.dll.a,sha256=2AEXHcMd5Q23QSshx4iaI7DkV1jpyAyYI-84hfjBtKY,1724
scipy/_lib/_test_deprecation_call.cp312-win_amd64.pyd,sha256=uNKH5qIYYwHkn7PQoOclz_DSB2hhZm_doYyQFEWE-2k,24064
scipy/_lib/_test_deprecation_def.cp312-win_amd64.dll.a,sha256=lCIK4xvEKtZQxmR7ErrNOfN1UygBucBu7aIxzLAwk9I,1712
scipy/_lib/_test_deprecation_def.cp312-win_amd64.pyd,sha256=BVOq5kJgMVVjPtWiwYPaJLrw2p9TYf_C_NIryW-Tl6s,24064
scipy/_lib/_testutils.py,sha256=C3YKXJGotuKGKvXPsWmDbn3axOAREcja-ibiW3l9mkg,8089
scipy/_lib/_threadsafety.py,sha256=2dkby9bQV_BrdlThHUfiqZJsQq-Mte5R28_ueFpNikA,1513
scipy/_lib/_tmpdirs.py,sha256=r8sz8dcfBYiPAYondvpGHVVFB2_rB5sajkTeV1gt1fw,2460
scipy/_lib/_uarray/LICENSE,sha256=5MSswx6ChwZtfJldJuPvkCyhl37hh7JgsRwng2ZQOBE,1543
scipy/_lib/_uarray/__init__.py,sha256=_rTAltFwgd5_QrYGqs0h5iGLaQaTZWG_YYoBL9z8EPc,4609
scipy/_lib/_uarray/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-312.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=mEw-TGkmzqlGuii1cEmlVMY1CqiUiUZULO8V8BMeYNc,21178
scipy/_lib/_uarray/_uarray.cp312-win_amd64.dll.a,sha256=T1DlL0b6ZHUWD-nOgXhhpExpjwON1ZLVJPYpOP-drDo,1544
scipy/_lib/_uarray/_uarray.cp312-win_amd64.pyd,sha256=pwAYijtLWizKBuOppjtmOlVyza5L2VY29g2Qf-7cnL0,235520
scipy/_lib/_util.py,sha256=G0eqU5gU0fNaeIDWQNVr2UeUyut7zjA1D3eL1mhLWx8,25950
scipy/_lib/decorator.py,sha256=4YmnAvz1GC-Q1Qf4ktN38zP4RZI5Ub0k6tu-sM5TwpM,15444
scipy/_lib/deprecation.py,sha256=thgl-8k4c8FDzXKWz7YCVfjbX8zSsBYQ99RLZNYUNaQ,3255
scipy/_lib/doccer.py,sha256=9Ins8j58b2YQkD-vWCH1nMJ7pklhhRWqjELTeiJ-a_w,8637
scipy/_lib/messagestream.cp312-win_amd64.dll.a,sha256=qLj9UWhtV3wW8FP9DfIa2AuxuLM5lIxM-eCsCFxdzu4,1616
scipy/_lib/messagestream.cp312-win_amd64.pyd,sha256=Hi5GQDzjirF7Pj8614UgDaLqaw3HRkfCtzEKEDmR9uM,43520
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_public_api.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_scipy_version.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-312.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=EeeSFQ1MTmcgxcDD5yI16UUAfESjSx40n3prcogOX30,3517
scipy/_lib/tests/test__pep440.py,sha256=4AS6UhkhxfVqvZfZmxM0pgIOvQVbAjPdWN3YtgfXWkY,2344
scipy/_lib/tests/test__testutils.py,sha256=cI6oLinJsZDXIi_pmPMTVAYTvepddvhYVGEMd2MlW2Q,832
scipy/_lib/tests/test__threadsafety.py,sha256=nnAypNwYK4YNe2s3xcYfGslLKXqdcrOeza73EeybiAQ,1373
scipy/_lib/tests/test__util.py,sha256=ygfRyG2HAgaKbnuLbhkDMwhQOWFJsy5rI8bItoyCOTc,14236
scipy/_lib/tests/test_bunch.py,sha256=yZmGHnJ-qBIMP-8TmnyvK7OGiXRzHzbRHCpMwIsmsk0,6330
scipy/_lib/tests/test_ccallback.py,sha256=BkiVNpEffUy31JdA6B_IB0szJPVjXgjffZNstre4IMc,6232
scipy/_lib/tests/test_deprecation.py,sha256=NGUuuv24fSTGyTUZWd2saZkapR2NOpIpx0tijRjBQ7Y,374
scipy/_lib/tests/test_import_cycles.py,sha256=pKC7H1Tsu1Cuun_moQFZrRPYrdeuAT0s8dFAeZMeyZI,514
scipy/_lib/tests/test_public_api.py,sha256=UumX84M3L95M1t3eYhe9U9ckhscRrWgUrgo5QQUEUtc,9836
scipy/_lib/tests/test_scipy_version.py,sha256=j-i3VewqD2gfdZZiJQCdlB3x_4EVekZkND1IFWszqhc,624
scipy/_lib/tests/test_tmpdirs.py,sha256=jY1yJyn2NN6l_BX_7u5HOuohD9K_SlU08W_yaoR15ek,1282
scipy/_lib/tests/test_warnings.py,sha256=0TzfnNIPopXA-k-M56juJ2jfTnhDuGfvDaEyCxqudq4,4406
scipy/_lib/uarray.py,sha256=60KNupQxJ6EUbiNisIrzqFQtlSMEzgEUa8kvoItKE-E,804
scipy/cluster/__init__.py,sha256=ck3TgyUyHOG1-MiymZd04JoIvkWrBaFL56fM-LS-tK8,907
scipy/cluster/__pycache__/__init__.cpython-312.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-312.pyc,,
scipy/cluster/__pycache__/vq.cpython-312.pyc,,
scipy/cluster/_hierarchy.cp312-win_amd64.dll.a,sha256=-kXKeibz92UnN0cdmWVjD8jQnWoOPgds6KAr7_wbenA,1580
scipy/cluster/_hierarchy.cp312-win_amd64.pyd,sha256=-gnmqXHtJO1b_5aHDmD7qn5ulhJ4qaIwfjiqwJvjb2E,348672
scipy/cluster/_optimal_leaf_ordering.cp312-win_amd64.dll.a,sha256=7Cv0CCDC3Nv75sn7fN45trbxYVlrYz0snBswSLvBos8,1724
scipy/cluster/_optimal_leaf_ordering.cp312-win_amd64.pyd,sha256=KPRcBNE7n_1fqZFXkCjRuQrTpnWuh5iDLcQZPsslDo4,297984
scipy/cluster/_vq.cp312-win_amd64.dll.a,sha256=MIFVkvjRe3C3zpzp0c_SJugiDiSoIuVGBDSrC35eIRg,1496
scipy/cluster/_vq.cp312-win_amd64.pyd,sha256=aNh0Lk3vWzOhrlAs8x0TVMJ6-M3Q4XoO2ikyWEsXpts,94208
scipy/cluster/hierarchy.py,sha256=hOsKw8flh-ly1e5uylolkrS6_WYgzXDx8B22GHsdSOI,152606
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-312.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-312.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-312.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-312.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=lvVMk2L5iEKlhX32aFRBJ3clecx6AXLYw75Epl07OBo,6995
scipy/cluster/tests/test_disjoint_set.py,sha256=fczIL8hK_ICE23j54dwEdn9KBOipPnpxAhWK7xj8X4o,5727
scipy/cluster/tests/test_hierarchy.py,sha256=_7NFM6kituH0a9WEqpMtk_sjIYn2oI_0sw3i8HMnSCA,44680
scipy/cluster/tests/test_vq.py,sha256=KpROCVFhiI3CgTNeGXBcXwK9RbyTwx-V9nF26Ts0yYA,14058
scipy/cluster/vq.py,sha256=7HD2Hu72aKxtfqElPWroaw1DET6W3r9ZIS5t-NU4Rak,30051
scipy/conftest.py,sha256=PVI0vy9NsJzlPcsgsw9__JsEbTCZG5M5Tr1WoAJa1vY,3566
scipy/constants/__init__.py,sha256=YOyr_JURMV7qN4BVo7YejwBOnSHdiaMnaBso3oVYVxQ,12770
scipy/constants/__pycache__/__init__.cpython-312.pyc,,
scipy/constants/__pycache__/_codata.cpython-312.pyc,,
scipy/constants/__pycache__/_constants.cpython-312.pyc,,
scipy/constants/__pycache__/codata.cpython-312.pyc,,
scipy/constants/__pycache__/constants.cpython-312.pyc,,
scipy/constants/_codata.py,sha256=BwUKxMJ1Mn-s557ICwCXliYdUTLkAJM5rALK-PfbbJw,157370
scipy/constants/_constants.py,sha256=4vphErK9vHEU38_SV63EtOZD_bDIyyizFHyxOFM_CqM,10730
scipy/constants/codata.py,sha256=O34xrpPg4Pz6ARh1IkA4BNDqL_Fgu5kps-LtoQDo5OA,1047
scipy/constants/constants.py,sha256=ElE0VaXOuGlGx4NJEVOSNS1eH4YKPzTZr_0wwUH32T8,2538
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-312.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-312.pyc,,
scipy/constants/tests/test_codata.py,sha256=0p4cAPH0UMK3JWHGI2Ha05dQpxBOzNSme3_pWcIVTDw,2016
scipy/constants/tests/test_constants.py,sha256=oZy6J36DLygy5IVdcNY_NFJlbvuTTFoAt3EM4JGCIkM,1667
scipy/datasets/__init__.py,sha256=sq5j7Lo90MdMXgQPuWnscm5m62hLPabgZ-n6SnGjmPU,2906
scipy/datasets/__pycache__/__init__.cpython-312.pyc,,
scipy/datasets/__pycache__/_download_all.cpython-312.pyc,,
scipy/datasets/__pycache__/_fetchers.cpython-312.pyc,,
scipy/datasets/__pycache__/_registry.cpython-312.pyc,,
scipy/datasets/__pycache__/_utils.cpython-312.pyc,,
scipy/datasets/_download_all.py,sha256=i-fMQWgAfvOgCEoEabsd68iUYDki-4OUfPjjcGLHRDI,1758
scipy/datasets/_fetchers.py,sha256=QMAi8ei2vmdPxr6VpAYNTkqYA6sdBAzrMH454SUPyhQ,6979
scipy/datasets/_registry.py,sha256=3_ZNYpe3lCXvUGalpNsrhRwKcnFA2P6yWV7MFdKb2Hw,1098
scipy/datasets/_utils.py,sha256=7bK_NVCoURt-HDGe7I0iBGLEdcrLIU5eJuSjJqAkgJg,3048
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/datasets/tests/__pycache__/test_data.cpython-312.pyc,,
scipy/datasets/tests/test_data.py,sha256=loTZrDHWZEQjIihEcSYi6S6HlzltygW3HeRQp61SdVA,4187
scipy/fft/__init__.py,sha256=n4Of6Yu9RZ6sZxgqZdbZeE74dRJ7_TLtPjwhkQiWU6M,3680
scipy/fft/__pycache__/__init__.cpython-312.pyc,,
scipy/fft/__pycache__/_backend.cpython-312.pyc,,
scipy/fft/__pycache__/_basic.cpython-312.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-312.pyc,,
scipy/fft/__pycache__/_fftlog.cpython-312.pyc,,
scipy/fft/__pycache__/_fftlog_multimethods.cpython-312.pyc,,
scipy/fft/__pycache__/_helper.cpython-312.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-312.pyc,,
scipy/fft/_backend.py,sha256=PzGbSSveCv8kiyqfJa6_Ig6Gb6KNf5Wteos6o2A-mlU,6579
scipy/fft/_basic.py,sha256=r8S3dKUv4LQkvcPHQKhMEA2Uo0QvZXa_NKYp9VvJWBM,64620
scipy/fft/_debug_backends.py,sha256=R0zCc_CgDubYnaGbQGjk0GTmd5HlYFpBLw7WSlqljpQ,620
scipy/fft/_fftlog.py,sha256=ev6kJ_ejioQE5TAEHxWEC9p6Rl8qvqMOQuhUQ5Bg2WM,12269
scipy/fft/_fftlog_multimethods.py,sha256=XdQnCMC548a7wnJXC1I8tC8OyDs01kQVVPeY65xU_b0,604
scipy/fft/_helper.py,sha256=TVeHb5Dd7xUVVU8y3j0c1T7AdTRhX55FI5JwCE8QP7E,3515
scipy/fft/_pocketfft/LICENSE.md,sha256=wEZhaFz_nYA1_hxMOrNX5_djPK_AanPp2Qw8X-3oqGo,1523
scipy/fft/_pocketfft/__init__.py,sha256=WKXb59wx9DK9zXq9DwKchp-zg1SuJIynTQ_POQrFXxo,216
scipy/fft/_pocketfft/__pycache__/__init__.cpython-312.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-312.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-312.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-312.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=wX2Az19AXanbBPjGT8cb2o3r8J3ehaochr7VX0hOKj0,10138
scipy/fft/_pocketfft/helper.py,sha256=x4IldW9L-jDV2CIdgyO6vx279ypj6OfMcUujef1dkPQ,5937
scipy/fft/_pocketfft/pypocketfft.cp312-win_amd64.dll.a,sha256=F6GaZi310Z7CmmYzJSyjUF2_yXItkQRR1WfZqvghksg,1592
scipy/fft/_pocketfft/pypocketfft.cp312-win_amd64.pyd,sha256=HBNwYd8CAYUPnIhvAKbZBSt_8qm8FWWHbXitC0uR6zU,1075200
scipy/fft/_pocketfft/realtransforms.py,sha256=w67vn5bxN8Zx4T1XRuU-nSfNRc4VqCVxMLqPQEyo_0k,3488
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-312.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-312.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=g53jLblys_tmHJUzeppE_BEiwzJh6dX04BZbTip30qI,36633
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=jehPJ8m0VgAjFRWqiXwVW62xUcH4SmqypU4WBgTmQcc,16931
scipy/fft/_realtransforms.py,sha256=M5p8GbeXNZUlw_Femb3fT-1XrhiyK5kKrNsJS2sKKl8,25973
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/fft/tests/__pycache__/mock_backend.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_fft_function.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_fftlog.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_numpy.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-312.pyc,,
scipy/fft/tests/mock_backend.py,sha256=lucfCVPsqehDtLXe0cdLB4LL_MaU1xwhzN0u1yqnOCM,1828
scipy/fft/tests/test_backend.py,sha256=dJehAFjzYlIsYqpknfnd_sikp2qI_ndyiL2BkM5cD28,4354
scipy/fft/tests/test_fft_function.py,sha256=365KMEHdBO3ekahEWJdK5uIURtT4DvAwzFtFZEc1Evc,1091
scipy/fft/tests/test_fftlog.py,sha256=5VQG1V9c9NneAQzQmjIcRmkGd1DWif31J2zbavVDiWM,5980
scipy/fft/tests/test_helper.py,sha256=zDM59ZIs02WMS96UfqeoZqN93LOgLmOlk5RI9R2QeQM,10095
scipy/fft/tests/test_multithreading.py,sha256=AD3naMjpD9aQ0sJwLs8n7ln3IzeCzPm5Y6QV8su0YgI,2215
scipy/fft/tests/test_numpy.py,sha256=BN_ic9gJwqJdt2XoEFJyAELTACZ_tj1KvQos98t6NWg,14796
scipy/fft/tests/test_real_transforms.py,sha256=G-aEPl_7igZd97y3NilyWo6Zirb-vClY8BRgfcsKwKA,7806
scipy/fftpack/__init__.py,sha256=NKSnRJ6EMDP3nDxGSJMyd2PJBB2ytMAO01Kq884NXXo,3258
scipy/fftpack/__pycache__/__init__.cpython-312.pyc,,
scipy/fftpack/__pycache__/_basic.cpython-312.pyc,,
scipy/fftpack/__pycache__/_helper.cpython-312.pyc,,
scipy/fftpack/__pycache__/_pseudo_diffs.cpython-312.pyc,,
scipy/fftpack/__pycache__/_realtransforms.cpython-312.pyc,,
scipy/fftpack/__pycache__/basic.cpython-312.pyc,,
scipy/fftpack/__pycache__/helper.cpython-312.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-312.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-312.pyc,,
scipy/fftpack/_basic.py,sha256=6AFPL2JeBvZsIZV-C3kAmvG3ybQ8XtiRLSJQ9Go9L2E,13526
scipy/fftpack/_helper.py,sha256=B1AnxDFXJwbpmsOn16CTfzOITo3dBBdy_Fq0ndj1vhY,3466
scipy/fftpack/_pseudo_diffs.py,sha256=5IZuPf96AqFK17zyOv3PJUICEd5qrzQPqW7Aq-B-Pa8,14751
scipy/fftpack/_realtransforms.py,sha256=gG8Q39JAiO1Y3k8neJR1rfXLNhb9IvfBn7w8URM-Ndw,19812
scipy/fftpack/basic.py,sha256=YP6CJx8M1QhCpeqEP60W_1INHc-QRyzTOq1DIrpqGJI,818
scipy/fftpack/convolve.cp312-win_amd64.dll.a,sha256=U85kviNCezu7eH0L2znCs21HIMNVobhTakYRIzvl4Fs,1560
scipy/fftpack/convolve.cp312-win_amd64.pyd,sha256=xmdIZsYfK601VlLBM3l9LfjIWPlk965eY_namxezVeg,216576
scipy/fftpack/helper.py,sha256=k6qzrP69nZfGgRkgKn_WJJJizN2IOqYgA7USqpfZ5ck,822
scipy/fftpack/pseudo_diffs.py,sha256=XBr7mUmY95jh_1cKGhpjqwCDvBeRI83K6aQBEBchsTE,931
scipy/fftpack/realtransforms.py,sha256=Tgdj9FnqaCUApfALyR772ZHPmaeYfNGk7QjFtvVepHo,853
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-312.pyc,,
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=U1W0zJdoOmbzTcMtQ84-ESua1QjLHBcqJZ_FKd5OH5s,31155
scipy/fftpack/tests/test_helper.py,sha256=HuCAP5esSm4lMnw9f9i-ByJj1DURntgC64s2fX5RVIg,1729
scipy/fftpack/tests/test_import.py,sha256=96z6hsyxt6iMH0Xn1xHd2T-kTyYM3obvlf0xniF73Cw,1151
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=5wmGeVj5QXbFkxrRG7Tl162v4pFSxiMSONtm4Uoy-AQ,13769
scipy/fftpack/tests/test_real_transforms.py,sha256=ujbhnCAegORBpTwl1OyRDWCJtcxWF6NllmMSXYSF8L4,24768
scipy/integrate/__init__.py,sha256=ustIbViBz10rE2Gv5UrcPxI4pi-gi80XrvVMGUuGwUg,4182
scipy/integrate/__pycache__/__init__.cpython-312.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-312.pyc,,
scipy/integrate/__pycache__/_ode.cpython-312.pyc,,
scipy/integrate/__pycache__/_odepack_py.cpython-312.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-312.pyc,,
scipy/integrate/__pycache__/_quadpack_py.cpython-312.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-312.pyc,,
scipy/integrate/__pycache__/dop.cpython-312.pyc,,
scipy/integrate/__pycache__/lsoda.cpython-312.pyc,,
scipy/integrate/__pycache__/odepack.cpython-312.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-312.pyc,,
scipy/integrate/__pycache__/vode.cpython-312.pyc,,
scipy/integrate/_bvp.py,sha256=qOejil-4reFnJWsChtfcxr3smkESLCRs62CMP4cOJm0,42226
scipy/integrate/_dop.cp312-win_amd64.dll.a,sha256=wdDnJUrxglCfKI-dnhyst7WiQFfvPP1zwFA0N6kN8u0,1512
scipy/integrate/_dop.cp312-win_amd64.pyd,sha256=u4Fw1H1PjX4p36A9moaPz1RucjUocCJA_CyttdUpr_E,434176
scipy/integrate/_ivp/__init__.py,sha256=XVoxnj-1q1Xm4StWijukIoIrqc6Ny1Dxi625GurInWs,264
scipy/integrate/_ivp/__pycache__/__init__.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-312.pyc,,
scipy/integrate/_ivp/base.py,sha256=efUVvU8k5dFgglCta7f5ZiCMyTU4XN38JMfiKLQcYFQ,10585
scipy/integrate/_ivp/bdf.py,sha256=TYyHuFvF-eKxe-5PMtzkPs192A9HzkEw7adDazq0QDY,18001
scipy/integrate/_ivp/common.py,sha256=A5cul0X0-MrUGJ3o1mHV2HAt73TTyUkrkyxKbD2iH20,15205
scipy/integrate/_ivp/dop853_coefficients.py,sha256=4oMhmg8eXyzDeSSrlh2dJJMvaabWn-aO44S3wx8Ig5o,7430
scipy/integrate/_ivp/ivp.py,sha256=Zu9YGs3caUZrXdMWjyKLpXBujKwr9fX2vjet1ZmiSZM,29412
scipy/integrate/_ivp/lsoda.py,sha256=QqobAgXNBKUwXqiGb7I7V0RCDeHp84_rZpMZGdX1YjQ,8800
scipy/integrate/_ivp/radau.py,sha256=I33zwaSN9IJzaGiaRy3cU0SS56TyaCpBEiDPCo7IWXs,20317
scipy/integrate/_ivp/rk.py,sha256=FAHMFhB4KsolFDWa7_ldPYgwTIGPKWXfzJAmSZ_jr1U,23365
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-312.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-312.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=TA9GkP6b4miqLbAkT9ezUli9GrZX1X8oOgh0HrzND9Q,36117
scipy/integrate/_ivp/tests/test_rk.py,sha256=_8nQKtotVvR218br-0mNSQSEZ-ST4L2Tgaz5xKXHgJw,1363
scipy/integrate/_lsoda.cp312-win_amd64.dll.a,sha256=u_r-sLvZMKKCtlgQK0E_GUrnJ5lG8Yp_8dXfz7sFdRk,1532
scipy/integrate/_lsoda.cp312-win_amd64.pyd,sha256=s7Zc4-TMEzcznTwi8ZERilDlraEKNdcPTMbBiC9f01Y,429568
scipy/integrate/_ode.py,sha256=hMC2VqGlWtQCg7hM1Dc2mXggIYwCMmuDAoX7IFAHI3Y,49293
scipy/integrate/_odepack.cp312-win_amd64.dll.a,sha256=lmzi_1YTR2xZtJKTiTKRCssz9AnzEpHivt84-gfMtQY,1560
scipy/integrate/_odepack.cp312-win_amd64.pyd,sha256=UasVWm4YJwNNsJWnI5g9zAANfWw6xs7mONgAcqmKcg4,410112
scipy/integrate/_odepack_py.py,sha256=-lOaDEyg4OrG2b__C3JrnGi5jdAJAFVFzxvXCQjXBFc,11029
scipy/integrate/_quad_vec.py,sha256=Vn8wN0TW342rKrYTewQ8fIC6k-2jgpWSiCWGXaIfEA4,21819
scipy/integrate/_quadpack.cp312-win_amd64.dll.a,sha256=hbehPk7i_F0M19BIJYDWPZdHh1oVa801xv_ZPIq9gSk,1568
scipy/integrate/_quadpack.cp312-win_amd64.pyd,sha256=t--eMZPOp7QWgi4baSzIfQD8TUL8F9e6Otu6P4gB2Wg,447488
scipy/integrate/_quadpack_py.py,sha256=kmLZeLzXNMwgw6NQBc7zigyDi9wBjhhYI5IKZkEAUR0,54026
scipy/integrate/_quadrature.py,sha256=MObEfAi7x23LmcptWDYLb9_l2G64Qk1gmNHwww4NCPE,53365
scipy/integrate/_test_multivariate.cp312-win_amd64.dll.a,sha256=eL86Rv4AeC0UHMIrsiMPyzrWJeeh5tD4W7k2uBa85To,1676
scipy/integrate/_test_multivariate.cp312-win_amd64.pyd,sha256=tOFYHzdAJRvBGKLRS7d8wRY_GGRC0oRGK5oCWtFE_H0,17920
scipy/integrate/_test_odeint_banded.cp312-win_amd64.dll.a,sha256=oSXrzyOE3bo8gKcHK1W75IzpE_ATgAzxmhZvZqgumN0,1688
scipy/integrate/_test_odeint_banded.cp312-win_amd64.pyd,sha256=dWDS9iA53aeiFOSbd4pNYcStcw36hmmYcxquAtoDkQk,430592
scipy/integrate/_vode.cp312-win_amd64.dll.a,sha256=QQdUpZwoyyIHWwhy9myUrOc3vhaeSWRpJW74Q7gAZDk,1520
scipy/integrate/_vode.cp312-win_amd64.pyd,sha256=zkkgwUJVDYthuGGqKNadi7ak50EfM6jd7-q7d8qyPgI,491520
scipy/integrate/dop.py,sha256=LuJEuLD2P0QiAKhLFVyKEjmKOOxov-etcRcjUKoGlj0,650
scipy/integrate/lsoda.py,sha256=wUvgBh8lRrVThfzSMXww325Rc-OZXIObG5rM77QwiJY,635
scipy/integrate/odepack.py,sha256=oItkHltyej5LYB0ZfOHK-zrK5bhIzkLYw2C89vz1flk,796
scipy/integrate/quadpack.py,sha256=M53X9b3kdhysl3GFKBf0lha7XYmP_u5Vy3Ndg326xLQ,877
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-312.pyc,,
scipy/integrate/tests/test__quad_vec.py,sha256=HBl9Ju69S4lpllSDsN7SJReLNNgqOzxUY6-Y7WcAiaM,6495
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=erjGeCJNAVCaiulMn698i6ZjMqUFFr0SCZcmo2jJZsM,6905
scipy/integrate/tests/test_bvp.py,sha256=V4jOddeW6ukG3U4jgNinE5hR08ihZ0bZEWOlxe5B6rA,20892
scipy/integrate/tests/test_integrate.py,sha256=F231ywZhXtslebP7f14AjAfmF0_kAQtn0yI1nHNEdRo,25237
scipy/integrate/tests/test_odeint_jac.py,sha256=XedvNm_tVVuiQhUiL_5_OfPe5nDOEc821vsqiLZtm8c,1890
scipy/integrate/tests/test_quadpack.py,sha256=psZrXuoDoZf6YYTSFN2bvOHBkKx88RfUHrRhriYTyWM,28653
scipy/integrate/tests/test_quadrature.py,sha256=NXHKcBQWI_5M40YNLDd2fbnONixftJl-mgUY2liK078,18133
scipy/integrate/vode.py,sha256=l9KU9SI-Csxkcb0OuLsGO_He6KKVfgvIVVDKkJGZbWI,653
scipy/interpolate/__init__.py,sha256=atoDp2yw2xwAXVAK1uMlWrcjzAlHjYH51_lz6SqmDko,3682
scipy/interpolate/__pycache__/__init__.cpython-312.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-312.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-312.pyc,,
scipy/interpolate/__pycache__/_fitpack2.cpython-312.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-312.pyc,,
scipy/interpolate/__pycache__/_fitpack_py.cpython-312.pyc,,
scipy/interpolate/__pycache__/_interpnd_info.cpython-312.pyc,,
scipy/interpolate/__pycache__/_interpolate.cpython-312.pyc,,
scipy/interpolate/__pycache__/_ndgriddata.cpython-312.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-312.pyc,,
scipy/interpolate/__pycache__/_polyint.cpython-312.pyc,,
scipy/interpolate/__pycache__/_rbf.cpython-312.pyc,,
scipy/interpolate/__pycache__/_rbfinterp.cpython-312.pyc,,
scipy/interpolate/__pycache__/_rgi.cpython-312.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-312.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-312.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-312.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-312.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-312.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-312.pyc,,
scipy/interpolate/_bspl.cp312-win_amd64.dll.a,sha256=hHqgBziBIC423R8_1wF0O730q8jjXtBgSEPsaJ9S1qo,1520
scipy/interpolate/_bspl.cp312-win_amd64.pyd,sha256=2I3nEHAcfG0dxzig91--GgfpZ6fUgV9DKYo10WKFShk,298496
scipy/interpolate/_bsplines.py,sha256=Ngrc13FCaiEY4bXa-nY_A19-2YmqBjPWdgaEtfsPu9E,71430
scipy/interpolate/_cubic.py,sha256=Aqrbm_7SqXVA0XtH3i1k9GivyNKPgnE0amYYLZYKxRg,34785
scipy/interpolate/_fitpack.cp312-win_amd64.dll.a,sha256=ExwApOV_W05CSP55eP_7goc7oVK8Tlm3hfgaSsnHDug,1560
scipy/interpolate/_fitpack.cp312-win_amd64.pyd,sha256=A2yoMvFWG7i0rdm5EVCtqVkKuYwvaWNf-zFW9kk8EpU,429568
scipy/interpolate/_fitpack2.py,sha256=gXxb-Zl4iqBVLAdLzATYJTzJmkGYfQgQEul_yOb7cr8,91572
scipy/interpolate/_fitpack_impl.py,sha256=RYxhVqLH9Xao9ZnbbIGIDFtJsHr5ZpRrePvijTAfZ5s,28900
scipy/interpolate/_fitpack_py.py,sha256=zYUa--ZSkmKUjRclG9DDHIB_pOjgfobSbhDmFYOFkRs,28314
scipy/interpolate/_interpnd_info.py,sha256=GYonUhHHH1g1O9TdINaSgOIv1bSzcWDeLCskackRJMM,906
scipy/interpolate/_interpolate.py,sha256=tBoDFJvZ8JHDnOavobCbjuNbGhFjSbutfGdQQo6EaGE,90443
scipy/interpolate/_ndgriddata.py,sha256=04u24lpQ9E5NFPljF-vhstfHrhAM6qrjp5y3Eai1Vt8,9968
scipy/interpolate/_pade.py,sha256=Phydd4f5oVM_-B3_iwe7H3t66FoCjbfsK_TagWLz9Jo,1894
scipy/interpolate/_polyint.py,sha256=kI6h-uAqYowvsKq7K3v8rYHpmtCF2xRGv8K1MPQOXgQ,27212
scipy/interpolate/_ppoly.cp312-win_amd64.dll.a,sha256=_MQ9KYvt0OPE9_G8DT2hWvlkGwXSGOV5amVKiXeoEzI,1532
scipy/interpolate/_ppoly.cp312-win_amd64.pyd,sha256=pysRohIjYSF8gBWJ32lDm7EZ3lDn83wQ7FX64JoPbuo,349184
scipy/interpolate/_rbf.py,sha256=ThyHaf-qmUXMn-5-X7LpFRCIssNW_p3yfL80dCgK6P8,11962
scipy/interpolate/_rbfinterp.py,sha256=W6OeWZP0nbbL9fFNoBdiV80Pu6DA041PAVi3jktmXqs,20135
scipy/interpolate/_rbfinterp_pythran.cp312-win_amd64.dll.a,sha256=3ZLnFiCqRYfSHAR6LzzIOlB28Y0PL2aUd4pBcU4KwUc,1676
scipy/interpolate/_rbfinterp_pythran.cp312-win_amd64.pyd,sha256=SE_PqcQukNmaprfXIxjQqPuGmtsdbwM78vwDbLl-J40,1168384
scipy/interpolate/_rgi.py,sha256=w1EmbTIJETT2ECPBSk5P2s7rocwmJqd1fNPGf2bfP4g,27579
scipy/interpolate/_rgi_cython.cp312-win_amd64.dll.a,sha256=6r0q_Ua8z9521bXMFh8iTIdGzndzSqvB-eAmCUwacz8,1592
scipy/interpolate/_rgi_cython.cp312-win_amd64.pyd,sha256=KiyfKhw2qXRLqmf6FYxj7ka_4Qsf93x_dE22lIKkDRI,233984
scipy/interpolate/dfitpack.cp312-win_amd64.dll.a,sha256=jrPr7g34NMsjkKBLrR5JvgvcRqWqULMfzOc-K9XwP4I,1560
scipy/interpolate/dfitpack.cp312-win_amd64.pyd,sha256=E6exm_l-WwFsrs0Gzvh96vUPY4n1kdoCPKE56GcF7es,662016
scipy/interpolate/fitpack.py,sha256=EZLYybeRB4VuqcAUL3WefJJKBt41LfKwteMNJSJUhlE,988
scipy/interpolate/fitpack2.py,sha256=RQZO0omUVY8_5tIZNuGS0xYnaITMg5gne7b5LvO_QWE,1241
scipy/interpolate/interpnd.cp312-win_amd64.dll.a,sha256=xNck3zO-r52R1U4XICJnfPuZLvKsHbdBojgBte-H2LU,1560
scipy/interpolate/interpnd.cp312-win_amd64.pyd,sha256=F6C0p6_rGB8eTuLPqDRSP1we7ripgVwfyiwhe3t24HY,346112
scipy/interpolate/interpolate.py,sha256=YoEgqQsjxTZxYjlHFXEBHd7f4VpFfzNM0g7Wmh16yao,1232
scipy/interpolate/ndgriddata.py,sha256=68eqSfvBC0To4kWCV0aUUdzXUgWnB2Wi4J5aDufqXOU,945
scipy/interpolate/polyint.py,sha256=_SRqRCIhOcik1iLhLGxJb5RtFna42jDfnJByJ-JGlEI,975
scipy/interpolate/rbf.py,sha256=CD57kmE86z1FxoW7084fL06zYF9tsvVgoAs3dPnqyQI,851
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_rbfinterp.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_rgi.cpython-312.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bsplines.py,sha256=cMgAL_LwcwW1rGZiHOpUYqAdqGPe8ntZhh7ebwiQIDA,62214
scipy/interpolate/tests/test_fitpack.py,sha256=jYFzZ4qnTnbokyc9877tPG_uElrmFKI6xMcIIDbkOtU,16515
scipy/interpolate/tests/test_fitpack2.py,sha256=84rsyQiS_gNcMJDvv9rewPStAJX4y09aBRoG4Ox9egA,60020
scipy/interpolate/tests/test_gil.py,sha256=U7P1Jo7ztDrrkjDRv_2AZb7I2f6hv-c6WyP8UeeNVVE,1939
scipy/interpolate/tests/test_interpnd.py,sha256=r2PBsiLCA5u2Nms1IEftk3iq4NAJOX_NOZNFGS3ZarI,14013
scipy/interpolate/tests/test_interpolate.py,sha256=DjY_meXyYewDeacqVp2y7N5Yb7AFVbiwlEFHh54Zf8c,98301
scipy/interpolate/tests/test_ndgriddata.py,sha256=uYgSi4F10q44J6fALyPurx063zJOLAhYtU23xW2KPfQ,9691
scipy/interpolate/tests/test_pade.py,sha256=BIQhGVfZRFMz9dbAFfmLJFxQTUpRtnf_QshNqcOs4eo,3887
scipy/interpolate/tests/test_polyint.py,sha256=1sQpV01X-8DRVJRXSwCZ3Dr9Py4hJduR6Mgw2YOXdRo,31100
scipy/interpolate/tests/test_rbf.py,sha256=p4TlyxPEMCcKaVKbFpl8foncvs8LCyyBrMkYAj2lYfg,6781
scipy/interpolate/tests/test_rbfinterp.py,sha256=x0Eh1Zlq35104Q8xlxLwvRieqHaPCYR6PLi8-4nRCI0,18634
scipy/interpolate/tests/test_rgi.py,sha256=0s_wzW9XPWc83aPMbDxJg9KV2gdp0GmkAAtJhG9L5Kw,42743
scipy/io/__init__.py,sha256=xW2ULETNsp4fFOCg7HJzRXdj0pHkB9Ye7c7JrRM1NQ4,2837
scipy/io/__pycache__/__init__.cpython-312.pyc,,
scipy/io/__pycache__/_fortran.cpython-312.pyc,,
scipy/io/__pycache__/_idl.cpython-312.pyc,,
scipy/io/__pycache__/_mmio.cpython-312.pyc,,
scipy/io/__pycache__/_netcdf.cpython-312.pyc,,
scipy/io/__pycache__/harwell_boeing.cpython-312.pyc,,
scipy/io/__pycache__/idl.cpython-312.pyc,,
scipy/io/__pycache__/mmio.cpython-312.pyc,,
scipy/io/__pycache__/netcdf.cpython-312.pyc,,
scipy/io/__pycache__/wavfile.cpython-312.pyc,,
scipy/io/_fortran.py,sha256=yAvziQCMkFXUlnA4VJtAaTb7xkt_YGlGpZA3SJc5xkM,11245
scipy/io/_harwell_boeing/__init__.py,sha256=YnsHlxg7HQ-WxR5OOELtfv1JrwYrSkojji4_8hpppBs,591
scipy/io/_harwell_boeing/__pycache__/__init__.cpython-312.pyc,,
scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-312.pyc,,
scipy/io/_harwell_boeing/__pycache__/hb.cpython-312.pyc,,
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=jXzBUiw0z49ecYFayTgPAuW7jYk1MJK8PeaNuyo9nwo,9225
scipy/io/_harwell_boeing/hb.py,sha256=JU8-OnMbUmnblBpWrscABa4S3x63TAWqGGakEDux-Gw,19718
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_fortran_format.cpython-312.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_hb.cpython-312.pyc,,
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=oBT5mfOa0sthUlb4qo_lJnP5BYFj3h8a6HqUZPMTXrk,2434
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=BCfVyKAE3O0ABcfeGfxtGquwhehBGMPKGJl95oC3EIo,2349
scipy/io/_idl.py,sha256=NE-NM0hlO_Y27R2RQinsACQQiSChsDF3yx8gBR-YA5M,27838
scipy/io/_mmio.py,sha256=w3a9L5DhLT7q59TyvhGJmwwM5BKb_WoBl-cp6KmjrOk,32861
scipy/io/_netcdf.py,sha256=sxAnYe8R5wWkzVm2BdMqTelgmkubPX13K6GLHjyqWmk,40173
scipy/io/_test_fortran.cp312-win_amd64.dll.a,sha256=SJe3bw76n_pZpMsLipRZsmUbdO0Ez6rJR0wO9Sk7C6I,1616
scipy/io/_test_fortran.cp312-win_amd64.pyd,sha256=Jw5BwAl-lb7HYKWdmk3XUhWgLzNAOpUFTH0lnnVkeiM,380416
scipy/io/arff/__init__.py,sha256=3F0yvqizJT7Hq5gygpmZw2WMuy0r9aCmb6G2yNNn8-I,833
scipy/io/arff/__pycache__/__init__.cpython-312.pyc,,
scipy/io/arff/__pycache__/_arffread.cpython-312.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-312.pyc,,
scipy/io/arff/_arffread.py,sha256=3fifCNBiodyWWHCz8DthdYPCXfvF9u4wSW6yZnvN49k,27264
scipy/io/arff/arffread.py,sha256=F2uc1fWu_w4GrgZmiO1sUnUd16N_4PeTRIFLuW0v5ws,1400
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-312.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=gzevjIJmmJLocLjOEnQMkZVZuIpfPJLocJdIG4RYHIk,7711
scipy/io/arff/tests/data/missing.arff,sha256=m53l1Himyx0TtVgNLucoz1go8-9BmSE70Ta9bm6dt_k,128
scipy/io/arff/tests/data/nodata.arff,sha256=6oAHQ7CUQEG8h5glXKpCaXH0UABOCEoxs5j50WYPAKU,240
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=rc8IS9thME1NM9tAR-C4IUixLrSpw64YNeHkIRMS6Ng,299
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=NbE6wwE0HkJDomErLPHgPGO6iF2HA1bLpmcamzx3F58,305
scipy/io/arff/tests/data/test1.arff,sha256=NV86lzIKWCwq3UNZmMGt1XK0yurcYmcp0KS9c5rKLkE,201
scipy/io/arff/tests/data/test10.arff,sha256=GbDWnG6LzjsbjYxiarYG4Ft3WlK1rvuM6w3proNFD58,199016
scipy/io/arff/tests/data/test11.arff,sha256=RFWNoWrx2LHJ0izc1bJdmGA2M2L8NT58MwKQN2B-3AI,252
scipy/io/arff/tests/data/test2.arff,sha256=zk5hNFTEcphFXkon2At_FD9ia_lAyzOcSFj4xnJTVPs,315
scipy/io/arff/tests/data/test3.arff,sha256=R2zwCzapweGqylmLD-LXx7GAS957NNJ8emM48hLTwEY,78
scipy/io/arff/tests/data/test4.arff,sha256=KtyokEnupbYZVi-fR7GJtr-hHefP54xgV2l26Nz3YZU,249
scipy/io/arff/tests/data/test5.arff,sha256=_1-QhpmflJKp1polaKwjkeJZsHeho7RzM3_ic2JJykA,391
scipy/io/arff/tests/data/test6.arff,sha256=WOCPN8QRdxCUSfzs-Y-OlJKiUmgLKuDrfy6wsWIFCJU,207
scipy/io/arff/tests/data/test7.arff,sha256=AQdPXYEKEhb10Odd_SDrPcv_gHNR0ymFKnD_91zjFc8,573
scipy/io/arff/tests/data/test8.arff,sha256=YlCbztVn-7x4w41HLIP852BqkKvWBrO9N8Nb0LSR0ZM,440
scipy/io/arff/tests/data/test9.arff,sha256=X0fH-_mz5ScbEvPJV7LJH-7Kbr2RTOlamf2_x18ydY0,324
scipy/io/arff/tests/test_arffread.py,sha256=O34ZYHvbItIRZ6VEZNXYUPs7_Wpz03SLGZaLfiXffLg,13501
scipy/io/harwell_boeing.py,sha256=JGv5F3Li4s8RQbdyDRsYQXh0coa_SOgdZhF8apsvMIU,927
scipy/io/idl.py,sha256=ZAJt7fqvOIGrQUFqI5mdaMq9dG0O-OLHDp62SkEfx6A,823
scipy/io/matlab/__init__.py,sha256=Q2pLqHXC6sl2UM6xbBo5ouvuQYwi7Ky_vf8aQTEBbBA,2085
scipy/io/matlab/__pycache__/__init__.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_byteordercodes.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_mio.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_mio4.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_mio5.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_mio5_params.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_miobase.cpython-312.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio5_utils.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio_utils.cpython-312.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-312.pyc,,
scipy/io/matlab/__pycache__/streams.cpython-312.pyc,,
scipy/io/matlab/_byteordercodes.py,sha256=Bzx5cwfuEKYvZmRA8cZSop_whh7tnH8098yZpXzuyM4,1975
scipy/io/matlab/_mio.py,sha256=iE9AkfS5nqEEZor7XjTzHDvG5431fVjw6wtaTyBAIaw,13157
scipy/io/matlab/_mio4.py,sha256=TNwDJPvw5bRg3oIz98Al1VkmQ5TlNn-dBU7YXfco-10,21235
scipy/io/matlab/_mio5.py,sha256=-pITbDApUG0AWAM4Dh8-SyPKx-EgVtHb2AfttlON2sU,34479
scipy/io/matlab/_mio5_params.py,sha256=b3tn-kJVtf-aYDG_da0nrrAnHkVrP17pfh95SLeZnOU,8479
scipy/io/matlab/_mio5_utils.cp312-win_amd64.dll.a,sha256=KL49bg_VsWP2SvRzYrOvhN7qyZbYdZ-O9vKYoyFCRS0,1592
scipy/io/matlab/_mio5_utils.cp312-win_amd64.pyd,sha256=XeocjKFrWItqSTtiZiFQ20PKd5dC5pCIqebePKCu-Nw,162304
scipy/io/matlab/_mio_utils.cp312-win_amd64.dll.a,sha256=Nes7WkX_5uGbVKquUnVsiavZbK91YKrtqaRzL-K7od4,1580
scipy/io/matlab/_mio_utils.cp312-win_amd64.pyd,sha256=LRxecmApYKlI191xlBMCFRqGI1i8VUwx0k-5yxpqK1c,41472
scipy/io/matlab/_miobase.py,sha256=88DTLye8vPk9Ory-KL15V7q18mnHr5J6RqTJA8UoZao,13302
scipy/io/matlab/_streams.cp312-win_amd64.dll.a,sha256=2f5SpYnFLQDpAHluN5xX324YRqB1HsZsAEweg1OQxcs,1560
scipy/io/matlab/_streams.cp312-win_amd64.pyd,sha256=gCIK_2FAcPzJuXFOs8_WQRga0hthZJo5jMso_ESUut0,98816
scipy/io/matlab/byteordercodes.py,sha256=QGGZ1YElbvWQV3zVz_d-GwlzoCca0hXizqNGTlg93oo,878
scipy/io/matlab/mio.py,sha256=avFPrzEr5G3tofr2ah65kUEariVqkTKavYI23CGXBAk,923
scipy/io/matlab/mio4.py,sha256=KbcOevZ1wx2mNaZenhbBImXrroOeo2F5NZ0Sd3O-8zo,1234
scipy/io/matlab/mio5.py,sha256=PNRaqOyDlooRhxzEcXFhlMcMVg4DJgb6htqQJguHB9I,1472
scipy/io/matlab/mio5_params.py,sha256=Dt10KjCvDtFxKNkM7eqV6fxHy1sQg4rnSqmHxGOsV7A,1563
scipy/io/matlab/mio5_utils.py,sha256=yqyy-8dxdEJxzakOkfqEi5HrE5T5tqD9YazSYSLuSXM,927
scipy/io/matlab/mio_utils.py,sha256=171bvCROOYtLX89rgjW4owHD4JjQ315U86m-07KPoYY,812
scipy/io/matlab/miobase.py,sha256=YmfpvPIhgDx2VzeDrPopeFzL5tqRWYsqnU6kii0CJqg,1019
scipy/io/matlab/streams.py,sha256=0yaJf3hsNv2_i85_MGJ7Kx22f9K-6SBB71f34iLtY8g,836
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-312.pyc,,
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=YKPAsE36MRPDi3Jsh2QrinvLOEvDPmsBylkwUzqoDTg,967
scipy/io/matlab/tests/test_mio.py,sha256=Tju8ok-enFIYhC7V1APv3HHU_e1udHHrY-j5WQSrbfo,45893
scipy/io/matlab/tests/test_mio5_utils.py,sha256=Z1Byr0AJMkdlidOETqIsAJdCZCx0TGqR4OyFI4SDBaY,5568
scipy/io/matlab/tests/test_mio_funcs.py,sha256=JoLN42aZLd-f-kju-kTgRFQjQmEy4VtJlKgJ5EgnVFI,1443
scipy/io/matlab/tests/test_mio_utils.py,sha256=I4_z8My_JDy6jverX4bm-GeDNnnC6jX71hprsGSnCF8,1639
scipy/io/matlab/tests/test_miobase.py,sha256=G3Xm9Q14vWR37Wt7uxxgIBnLvmSkJ86GVABDhlJMflU,1496
scipy/io/matlab/tests/test_pathological.py,sha256=8lcveXzzknQH_009kNTvrf4aAr-qgatrXPEuRZtxQ4w,1088
scipy/io/matlab/tests/test_streams.py,sha256=UtRkoFIJSTRJZZ-gCflcDej0iqFD76rFsOzB4QFbqeQ,7548
scipy/io/mmio.py,sha256=B55fPeX7f85gRK0OhQSyl5jXcexjLJMhmGrCDb9TcrA,807
scipy/io/netcdf.py,sha256=b4qPiQuQAE01Nb0M1YeX63os6MiWIETIf_1B3IbOhvM,1113
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-312.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=S68hJoxMZgGmyEvQZewuii-9txrUMZR8zP0Q6iMFv_8,7808
scipy/io/tests/test_idl.py,sha256=x4utO-ZvIk7FcG5K_Nr3YmVbhHhAX3rDGHf7_ek9jus,20122
scipy/io/tests/test_mmio.py,sha256=hXq7KZHCAH20qgh4V_S4Vi63YH-A1H5A_7nbxxX5xHA,27677
scipy/io/tests/test_netcdf.py,sha256=tfGbFWt7FBQMr34BRi8l7imv8d4wpSYC4QZyohbqkQM,19856
scipy/io/tests/test_paths.py,sha256=yfqQtS6YQkg2SFa4_YbGBqZlIhd-brGut6jSRDtv4yc,3271
scipy/io/tests/test_wavfile.py,sha256=TnsS-_8CoY0ZRFjR7N6E9UYK5A2lCHD5SQH1LOc8d10,15719
scipy/io/wavfile.py,sha256=z782OeGNC4ysiYqqfLSDUkgqHDlNDUIMDC48Q_LK_P0,27482
scipy/linalg.pxd,sha256=SUm9fRHyF3s5-nhG0PWlenOlEEXtMRBEW_5X2q0ufGs,54
scipy/linalg/__init__.py,sha256=EbuO2u-OCvaqh2VavmBAo3p1s_h7wFI7RPpKN5KB-p4,7972
scipy/linalg/__pycache__/__init__.cpython-312.pyc,,
scipy/linalg/__pycache__/_basic.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_cholesky.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_lu.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_qr.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_schur.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_svd.cpython-312.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-312.pyc,,
scipy/linalg/__pycache__/_flinalg_py.cpython-312.pyc,,
scipy/linalg/__pycache__/_interpolative_backend.cpython-312.pyc,,
scipy/linalg/__pycache__/_matfuncs.cpython-312.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-312.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-312.pyc,,
scipy/linalg/__pycache__/_misc.cpython-312.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-312.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-312.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-312.pyc,,
scipy/linalg/__pycache__/_special_matrices.cpython-312.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-312.pyc,,
scipy/linalg/__pycache__/basic.cpython-312.pyc,,
scipy/linalg/__pycache__/blas.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-312.pyc,,
scipy/linalg/__pycache__/flinalg.cpython-312.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-312.pyc,,
scipy/linalg/__pycache__/lapack.cpython-312.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-312.pyc,,
scipy/linalg/__pycache__/misc.cpython-312.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-312.pyc,,
scipy/linalg/_basic.py,sha256=snQdlAGG34woCYSPvJCWd-NPFcTAziZGblZsQ1PUPAM,71247
scipy/linalg/_blas_subroutine_wrappers.f,sha256=xCDifo7qRixvt11wrQR6BvQf3-l6rtH1H4cl1Snhz3U,8219
scipy/linalg/_blas_subroutines.h,sha256=z_4wvAsR2qygLTOQiqbv7PEMc8a1dpVnULvFKLP_VoY,19234
scipy/linalg/_cythonized_array_utils.cp312-win_amd64.dll.a,sha256=5yVtn9gdkilRZBoGtSbsBMZzSn831y6Znwu-qQxlx6c,1736
scipy/linalg/_cythonized_array_utils.cp312-win_amd64.pyd,sha256=_KjT437vsYwJBFeGP_bhLmvNVHpcxY2ulnZXcAenlrU,444416
scipy/linalg/_cythonized_array_utils.pxd,sha256=fyp2eQgNSXX88dHrIV5qUuZ-fTEU-8JdXKOTLagAeK8,930
scipy/linalg/_cythonized_array_utils.pyi,sha256=scRj6ew53DtnyIry4gVV3IsZlbwiNZJeptv_0icKE2g,356
scipy/linalg/_decomp.py,sha256=HuvE-oGxZb-mxqKOlWeo0et14o7ZZeTXW2G7NhkzXMY,62983
scipy/linalg/_decomp_cholesky.py,sha256=qj5SCRMOp6Zrs08DcoP7Nhrs3cD4XKbms_OWvRBWok4,12261
scipy/linalg/_decomp_cossin.py,sha256=jins-Wx6V6Tlp9qi0-NAzz4Eqmtgaurbzo2Mc5eeizE,9317
scipy/linalg/_decomp_ldl.py,sha256=VVA_IY7ol92EisVNUDDjl7KMYjSC8LgfuoPUIkR_sbg,12868
scipy/linalg/_decomp_lu.py,sha256=AlAc5QVLsLt4VKPJg8rSQPyhTcVuEqD7ueO6ZGT2cOQ,12113
scipy/linalg/_decomp_lu_cython.cp312-win_amd64.dll.a,sha256=v53ABGeZDpmALXpoq_qq5ZJQdYypz7Z2yjj1ZW6ZgiY,1664
scipy/linalg/_decomp_lu_cython.cp312-win_amd64.pyd,sha256=-ZMHiwoSoZO0wTLgxrwQh23RkIXYEltYEKr7aNo8gnk,219648
scipy/linalg/_decomp_lu_cython.pyi,sha256=4jzwxV-ttU3rlRbNQQ-gRlRUgGtQAXL7WwgYx5w9aMU,267
scipy/linalg/_decomp_polar.py,sha256=4KAJQAgKoN6W_onEM-m3oGqfwCK3__ASC7UFMFdKcac,3689
scipy/linalg/_decomp_qr.py,sha256=WioKKWcHa753N_JBtSH3BdrppWZi1Dk-gZi7XkpYQ8k,14156
scipy/linalg/_decomp_qz.py,sha256=DRwFa-SbWrlm6JHWIudywZ4LaaxykEkZE66I2ZMjmWs,16785
scipy/linalg/_decomp_schur.py,sha256=gZd4jk0szDOsPS1Fmte4hxGug-BjCgAAgXDUeYvIb44,10654
scipy/linalg/_decomp_svd.py,sha256=gxdVYl-TTPdFV5EgstAfzETs0QgPCcfJuQCDnV__o7U,15404
scipy/linalg/_decomp_update.cp312-win_amd64.dll.a,sha256=CjIMovTr9E1eJo1YzhxwBDz6vnZkv3O3zX4wHc2fuB8,1628
scipy/linalg/_decomp_update.cp312-win_amd64.pyd,sha256=H7XVhmUYSwKadcjM8kwDduiyW3Ayf_LVr9FsgpNO9AA,306176
scipy/linalg/_expm_frechet.py,sha256=M42pBJ5Czz9A3-IVAWguiq2BBk9-9Bsrft6GUhu5e-M,12739
scipy/linalg/_fblas.cp312-win_amd64.dll.a,sha256=QiqnkPxneUoCE28YVgFOPgvpNl6091pYK7EHiMVx2mA,1532
scipy/linalg/_fblas.cp312-win_amd64.pyd,sha256=j1oZWoswkXMseWV3XV6VuXOwV_iNhkGM0Ar3bbtJlVE,596992
scipy/linalg/_flapack.cp312-win_amd64.dll.a,sha256=dLwk4x6GbaYhGLKTfwmY5wCc41LYuN6VuWDPb1Km9pI,1560
scipy/linalg/_flapack.cp312-win_amd64.pyd,sha256=zk_jmfHrY_t5J5fpcYV_ipXDXrKRNTvwfxYLz2L9z50,1885696
scipy/linalg/_flinalg.cp312-win_amd64.dll.a,sha256=nyt6QlPxJ3W6pwtntrZfcbInHddAj77I3QGgE3MyuQQ,1560
scipy/linalg/_flinalg.cp312-win_amd64.pyd,sha256=Ic43x-ClYmA7vSwmPAcZR8I4skwzlhHgzuTHHZg5tSQ,88576
scipy/linalg/_flinalg_py.py,sha256=CgILFUJcTvTxhmLFt8AcJE2K66zREEBvBNtNwHU-KSA,1541
scipy/linalg/_interpolative.cp312-win_amd64.dll.a,sha256=Yx4nbr9bIo8P6HXoJCMT17jtAnAhXF0SR8JuosEUUw0,1628
scipy/linalg/_interpolative.cp312-win_amd64.pyd,sha256=jr4dLZ4utq1_82cOeGZh6NxNfore84kwOYaPAxwE5gw,758272
scipy/linalg/_interpolative_backend.py,sha256=mwdx_kKIlNkWDBeLqx7WnD2ce4UEo2nXqCSyKHny-9s,46873
scipy/linalg/_lapack_subroutine_wrappers.f,sha256=p9h3y4oiQiOBihKeoF08B9R45dV5-MBeUsUFeREkyOg,36415
scipy/linalg/_lapack_subroutines.h,sha256=jzZsEPEbMC3r7z1TqeBDyWxaI1YSk4OXkc_Qt1l4yeM,248359
scipy/linalg/_matfuncs.py,sha256=UvA0BjEQ6MMtLSqhbT6pAw45wROO6l7-mwrYjNh89js,25912
scipy/linalg/_matfuncs_expm.cp312-win_amd64.dll.a,sha256=APc2v0lYDg4iEd9c7vALGclN-yoznBaPtQnAv6Jjv7I,1628
scipy/linalg/_matfuncs_expm.cp312-win_amd64.pyd,sha256=3dmta9e39DMZAM_TlokCr_t2V45WXrj8LmxFv9TrNmA,378880
scipy/linalg/_matfuncs_expm.pyi,sha256=J4OGtavgQPeZXtpBbWDQMH08jKqrdM028ZpWzyEoDfI,193
scipy/linalg/_matfuncs_inv_ssq.py,sha256=cZAyxER2qDrN2lLMuUmN3YqW43uBNF9cAmRTl9TTur8,28916
scipy/linalg/_matfuncs_sqrtm.py,sha256=yJAco5ITC2pdXBEBpwTuirUabIDMZjmue4O3VBwSP84,6868
scipy/linalg/_matfuncs_sqrtm_triu.cp312-win_amd64.dll.a,sha256=DUd70DytUglQDh1X6RoGhp8ki-RffebyyBEorKuOarc,1704
scipy/linalg/_matfuncs_sqrtm_triu.cp312-win_amd64.pyd,sha256=GB3QRLJx19DJzZgPLZXYB_dbat-6wO8eweupX_z4iSQ,214016
scipy/linalg/_misc.py,sha256=GuAl0DgvKf35a2oyvYgcud84O0e3DCQLSUdR2AzXE3k,6474
scipy/linalg/_procrustes.py,sha256=lqEsyqJ6W4R-ZobmK9Iscrb1eJ5kSsD0Alsk1chYuv8,2877
scipy/linalg/_sketches.py,sha256=nVwWE2o7wZw69rFbEDoosoZoNm31jW1iM9fxhdz5BnU,6324
scipy/linalg/_solve_toeplitz.cp312-win_amd64.dll.a,sha256=7a2WE63AWEyy_FfVAq_kuymWB1WdYQtPOE-ptT2upFE,1640
scipy/linalg/_solve_toeplitz.cp312-win_amd64.pyd,sha256=otQLWpvB4Way5NWb3n5E_OrJvxWVlkxKODqMax2Joww,235520
scipy/linalg/_solvers.py,sha256=Q0vWoC62NomzXo9igz5h9YzEHEZ66jeOd2VW7GCBetA,29227
scipy/linalg/_special_matrices.py,sha256=mJTX0X399O-lmxF5IiyQ3axzjp2LxJorMqeof-77cLQ,42109
scipy/linalg/_testutils.py,sha256=ufSOQpa_ELeIj5xOKNcmuJPnr4KbcpisDaUm8sj-JuE,1793
scipy/linalg/basic.py,sha256=4u-n43f4Kd7InroewxBajK6vNOvrYI643v0EGl-g_2Q,1057
scipy/linalg/blas.py,sha256=GVldHtP_iR-moX1Klhr7mTvj-zJWYWFUchIxxGkVokc,12161
scipy/linalg/cython_blas.cp312-win_amd64.dll.a,sha256=idMsdcp62mhKhh-wScSE4lZe7CKQx3oaeSRl7aYdxBQ,1592
scipy/linalg/cython_blas.cp312-win_amd64.pyd,sha256=QSiMbZONQof4_CxzTYiGx4ZqbkvzvND5m_3mw0DHiI0,235008
scipy/linalg/cython_blas.pxd,sha256=iuRww_s-VX6vBgzRU2R8kEt1n5TJ2hfYcK5-UZ8XM1Y,16049
scipy/linalg/cython_blas.pyx,sha256=OOg3Ego-6MJDGoDjM4C_K4yFM8zpMg_ELfkzqGIaZi8,65931
scipy/linalg/cython_lapack.cp312-win_amd64.dll.a,sha256=kP248NmX43-EjJaWJQbzidG0tIEbxnOMs-n59DY9yXQ,1616
scipy/linalg/cython_lapack.cp312-win_amd64.pyd,sha256=8ZcompKMzYNzGZRXLnKOXUVLlG1LlGgunnxYThjtrvc,510976
scipy/linalg/cython_lapack.pxd,sha256=uPXUcQJqwGew9houpmqn1dnffXSUKU04hqt4ss9GbbA,209064
scipy/linalg/cython_lapack.pyx,sha256=LxZVRhD2mCDWcnAj27V1jzf-glDHc_IFpMJ_s8QUS60,710917
scipy/linalg/decomp.py,sha256=s3_tcXlzoQphoOv0ufw79or0q3FIrmLC9spAQLVIStU,1089
scipy/linalg/decomp_cholesky.py,sha256=n---n4edzo4hyF7vhqNHDQVcLFgDUM9G5c-NSB0mNM4,946
scipy/linalg/decomp_lu.py,sha256=5Kst2Svl_NFEWOTO5qTwgo-0E_WboyK_UzR8p_Yxi2k,886
scipy/linalg/decomp_qr.py,sha256=ADq7RNc1RJ7H0-CP5bc7v8PnV1_zVRX7Mh_vphpx8R4,823
scipy/linalg/decomp_schur.py,sha256=LdwZVICTCzbhg6pqpFBJVlCUcyRq3Y8M3C9WknpDFDI,910
scipy/linalg/decomp_svd.py,sha256=_Fiu3kLKkJJkS0HiIzNtm2Ew3icRz2kwZye9R2cayJc,878
scipy/linalg/flinalg.py,sha256=3he_hg2IyL7iPo084U7ed_PtSgF8nnaNuf4M6vhvOaI,700
scipy/linalg/interpolative.py,sha256=HNVj0Du9g71gcPDt3j6TziQW8WfMriCkZ2NuS3ExVP0,33200
scipy/linalg/lapack.py,sha256=IdhupdY73PhU_ZpR78VtBW5UopMhZXocK1QileIYAxU,16662
scipy/linalg/matfuncs.py,sha256=GFLrA47eIBKkBlU6i7cJLAR-oWG_O7mKvSA6CM770kQ,1130
scipy/linalg/misc.py,sha256=D4sUpr3GSPGub5WuJF4YZeP2XjAGYLqC2Yk0u-WDmuY,827
scipy/linalg/special_matrices.py,sha256=Vojq6AIpQVJZvsaaEDzIimjK3BZeNGHfybpa0_7knyU,1056
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_cythonized_array_utils.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_lu.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_misc.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-312.pyc,,
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=lleVCafSedJWd7kSTokXEZuium-NwxeYJeacMdTxork,71126
scipy/linalg/tests/test_blas.py,sha256=RkJSFRsLoW1ZfZZ5h_XM89RwgVn49sE7CqzaMVTvI4s,41302
scipy/linalg/tests/test_cython_blas.py,sha256=w-Xg6YtJKzlhfD5EKninnVnvmwJuDYnffM-kzotXoCE,4205
scipy/linalg/tests/test_cython_lapack.py,sha256=L92_DkwZQyPDplzVwrdbMDLCHaXu5WPACwG-dwACGC0,591
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=uGVyBm288GVIieUXpc0Iu81z9u8Vc6Q6neIN2aCnIvE,3961
scipy/linalg/tests/test_decomp.py,sha256=bWDQzDBaNyixCKs0FiQ_D3o--Ebsz1flu_50kRTOYm8,105805
scipy/linalg/tests/test_decomp_cholesky.py,sha256=FtAiYQKLuej7DyViryuIMOTfmlnYoW3sT0zqSfgTP4I,7467
scipy/linalg/tests/test_decomp_cossin.py,sha256=2hK8yN25BPcsjX6sI_Sb9Fiie203iRBdmKGAFWETO78,5927
scipy/linalg/tests/test_decomp_ldl.py,sha256=n8mc2SWqtRzMjDkJJT6n6F5cWtPDP3SbBadLZ6--D2A,5069
scipy/linalg/tests/test_decomp_lu.py,sha256=5SDUfPF-VUpUYEs604H4JxIQKkC5MtYOhXeIGXjpVRA,11453
scipy/linalg/tests/test_decomp_polar.py,sha256=dx9ubrGOCBZW2IVPBveUt6v6wWldGUOrh0MzFlB6h7w,2736
scipy/linalg/tests/test_decomp_update.py,sha256=FMBxtw8KdQ8s3Cj0XMc3nxRQkbsMn6JLsKGQHwGiN14,70186
scipy/linalg/tests/test_fblas.py,sha256=oJVVXNpZHFtLOrjaqFsI-m-Eyaq32F7tZlWeZiqc9xA,19292
scipy/linalg/tests/test_interpolative.py,sha256=n3KneEG0Yo1UYgr54PDlEjNx9GkBq1wfgA5pbU63mgA,9197
scipy/linalg/tests/test_lapack.py,sha256=OD6aqPDbO9PisymKKmGu5xlZD1UL-GMe7r748qNzVjM,128773
scipy/linalg/tests/test_matfuncs.py,sha256=qduXczSjnGLm0O5barTr6mODOTsMPgo3SQ5FhtwJkIo,39667
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=jrzob-NZ16k_1cYe-7xqI5L8ic7VMYxVlqG7IyiMTy4,3995
scipy/linalg/tests/test_misc.py,sha256=k0264gAdjSMDmE4KRZcMztRKkkwWuzblNOrAD4K17Ec,81
scipy/linalg/tests/test_procrustes.py,sha256=JalCsDisOMEY2lD9RSHfSU6X_W4wVmev_9BZpn8VAko,6949
scipy/linalg/tests/test_sketches.py,sha256=RMUKNRQTj1o08CGHGf68bsES-jzLuN0AswGG5aNXsk0,4078
scipy/linalg/tests/test_solve_toeplitz.py,sha256=1y0Vec8ZOK1tmjclVMXVxCJJ8ZgO_s4TguYRFawrKcU,4131
scipy/linalg/tests/test_solvers.py,sha256=p4RkivhRqc1fvcIKPwBvI4ExoswMyefP0L04CKCXEsc,32332
scipy/linalg/tests/test_special_matrices.py,sha256=UflM-fIkzmALMcV0buNwq50INpvF-F24PZ-uf3jGo7E,27749
scipy/misc/__init__.py,sha256=yGB5XvVr2KLtK1e40nBG1TTne-7oxFrwSYufByVeUDE,1793
scipy/misc/__pycache__/__init__.cpython-312.pyc,,
scipy/misc/__pycache__/_common.cpython-312.pyc,,
scipy/misc/__pycache__/common.cpython-312.pyc,,
scipy/misc/__pycache__/doccer.cpython-312.pyc,,
scipy/misc/_common.py,sha256=ZXc4BohxuQbN0NZVAcsnYAn58rJ4mchMkIR1c_-6Yds,11462
scipy/misc/ascent.dat,sha256=6KhJOUhEY6uAUa7cW0CqJiqzOpHWRYps0TxqHK1aAj0,527630
scipy/misc/common.py,sha256=emcNIDg_5jhB5ext7Y8sPbtMfbSTtPRrzAQCDgzKDBg,898
scipy/misc/doccer.py,sha256=muEPqmmwNGq3eYxz0fVBbqO2TY9djYJ5iEKZlzXAPq8,795
scipy/misc/ecg.dat,sha256=8grTNl-5t_hF0OXEi2_mcIE3fuRmw6Igt_afNciVi68,119035
scipy/misc/face.dat,sha256=nYsLTQgTE-K0hXSMdwRy5ale0XOBRog9hMcDBJPoKIY,1581821
scipy/misc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/misc/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/misc/tests/__pycache__/test_common.cpython-312.pyc,,
scipy/misc/tests/__pycache__/test_config.cpython-312.pyc,,
scipy/misc/tests/__pycache__/test_doccer.cpython-312.pyc,,
scipy/misc/tests/test_common.py,sha256=rfcxQnBUc22G95MDYoYRmIciDjL8Pgmrf0fbMp-pqGs,859
scipy/misc/tests/test_config.py,sha256=Ma6jMr-KuIuDdFFz88ZHmFcawmOugzfJnspZv9Aqda4,1288
scipy/misc/tests/test_doccer.py,sha256=CWV_zZfJCz88jyPJ-pcIk8hMXxmXh_f58LgJjjBbJzg,3872
scipy/ndimage/__init__.py,sha256=d3GLXQWYFAp4l1jLR4uTHe8CTuIWxLQPrV1kDyaciLc,5324
scipy/ndimage/__pycache__/__init__.cpython-312.pyc,,
scipy/ndimage/__pycache__/_filters.cpython-312.pyc,,
scipy/ndimage/__pycache__/_fourier.cpython-312.pyc,,
scipy/ndimage/__pycache__/_interpolation.cpython-312.pyc,,
scipy/ndimage/__pycache__/_measurements.cpython-312.pyc,,
scipy/ndimage/__pycache__/_morphology.cpython-312.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-312.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-312.pyc,,
scipy/ndimage/__pycache__/filters.cpython-312.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-312.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-312.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-312.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-312.pyc,,
scipy/ndimage/_ctest.cp312-win_amd64.dll.a,sha256=7UEs8YIvOrbvuU75aISU38SwO-P9F--_T5clZN0VEyM,1532
scipy/ndimage/_ctest.cp312-win_amd64.pyd,sha256=rD_CKFPDx0kmMTACbAMGkeJtGbm9r2lbWZ70QBex-yg,16896
scipy/ndimage/_cytest.cp312-win_amd64.dll.a,sha256=hoXVDqmby4BBZtvJWZEIb8L0pqnYL0oTvrslhKMWk7Y,1544
scipy/ndimage/_cytest.cp312-win_amd64.pyd,sha256=8MKd-qhK6AzyxUyuIjQ1VwRuNAQepEa_p9FJ0wdjZ-4,49152
scipy/ndimage/_filters.py,sha256=OD-JLgcAydge6ezCJ8s_0OmJRSIZJ3P7Zb3MvxQEHDY,65504
scipy/ndimage/_fourier.py,sha256=qECDrOx4UOXmnfMU7PEmP0bWlRBnK_YkLgxAcvdGMGc,11697
scipy/ndimage/_interpolation.py,sha256=N4FVsKcKgyQ6KyFOH340rEEAmDcZ0N8uNdABMUVpQpA,36401
scipy/ndimage/_measurements.py,sha256=qicFowvEryhQw300q-1l5Ph8qNGMyTWR7iYrE7Fo3qo,57689
scipy/ndimage/_morphology.py,sha256=x1VgbQ3ZsOphdL0mPSBokbx0EwvcZbxXc-0rkf62E08,97353
scipy/ndimage/_nd_image.cp312-win_amd64.dll.a,sha256=DN30mcubodNRn84svw8a3p3Q4_c6agnqGiESxFCZAic,1568
scipy/ndimage/_nd_image.cp312-win_amd64.pyd,sha256=yCfqT7Lcs3BHAMcPDDqyaHFJdxmReWcX1HXXCOCXGBw,186368
scipy/ndimage/_ni_docstrings.py,sha256=yq3jX_2G9UdXeWtMWv8cbr5EA3GBPN0yGHArqInqzT8,8724
scipy/ndimage/_ni_label.cp312-win_amd64.dll.a,sha256=XRbllC7IqHD3fo47HrGqAHg7laA3fAbky0l31B421-Q,1568
scipy/ndimage/_ni_label.cp312-win_amd64.pyd,sha256=9lcCwHWLdvKzC2jQTnUNCNVWaRw9lDUmiRQUlnjHe8Y,335360
scipy/ndimage/_ni_support.py,sha256=1Rg2HLGz1g92fNRWfTr--sKHnq7VLr5sCAIOZGFcB8s,4646
scipy/ndimage/filters.py,sha256=yFvle_r_zDEQDDzQUCBVvAIPz04nhTRN9r0wfT4bxzQ,1252
scipy/ndimage/fourier.py,sha256=XJ6JMT-mn5xTcJwSttqA8trKxsU2JgHzPiNXVFeBffM,869
scipy/ndimage/interpolation.py,sha256=u6pLDi-EdABzAd6ZiRRm1cOONGoyTw1q1OOKRk47Noo,964
scipy/ndimage/measurements.py,sha256=TZeNw84wyRAnye-fAADygOQVbS8U-1vqP1grgkFOHbA,1047
scipy/ndimage/morphology.py,sha256=xzmNiSWirVonCOsfXEjvFv8pSL_2tU8iGFH8gLxXpiI,1223
scipy/ndimage/tests/__init__.py,sha256=2UZQ_2Z-qHqwW95H7CL1wMcx-3TLvdR8qkBZnB9B_do,440
scipy/ndimage/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-312.pyc,,
scipy/ndimage/tests/data/label_inputs.txt,sha256=oBKOjlyOqkDERFefuyjlsPeVfGRyfmDc-uiawuMey4A,315
scipy/ndimage/tests/data/label_results.txt,sha256=hNPE8YOPm5MZ169I4c5JnG2cWxleOlKddG1VdA-Fn3Y,4603
scipy/ndimage/tests/data/label_strels.txt,sha256=fLjLCCb72QdX55kKssZdwg262SaV5NgdIG_Bn_gPHSg,294
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=5Q_hujml-UHMXAIPoUl8YtA_WBOLEJyT8m_QAZMMSk8,3546
scipy/ndimage/tests/test_datatypes.py,sha256=V_so7DMOR0qvkIEfhy_c3kcn1VXs8tDdp86PpbRSyQE,2808
scipy/ndimage/tests/test_filters.py,sha256=WqQFljzOZypoaIDv4ICMoNQz0vMNUn6PuGK5hAZ3PXE,95503
scipy/ndimage/tests/test_fourier.py,sha256=YI64WKLr4dJKXMT_o0J5C0C90Ojdbm9HGTx6AhE2zl8,6815
scipy/ndimage/tests/test_interpolation.py,sha256=kgFq2LAiZINPmVqSgL69MFB9rqt2gSJqueqg7ZPR3U0,56098
scipy/ndimage/tests/test_measurements.py,sha256=wde87e08dxA81HiXCV9lp3ol6DEL4XPak2TYmgutK-w,49174
scipy/ndimage/tests/test_morphology.py,sha256=qQkKhqGfZpOCVZ2vs2jBziZVEoxFJypVZff-9Ik0-6o,109081
scipy/ndimage/tests/test_splines.py,sha256=r1rRYS7GD0ZsQWvuobXIBBC1Mh1MdHusr68vZLdhXBU,2264
scipy/odr/__init__.py,sha256=gHT9GMAwFz2ewCC0B1kTenNLG6g8CHNm__5z6vtF_zc,4456
scipy/odr/__odrpack.cp312-win_amd64.dll.a,sha256=XbkYURrDvwyKiSkHRtfbxeWgH7n1d8iLw2zlfJ1ReYE,1568
scipy/odr/__odrpack.cp312-win_amd64.pyd,sha256=DHqeGpsI2T9Hv3kMEB3LUFQ0W2fI5hCMvaXBM5VOPQs,565760
scipy/odr/__pycache__/__init__.cpython-312.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-312.pyc,,
scipy/odr/__pycache__/_models.cpython-312.pyc,,
scipy/odr/__pycache__/_odrpack.cpython-312.pyc,,
scipy/odr/__pycache__/models.cpython-312.pyc,,
scipy/odr/__pycache__/odrpack.cpython-312.pyc,,
scipy/odr/_add_newdocs.py,sha256=r5m9cNR3ZxZ9Wxcpff4l4yyhow_iUTxRd4ituHbLAHk,1120
scipy/odr/_models.py,sha256=Mi8SuWoWXIFqGi3BfgMSc_eu1GQfGRtXSvkeI9fblWQ,8115
scipy/odr/_odrpack.py,sha256=CV-6nNnRQLHwLULtsx589X6X91dV-JL0hcFMcxzUY3A,43609
scipy/odr/models.py,sha256=rTsyLTTPj-tdnAFa3SaeEslT0bCDbWj1pD_CXEXPwD4,821
scipy/odr/odrpack.py,sha256=YBv1y2mGkw1p5Nl1yCLrdBu6aCAcYSZ2fX_ULJVe0h8,866
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-312.pyc,,
scipy/odr/tests/test_odr.py,sha256=RVM8IOExa_tZIdEjMDR-txR_uLlOaL3RRnTUK4X-x-k,21563
scipy/optimize.pxd,sha256=Go47l_Tk8YvXQxnuJNN6UmDEApEKgBUrplitjO4tsUM,40
scipy/optimize/README,sha256=xkkjdG-1gDRGLV1D5cnLkV8yEyDvO3wiwSS4zHa4mhI,3503
scipy/optimize/__init__.py,sha256=Nfu_jMPKpf9xno5XJ9trLIgoOWthPQGys_rwVbkIMco,13326
scipy/optimize/__nnls.cp312-win_amd64.dll.a,sha256=IqDXKQIUtWyGg36Zgo6qJkAwzfURRiw8jHO1dtS19yo,1532
scipy/optimize/__nnls.cp312-win_amd64.pyd,sha256=MtxKyKidDMZD5_tCAJHNOUHqU7AMhPvDOJEq312fg_s,381952
scipy/optimize/__nnls.pyi,sha256=-tUjlvEr3MsISy-iOz6WkLQNlx0236m4_MbOnH84P_0,442
scipy/optimize/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-312.pyc,,
scipy/optimize/__pycache__/_cobyla_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-312.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-312.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-312.pyc,,
scipy/optimize/__pycache__/_direct_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-312.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-312.pyc,,
scipy/optimize/__pycache__/_lbfgsb_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_linesearch.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-312.pyc,,
scipy/optimize/__pycache__/_milp.cpython-312.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-312.pyc,,
scipy/optimize/__pycache__/_minpack_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-312.pyc,,
scipy/optimize/__pycache__/_nonlin.cpython-312.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-312.pyc,,
scipy/optimize/__pycache__/_optimize.cpython-312.pyc,,
scipy/optimize/__pycache__/_qap.cpython-312.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-312.pyc,,
scipy/optimize/__pycache__/_root.cpython-312.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-312.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-312.pyc,,
scipy/optimize/__pycache__/_slsqp_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-312.pyc,,
scipy/optimize/__pycache__/_tnc.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-312.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-312.pyc,,
scipy/optimize/__pycache__/_zeros_py.cpython-312.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-312.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-312.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-312.pyc,,
scipy/optimize/__pycache__/minpack.cpython-312.pyc,,
scipy/optimize/__pycache__/minpack2.cpython-312.pyc,,
scipy/optimize/__pycache__/moduleTNC.cpython-312.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-312.pyc,,
scipy/optimize/__pycache__/optimize.cpython-312.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-312.pyc,,
scipy/optimize/__pycache__/tnc.cpython-312.pyc,,
scipy/optimize/__pycache__/zeros.cpython-312.pyc,,
scipy/optimize/_basinhopping.py,sha256=-SLnxu5QYU9Y-Uxa-x1AGkQU4GflsYYsr1mcaDt_FEE,31410
scipy/optimize/_bglu_dense.cp312-win_amd64.dll.a,sha256=k3LY5LhXzUQGDSZYRHFv-eXv2lqSbIJpCtIZ-U3hzO8,1592
scipy/optimize/_bglu_dense.cp312-win_amd64.pyd,sha256=l0RNTkhAqCUnWmKjhqgVgekz7Fo6q0DDsQxrRohDcFg,280576
scipy/optimize/_cobyla.cp312-win_amd64.dll.a,sha256=X3lT77boyOOU1KpKJyhQQ6_oTRqbQfA9vAW4IERKnXc,1544
scipy/optimize/_cobyla.cp312-win_amd64.pyd,sha256=2YOSkEprbi8o64CD97_6bc2-m1rtDYotgVxbFdCIAeM,418304
scipy/optimize/_cobyla_py.py,sha256=SriSDVYibtAr8QE9bTAJF1umO-1-pN54K7UCu0CnbLk,11184
scipy/optimize/_constraints.py,sha256=CdSMEh-paBH7UXgT-VFHf2pgOGUr0qLxOdFcN3Nsmvo,23136
scipy/optimize/_differentiable_functions.py,sha256=g-hDHp6rL3hTIPlcc2vrWOmXchCEoj-XmxybBSnJpZ4,23335
scipy/optimize/_differentialevolution.py,sha256=l56dU-A-3X9yYt-p9SAugkwWVcxA_SqXmRMtgN3J3hY,76285
scipy/optimize/_direct.cp312-win_amd64.dll.a,sha256=ooD7uBku_9dyS5hR98Ycb0N27bxTw1xV7qfENJBOGyQ,1544
scipy/optimize/_direct.cp312-win_amd64.pyd,sha256=kY1CS6xMftia2WJ759lO0grPh-YpCCVfhLnArWTxB54,70144
scipy/optimize/_direct_py.py,sha256=kRtd45Akip2Z-lzRzoLdOkJoxAkr9wTLVJArnpfq8bY,12076
scipy/optimize/_dual_annealing.py,sha256=gYQ0ibYOSGLSbIWu1ML4VF43-rasMuQe9ikSJ1aBTw8,31079
scipy/optimize/_group_columns.cp312-win_amd64.dll.a,sha256=NqQnLlDG0aQrBCy09cmEHUVShVpWrFTabMWP8tv7LCo,1628
scipy/optimize/_group_columns.cp312-win_amd64.pyd,sha256=Q42NyYGvQccUF8vd4qmDYXNdx66ugFrM8UVQYfisUUI,1028096
scipy/optimize/_hessian_update_strategy.py,sha256=2ZwhDFIwD83WgaLW6CaDfCu3F5r4WvkG5SqsY2QrK8o,16259
scipy/optimize/_highs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highs/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_highs/_highs_constants.cp312-win_amd64.dll.a,sha256=k_PGSVAD8lS3s1EBmzBkIB2ngHj3DaE3MrBTxD2nd9Q,1656
scipy/optimize/_highs/_highs_constants.cp312-win_amd64.pyd,sha256=K-rNgGmlsirnR_Jt2vB5TWEg-wD5aXDHdD4rWjLgii8,946688
scipy/optimize/_highs/_highs_wrapper.cp312-win_amd64.dll.a,sha256=DupIr1oHFhJQvMiUg5aWXpiRnZG0QJ8okb6l77jjhmg,1628
scipy/optimize/_highs/_highs_wrapper.cp312-win_amd64.pyd,sha256=UrKccJm_7S4JQHMb1WGbol1aLpXcaD1VytTEUD56rPI,4510720
scipy/optimize/_highs/src/cython/HConst.pxd,sha256=cHJGLWN3PFKwA4TPcf_Ysy33-q50GE7x7E4cm5WPaj0,5644
scipy/optimize/_highs/src/cython/Highs.pxd,sha256=mrfP6YO9VNV-to9MdP6132ov34FVV03sM2kV18m2nE0,2230
scipy/optimize/_highs/src/cython/HighsIO.pxd,sha256=yfm3BoLQBanrW6yqNqX1jtbTlqM_z4GVm9FgR3HqR78,752
scipy/optimize/_highs/src/cython/HighsInfo.pxd,sha256=kX_fiVNr5R-PNRNa3DEWdW_TSBemRaN9P-3n4TkMV04,784
scipy/optimize/_highs/src/cython/HighsLp.pxd,sha256=72mPP_GTwA1u9IIAJYVbm5p6-llcV2VAkm6mmcAAeJo,1179
scipy/optimize/_highs/src/cython/HighsLpUtils.pxd,sha256=9_ZVs291VCA792pOg4wlufiThs5Ko5tpMi4wKTERy8w,325
scipy/optimize/_highs/src/cython/HighsModelUtils.pxd,sha256=Zc1Kw5QACiT7F_NRdf65jjgm4BZDU_QPnjJd7785aO0,372
scipy/optimize/_highs/src/cython/HighsOptions.pxd,sha256=pWrLtCo_XElAPNgRy_rORfulWUdLKRX0_QrobBQlFfk,3297
scipy/optimize/_highs/src/cython/HighsRuntimeOptions.pxd,sha256=jQGo5ge0zLpsTOwHUSD4U-AXaSp6v3qRoWBAwdhq558,297
scipy/optimize/_highs/src/cython/HighsStatus.pxd,sha256=mmeURT3hjZvNPqMS5scMlvoz0OolBOh7TIaIOsb1Hu8,378
scipy/optimize/_highs/src/cython/SimplexConst.pxd,sha256=8UBis1MSfyd6yABST2hcm-j3A2MDPvWMS80r_8W5DR8,5140
scipy/optimize/_highs/src/cython/highs_c_api.pxd,sha256=UqxNL_2JCWCmwNJ9W22NMrpzl6fU6GPfaUxdWzpV6CA,366
scipy/optimize/_lbfgsb.cp312-win_amd64.dll.a,sha256=hFA2t-ZBpF4Kp2R38cgbJdLBlW2aDVMAKKrlpvmlVoM,1544
scipy/optimize/_lbfgsb.cp312-win_amd64.pyd,sha256=pRqpEqKbwAG7E8uibAdzXEzVM-qKhhiSvYqC7C7SVog,436224
scipy/optimize/_lbfgsb_py.py,sha256=Uo43tsNx0PQb1zJgps5240wNr1zn4TlamdqlUX4-Rk0,19598
scipy/optimize/_linesearch.py,sha256=OXvdyCPalfACKaheNCk_yU-v8u0yn2LEpu9zXuojv6I,27934
scipy/optimize/_linprog.py,sha256=NLfE0T3OEkDQILWau-EWHZilfpy7CfcM50lSNeBwg3Q,30378
scipy/optimize/_linprog_doc.py,sha256=4W-9H6KNynYC8zZ6X-r0yZok3as1JqIVzwu5JGqLT2Q,63377
scipy/optimize/_linprog_highs.py,sha256=iSZSu_Kh246CFOjQ1dgZkhuZ_9xaUv5SLgN9OgvDm1w,18011
scipy/optimize/_linprog_ip.py,sha256=ypcdES525eAqj4gxYz9qE1G1EVUeLRMZgeN1dzFRuek,46874
scipy/optimize/_linprog_rs.py,sha256=i98FNQoswOPg7iVvfQBGtg206CPPeqbBlgcHb6AC3tI,23721
scipy/optimize/_linprog_simplex.py,sha256=8DfC4-k9uW8l7Oxl50Lg1bg6BQdrWK-PnWvA6PE_BP8,25386
scipy/optimize/_linprog_util.py,sha256=R9MZt2bX7-5xIGbXgOOlBR6LS3Iv49kLm6HHJt2I-ks,64295
scipy/optimize/_lsap.cp312-win_amd64.dll.a,sha256=9bwxltlRlpWmUnqYs55BYz7L2-aWCjhz-musg2qzn2w,1520
scipy/optimize/_lsap.cp312-win_amd64.pyd,sha256=Gh5MfFQor9-n2KH-axYl8SH-cgqV3dzFQVaQUiwzKec,177664
scipy/optimize/_lsq/__init__.py,sha256=c4V-tnMyi2dXNNXkjFQ8SdvGANtgbrqXEM-OpdBdZpQ,177
scipy/optimize/_lsq/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-312.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=h9bYh3astIRvMtMGghtEGpG5fDsh74SMRbmgF3HN8YM,5378
scipy/optimize/_lsq/common.py,sha256=DxkAgGY3hlB0gAo1i8keDBcq1P-DSk7UkL3TIoXbWg4,21282
scipy/optimize/_lsq/dogbox.py,sha256=KuyTxgJPRumBkrk8KlgZZVhm8tpdmsGNBtPAsj2U1Xs,12013
scipy/optimize/_lsq/givens_elimination.cp312-win_amd64.dll.a,sha256=n-eLup6rwvP61PcuM2qPuSDbJuEyQXz6udoDipWEYrQ,1676
scipy/optimize/_lsq/givens_elimination.cp312-win_amd64.pyd,sha256=IkPhnWi1S6m8g14MK0uVJcN1sV0GVrlI03DA0wllqxE,182784
scipy/optimize/_lsq/least_squares.py,sha256=Q4wlTEzL0WhFY6KrDCl3-7AWvql26Bga9msBP4TnM9c,40494
scipy/optimize/_lsq/lsq_linear.py,sha256=ndBs9sy8vnSBPOnMHkKdgwjYFKAL2kzTM-DL6CuKj5g,15579
scipy/optimize/_lsq/trf.py,sha256=BBsxUjWZjzdVILU-Zr98t3WiySH_MUIp-unCQlRAo6U,20037
scipy/optimize/_lsq/trf_linear.py,sha256=GEMGyMqWwaVLUa7sCo9iwDmUkuT746oci1iK6KjnJQA,7891
scipy/optimize/_milp.py,sha256=IRZj53pgXvmqCcWdRHT8Ovl9f_Vxzj2A1PJiPYOvrk8,15520
scipy/optimize/_minimize.py,sha256=lQySPWSd1CKRC1oEe774uRG-V3VdzU84a7Af0AwX9Vs,48797
scipy/optimize/_minpack.cp312-win_amd64.dll.a,sha256=op4b_Suer2794MH1KuS7EuGFf8atFh2Pbmf0MsleNAc,1560
scipy/optimize/_minpack.cp312-win_amd64.pyd,sha256=cGFHNk0_IIdUklJchaE4MpigC9C_8cZzigIkJFtYs3M,109056
scipy/optimize/_minpack2.cp312-win_amd64.dll.a,sha256=d8vDOBAypkntjZGTfRWlUa4CsLUcv4yfHtqHKplQIZI,1568
scipy/optimize/_minpack2.cp312-win_amd64.pyd,sha256=sWg1S1MpmKJrbjh91t7kGOOvG_OrPpap9p2VkpaxXfs,74240
scipy/optimize/_minpack_py.py,sha256=CMSM3En3HoN-mp_vD5W93fEhPJBDVrt3FL3aZg3OR0U,44152
scipy/optimize/_moduleTNC.cp312-win_amd64.dll.a,sha256=mULCyHbEXfbatGqM9sKnQkEHDGRgX83Y9AXkgsD-We4,1580
scipy/optimize/_moduleTNC.cp312-win_amd64.pyd,sha256=7myTBHucq1fuEJNGg3-Gjech-S5hawNYitQVNSPQh4A,138752
scipy/optimize/_nnls.py,sha256=iA2A9oS6prgZmVeYpu-xo0BmCd1J7nAT5eelTgLjGEA,2367
scipy/optimize/_nonlin.py,sha256=iPREGhH-3arh916UhJgE01_oex7-29uBEiC6iWvyCHQ,50598
scipy/optimize/_numdiff.py,sha256=c2MFaF7yjBjV7d7pqA7m5jGgTtkctW8RWLUfdIElUZg,29040
scipy/optimize/_optimize.py,sha256=VfM8USRnMmUZOPzF2WcipPjT4Qhx7dHNRtVJXorfSGQ,150286
scipy/optimize/_qap.py,sha256=lGe7ogeqUVEs-dUTAr2tumGUCjiNOJFcCQNch1k6OGA,28382
scipy/optimize/_remove_redundancy.py,sha256=kIpdqqpum9ZGyBNbf6cHhNRvxaYmSJI-RvD_qGA8AKw,19289
scipy/optimize/_root.py,sha256=-NjPsHfl7nXDEXtoJXDSuk_kWVsmXbVLVYSJxOAv_I4,28998
scipy/optimize/_root_scalar.py,sha256=_34fzUUA_CWSIiEZ1e1SNbgp0MJ7U398si9Gi4XOE8s,20081
scipy/optimize/_shgo.py,sha256=fIhfsuujG1WwNankWRCMzUDaUuUgqvdB8nOajY1Clts,63828
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_complex.cpython-312.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_vertex.cpython-312.pyc,,
scipy/optimize/_shgo_lib/_complex.py,sha256=LOGNc9cYxxA6lbTWuBQEmiptY5XPkSkZ6-rbFBjiKzQ,51578
scipy/optimize/_shgo_lib/_vertex.py,sha256=BcS4hZHQN1S4xsov95t9U501S7qJEJupc_WLQOPCYOs,14457
scipy/optimize/_slsqp.cp312-win_amd64.dll.a,sha256=5jkAQQK0ETeKiMm7wKXhq1YRsIVPviIxOno-MzJ9jbc,1532
scipy/optimize/_slsqp.cp312-win_amd64.pyd,sha256=3oJwPvkFqmr9-4_nP4Jju5SFSDk1rhDUN2zzl2bLoZg,107520
scipy/optimize/_slsqp_py.py,sha256=MYjj-U2eeqLw1WPHPbp37veTCs-VtsZAh1kzpTFJbEE,19271
scipy/optimize/_spectral.py,sha256=I1e8hHPV0j58LoyqHlIWD-rgfi644dRraDkM-Ol91-0,8177
scipy/optimize/_tnc.py,sha256=ELitw11aJtPT8m1YLPR4kuvDwWX9MGuxepPRk65PEHc,17101
scipy/optimize/_trlib/__init__.py,sha256=5TbV8UJSSEwW_qONgzozoHDgvhhugzqykJzybDCrNj0,536
scipy/optimize/_trlib/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_trlib/_trlib.cp312-win_amd64.dll.a,sha256=P5VL16N0SVg5pnaiPUYl1-n9_-lNvigyZ5hkL8LxRoM,1532
scipy/optimize/_trlib/_trlib.cp312-win_amd64.pyd,sha256=eYF0KOwleBCr5K7aEbAlUqsDm5IXDiZfSduXZT_sTSY,313856
scipy/optimize/_trustregion.py,sha256=seRX6t6zSEenPWH29LuR13NUs2h6KJ-q0DQUGcTAUdo,11090
scipy/optimize/_trustregion_constr/__init__.py,sha256=Y2OLn2HBQ5rxpAfI-UjWZc4j_sDqjOBGmgdS2zAC_24,186
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=pYMm2qLrCrvpD7pQZAZpz1H7Zfag3uiwezGBCDx6syk,12928
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=uuZ-Jtgl6G6sew7J2aeA4pERgAQ6tPtvvebGLBdNFUQ,8809
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=GkloTNfoZpPSmyOC7E91BEUU-ffCAaiI53UnwwrzWec,26308
scipy/optimize/_trustregion_constr/projections.py,sha256=YOPzrg8CMqhn1cXq-ykqqlYSC1OYkwyB-YTCGhVsaoo,13510
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=kzhhoadtUgeFmSoBKerKMnSgMSSpzHc_m43hkgqjTlg,23229
scipy/optimize/_trustregion_constr/report.py,sha256=Fvc4n77YuGiRQuGxiDIgkvkgScq5dqko07g7zJOwj-w,1869
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=z8MKN6NbaxsH7pj2rbCGdOQ4jXE-eM-SPu_IcNMtIaI,10165
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=YQ5iAUYXSTKZusnJxG8yD3Ctyb2qlCPm9IAk8y8pkAY,9048
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=a9VYYB-PIUgNZht7COhFkVseeSMo3rdK8jO2QI4VTYg,28364
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=DWLQhuc4K6-UxV810cLlbTFQg7i7Ueja1XHZMWt4DsU,1102
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=2EAitOYVUPycT9CfGSrugYh0zOz0rhr9cV1kwKM6C2U,14148
scipy/optimize/_trustregion_dogleg.py,sha256=XTvNQtebZTPzl6xGtzHBLDcFD2qe5sSOFOTbmtDWswo,4511
scipy/optimize/_trustregion_exact.py,sha256=VVPh7pr5ZJRFdogGZSRou6At1TNrH7VxMqynvWeLnTQ,15843
scipy/optimize/_trustregion_krylov.py,sha256=OE6ABN1_oeRUUBXWGBA2QFlzxO4GJVBseoNOMB2b4Sw,3095
scipy/optimize/_trustregion_ncg.py,sha256=O2_8p92l_faUHLI26GHK2wV0RTdAR1scli0XhXTTsSo,4706
scipy/optimize/_tstutils.py,sha256=kW7yKhnvDcjIFXFIZ6OEKyq9Ug1QDIhHnn0uYhH83lc,33838
scipy/optimize/_zeros.cp312-win_amd64.dll.a,sha256=Ci6tVI4aySsNqQhi2vcgbcqi2Rlty2dtYDD5lUDbk4A,1532
scipy/optimize/_zeros.cp312-win_amd64.pyd,sha256=FQiKmjpFeXCzKLOgGLHxxzipZkDvdMbCUwQIcaCx9Yc,23040
scipy/optimize/_zeros_py.py,sha256=6Yauv6pW7-XWc086SxpE71iObJJODTKrXWLr39xzMIY,53167
scipy/optimize/cobyla.py,sha256=-MoSAgYJMPGorcRS231A5-rYFHkkwo_1A4jyY4xz2-o,871
scipy/optimize/cython_optimize.pxd,sha256=FhEeI0aq_Y2P00BpghHKOa7UjhW_uv4e7aEzXdS-QjU,453
scipy/optimize/cython_optimize/__init__.py,sha256=6I2EjFmi0mofl4RM8r-1d74jXlD1KaPOjyhpOL6fJGg,5002
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/cython_optimize/_zeros.cp312-win_amd64.dll.a,sha256=rN4lb4LoS61X4okynqC5CYvg1zhUU1CwVaU6G_iVwuQ,1532
scipy/optimize/cython_optimize/_zeros.cp312-win_amd64.pyd,sha256=KBfq2Xv689uCN0N-Sm9A-HIXDsh2DCge7o8zt76xK0g,73216
scipy/optimize/cython_optimize/_zeros.pxd,sha256=5o3CUJecdwYcTh6bX9c4O8F04MtmvvW9KluxtFsYDh4,1272
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=kbSds0gdPDP4-oM6U2AaaFhsuEhCDXkzlfZ4BS82sHE,1144
scipy/optimize/lbfgsb.py,sha256=wcWdyuACE7bFtRkS_Rc7c0q3uRwjzPmoxtYYK_LKlhs,966
scipy/optimize/linesearch.py,sha256=ddHjwUqHFsEH0_NSCni0HphW7UtPrp2TXccmH-cPP2c,1045
scipy/optimize/minpack.py,sha256=Jl-uGpUjoaod9D4pLNZEBSl0aCuDUaKmppD3C64VD70,1337
scipy/optimize/minpack2.py,sha256=n-m19LmjnYy2UNj3EvwVYKJUdtPAZbJh22-2DwybjhQ,798
scipy/optimize/moduleTNC.py,sha256=W1Htn_FZFIGOV8B6kczl0Q6OQmq8Tc8u6hbENArOrZA,774
scipy/optimize/nonlin.py,sha256=xlJBsGhM57y9ItIueD9_FvCkDo1oShOez2MCBFGtDDw,1483
scipy/optimize/optimize.py,sha256=E_EEWvY_rFA09x-q1p1k0pi4RHUYML-FvKyNRoEyAJc,1596
scipy/optimize/slsqp.py,sha256=6ttTaN06slpaI7fA8rBKeLBf3dtMLoZTJkdYBvao4kA,1090
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_direct.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_milp.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-312.pyc,,
scipy/optimize/tests/test__basinhopping.py,sha256=OnRO7GaHTr1QffrCZc0CbXnrX2zzQMUoMpzsnkbt5hU,19422
scipy/optimize/tests/test__differential_evolution.py,sha256=AQUbgPWqefP_lIuftRPAfeiHl2bvBgK_xl09TH1qt4U,63270
scipy/optimize/tests/test__dual_annealing.py,sha256=IQq0e2n8dr3YidXZe4eMKazGLYP5C-FQgkoYmZo91hU,15552
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=so1nDYZ734laRwVZSGY9oydP_TpjulYA-A397PBOiOA,11725
scipy/optimize/tests/test__numdiff.py,sha256=1fe0zErbBz9h3NYr0nB1G4FF4LsgLVRMGa8k3DlCQCw,32152
scipy/optimize/tests/test__remove_redundancy.py,sha256=VC2Tz00TMHan0FJ8QXgKdvazARH58aWCLb97BQZyd08,7027
scipy/optimize/tests/test__root.py,sha256=vchrz_deCGWBFgEpHjO_yspvWOuoWYvV-9eEJN4LF8A,3838
scipy/optimize/tests/test__shgo.py,sha256=82FZCfyDdlv9nqHLv3w0TA1dhmHjh-cdPL_P3wL7vS0,41457
scipy/optimize/tests/test__spectral.py,sha256=H6nN_r4W_BwUFah7nOHvZtKWEhxl-eXx3Yt71sPQMYc,6809
scipy/optimize/tests/test_cobyla.py,sha256=oFELl2kFspJ1HTglE1spjc2qLD59V0KWcGI2LBR6wM0,5313
scipy/optimize/tests/test_constraint_conversion.py,sha256=ib0hzSBpHty30rMRHEug5ESN2PPXXHyujdwiCUDjnH4,12161
scipy/optimize/tests/test_constraints.py,sha256=RkvFZMbgJ2YEb1T5KFJx13aBZtBQHG3ClTbUleP4fJU,9657
scipy/optimize/tests/test_cython_optimize.py,sha256=XmUrnJrxWKWfH5337FiCAfCV6djffjpQWVg1olG1vg0,2730
scipy/optimize/tests/test_differentiable_functions.py,sha256=LNNcUE6Zz0FNlprx9U8AA1xuJXM666cOsi3N6jtHtHk,26885
scipy/optimize/tests/test_direct.py,sha256=_JopiGDv5bJeZuALeYyE5a9umkHw21Z-r7yfWbFtdtQ,13470
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=sapTTn1g1XVzth5l0b_jOp7y_nbRbD2vnr2rNNYwfBg,10320
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=8_Bvj0kpB1mvK3fmh3v_vsYCrH8iFTnmaVjqe1EnSGA,1180
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=MBgku6a6vtpw8BWoGQNQygMhxYMhMV8vnbpJxXTL8RQ,3727
scipy/optimize/tests/test_least_squares.py,sha256=t26_Wpjgqh7INpGvFqvITNBQ5HyySZu8aLe7a8pjOmM,34099
scipy/optimize/tests/test_linear_assignment.py,sha256=51pamAv4Kf0zrqIUkxk-8iFVF4-iZS6F8CxVH1h0J1A,4201
scipy/optimize/tests/test_linesearch.py,sha256=gpIOB_wcNZt-OAWunbrL34CxbuzkHzM_36C6U3Z9qV8,11210
scipy/optimize/tests/test_linprog.py,sha256=0IFveLgqJVCcAPNIlsjYWYh_TxtzUeQv2AEMdYcoya4,99090
scipy/optimize/tests/test_lsq_common.py,sha256=AXdfzIlCO5gQa3gy7Ou9mp1SAHD8-qMP1yCyfK1kaI4,9797
scipy/optimize/tests/test_lsq_linear.py,sha256=pnZ89H3ctmp43yw5p0Nz2gi7hHfIRHoUFavdWhe1rXU,11121
scipy/optimize/tests/test_milp.py,sha256=0vwenAWdg--VjEJbZrG_agTQARI5v6wXGdEoZvhfaoM,14938
scipy/optimize/tests/test_minimize_constrained.py,sha256=ZtOAx3on54CdU_2PwchzGXlQZ-y8C4QRy4udNWbGXnA,27282
scipy/optimize/tests/test_minpack.py,sha256=sCqsAxbHPnSa9iClDjddiZAr_jGUllLS5aujMuH1SW4,41756
scipy/optimize/tests/test_nnls.py,sha256=mCllSFFe9J2NtiGs6xxa49tjPmvP0tRoT0BKVgEIxaw,948
scipy/optimize/tests/test_nonlin.py,sha256=SkXAENzv0t6Dn26gqBdCJDf8n9oxBhIsBHNCD2Tu_0Y,17435
scipy/optimize/tests/test_optimize.py,sha256=CtMtkIbpm971keEbVP4SVB0wBCI4iKLoLRkNeBFp-dA,122358
scipy/optimize/tests/test_quadratic_assignment.py,sha256=jdtSWYpLQ79EXoGBR_IrxtUX7VPWQH1QOFyivhrnufU,16740
scipy/optimize/tests/test_regression.py,sha256=jc-uV89QavAhf34j8J2riNxYY9gIIgOddrRMV5ltC0Y,1117
scipy/optimize/tests/test_slsqp.py,sha256=9252CNGqkQI5h1GR1uES-rWzKtINMpMUT8YbB10-I_g,23868
scipy/optimize/tests/test_tnc.py,sha256=XWL1j_u_9ZrkUBOfyFVytIAdD_dw5rRo-0w5IHx_7PM,13045
scipy/optimize/tests/test_trustregion.py,sha256=S57y-AFuek-24XWgUozVMLaoTGySNufU84-nZ8bo-6o,4813
scipy/optimize/tests/test_trustregion_exact.py,sha256=qaFjV_iWisvbDPrfVEl_SXqVLuSbTiqb1P4_JVRmQXo,13306
scipy/optimize/tests/test_trustregion_krylov.py,sha256=0FXn35I_-hBQS8rKb1BhujlfyAEJ1VDEofTXJ9X_UWQ,6757
scipy/optimize/tests/test_zeros.py,sha256=SXaxxg4FyxzcIpks_dBqsAweFKwKbitfjktdncZ8u7c,35904
scipy/optimize/tnc.py,sha256=HLK6bVEx_LlAeM2PUoDo1TRmEqWC0QHMtqrdhaAGImo,1201
scipy/optimize/zeros.py,sha256=PGXty8DoZhzj6HK4T1KEyczVx_2GRVq8xxNckihMiNA,1052
scipy/signal/__init__.py,sha256=QAa6ngAsAFg_APBxrZHiehX1yIJdwEQKfRY2Lh0Dzdk,16294
scipy/signal/__pycache__/__init__.cpython-312.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-312.pyc,,
scipy/signal/__pycache__/_bsplines.cpython-312.pyc,,
scipy/signal/__pycache__/_czt.cpython-312.pyc,,
scipy/signal/__pycache__/_filter_design.cpython-312.pyc,,
scipy/signal/__pycache__/_fir_filter_design.cpython-312.pyc,,
scipy/signal/__pycache__/_lti_conversion.cpython-312.pyc,,
scipy/signal/__pycache__/_ltisys.cpython-312.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-312.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-312.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-312.pyc,,
scipy/signal/__pycache__/_signaltools.cpython-312.pyc,,
scipy/signal/__pycache__/_spectral.cpython-312.pyc,,
scipy/signal/__pycache__/_spectral_py.cpython-312.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-312.pyc,,
scipy/signal/__pycache__/_waveforms.cpython-312.pyc,,
scipy/signal/__pycache__/_wavelets.cpython-312.pyc,,
scipy/signal/__pycache__/bsplines.cpython-312.pyc,,
scipy/signal/__pycache__/filter_design.cpython-312.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-312.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-312.pyc,,
scipy/signal/__pycache__/ltisys.cpython-312.pyc,,
scipy/signal/__pycache__/signaltools.cpython-312.pyc,,
scipy/signal/__pycache__/spectral.cpython-312.pyc,,
scipy/signal/__pycache__/spline.cpython-312.pyc,,
scipy/signal/__pycache__/waveforms.cpython-312.pyc,,
scipy/signal/__pycache__/wavelets.cpython-312.pyc,,
scipy/signal/_arraytools.py,sha256=Gys8GyLUFE_oDeczc1BWjY59i4mrLzcveciGb1WD49Y,7730
scipy/signal/_bsplines.py,sha256=fyIV37y4Ywg0nN5eZs8tfAGo7jGef_JI408Ii0u8xII,21985
scipy/signal/_czt.py,sha256=dhVmn1ScMOn8rzcvaou8n5s4ik8mlPH7BPsViaSA_so,20020
scipy/signal/_filter_design.py,sha256=RW7PZkszatwmsp7DZwTLkLTMZYNp11qhhHVXXdRf-Sc,191142
scipy/signal/_fir_filter_design.py,sha256=OLGmO0s0akFTSBcQnIXmmbEIXGp8Jf0obxztaZdA_MU,50354
scipy/signal/_lti_conversion.py,sha256=arhvsBjgFkIqOFUtNnBri0Po1CdXVjo9Pj6JlajTJZU,16663
scipy/signal/_ltisys.py,sha256=Zau2gZNkTUL-hj2tAaPIWeXyBQEZWxfWE1ccBSxXjZQ,134893
scipy/signal/_max_len_seq.py,sha256=HgaUzZZEOTy89zh8K5rEf7jDFALxRDEArsIUn1woWFM,5201
scipy/signal/_max_len_seq_inner.cp312-win_amd64.dll.a,sha256=K9LtTzYyJYHZh3i4nmEoU6t4B1Phe_qvITwSJhTJRRM,1676
scipy/signal/_max_len_seq_inner.cp312-win_amd64.pyd,sha256=xA1DgC3C5LoY8Ts26rbm647cMCWorUVyQEcMxGg29aI,1005568
scipy/signal/_peak_finding.py,sha256=YGOqY1MJTYuj8k_G9ZjMaIAprGOsx3cpqtUKn4SlDVs,50118
scipy/signal/_peak_finding_utils.cp312-win_amd64.dll.a,sha256=ktVnee1fWj7i9Ouw9GQRE2ZAKIkazbNVD5IdliWnFGA,1688
scipy/signal/_peak_finding_utils.cp312-win_amd64.pyd,sha256=GoneszX9MCnfd9mZLBaTk7QjI8sReRxKuAJraYrCcSs,231936
scipy/signal/_savitzky_golay.py,sha256=TA5zbmZeExTMJS2ORPe4BJ23PfS7NHKkoOTjRj552z8,13774
scipy/signal/_signaltools.py,sha256=gb58UL4Reu00lQpPVcnHcXiOGVYEGV9V7PlRxuijbYA,161948
scipy/signal/_sigtools.cp312-win_amd64.dll.a,sha256=QpS4IVKxurYK8XBrDDeRQBi3eFYILd7ugeUV6eOGE9s,1568
scipy/signal/_sigtools.cp312-win_amd64.pyd,sha256=Dl_IzPvDs3lP5zXiWdhFFiEcdkvcpBpbaott0n0kt_8,128000
scipy/signal/_sosfilt.cp312-win_amd64.dll.a,sha256=fnWBWIIBHe0VUMzI4IGpfGVg3RaTmpC6PZSBV9sNMhk,1560
scipy/signal/_sosfilt.cp312-win_amd64.pyd,sha256=YJGqvBxT2KuNQT17E-HofOO2z8xlM9u6tJSYeVo3A90,243200
scipy/signal/_spectral.cp312-win_amd64.dll.a,sha256=wLtLUT1QnlMaiHijspxa93dMu6459oeLxIdwv6a0UVg,1568
scipy/signal/_spectral.cp312-win_amd64.pyd,sha256=xy_lxT802Rb8Lrnro86Vum-gqsgNryIdy2lR-2Gv9nc,1008640
scipy/signal/_spectral.py,sha256=b6WMIuLcBE4WuMBvQj8avTGmPX4wkW11mpoMmpD6tKE,2023
scipy/signal/_spectral_py.py,sha256=7pEODVKpSD9eo0olcBBfj0R6i0ueAhysTk2hB4WvbVg,78720
scipy/signal/_spline.cp312-win_amd64.dll.a,sha256=2OzRwT-NYJmVSkVR4GThpMO8jfeLINq9dJe57170_Og,1544
scipy/signal/_spline.cp312-win_amd64.pyd,sha256=0AddnHQem8wi0Z-2kSyoi7ULR7yw7Ym-i8-TXj2Y4rQ,83968
scipy/signal/_upfirdn.py,sha256=OeP4CSPwZ0QD2Bu9UIqQzPfmWLQuW56wQQfeKrax2X4,8100
scipy/signal/_upfirdn_apply.cp312-win_amd64.dll.a,sha256=t4rkOuW4ddV3TmyOdoz2NsImbNl3ENk0Hbvdwhb5WIM,1628
scipy/signal/_upfirdn_apply.cp312-win_amd64.pyd,sha256=ek5GSsGUXGV-vlBDzByzIwsdYy1YCQkXvLmqzuwqPO0,306176
scipy/signal/_waveforms.py,sha256=SjUjL0zu-cOy1V-xsWxzz3tkwT7ycG4LXlJdQbiuKiY,21195
scipy/signal/_wavelets.py,sha256=xcFTQQhw31D-Kz3sb0RmKzWUv3lj4s6jGfQ7jxACU2Y,14626
scipy/signal/bsplines.py,sha256=If7aqljUcfb_KMkNmcPP_Z43wFlYjF1VZEtcxHBtrto,1117
scipy/signal/filter_design.py,sha256=4Ma-9mrsHtdVjkqCOhPkPs4qle4ZPAEfVxup-r4hfq4,1761
scipy/signal/fir_filter_design.py,sha256=uyer_Ekos6bsjQXBv_uofH_l4iBNmk3r_TP-F2ExAf8,1036
scipy/signal/lti_conversion.py,sha256=VLeuuxDFDXAni73MjwG0lsDPnAVnB9yA2IOSOZhtQCU,966
scipy/signal/ltisys.py,sha256=o3lWSZRHAEqDxQA3Uo8fFqZ_yHCgDepdzwj9kbF6WP8,1508
scipy/signal/signaltools.py,sha256=H6fVq2a-lEG1xJGhI2la1aU0Ve3C76wdkfTLRce04pU,1438
scipy/signal/spectral.py,sha256=sIEVoqzANYHLVo3sLDWdyR9kCaZ9YRxocXsZXBAMelM,976
scipy/signal/spline.py,sha256=4xylmC4JyNUioC_v_jsn4fdi2aFMVOy23Id14X8Q9wM,836
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_czt.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-312.pyc,,
scipy/signal/tests/mpsig.py,sha256=3cd_WPgz9dkjalruhXxxAPYCV8Kzs0L8CH4h-0W8jX4,3430
scipy/signal/tests/test_array_tools.py,sha256=GHjEyc4Q-O3PN9C9R1CCQ2Izm-LKGZhg5mX5gZ_FxUk,3743
scipy/signal/tests/test_bsplines.py,sha256=JIKk4a_jRWTX7-awcvOZbLrOFmfdXzS76c-SUVzmqqg,10844
scipy/signal/tests/test_cont2discrete.py,sha256=1vzkouv0wINTChU3pmQksPPbN9WQ3sbL7l0hahGnOnY,15021
scipy/signal/tests/test_czt.py,sha256=gSrHRP4imVuSnNrYkNrxvb4sdYnAFp6rRUMFr-4C6Qs,7212
scipy/signal/tests/test_dltisys.py,sha256=2ETM-Wwlgc-p6-Vbpu7-Mz1Vp6UK51M8qR2yvb70gOg,22156
scipy/signal/tests/test_filter_design.py,sha256=i672pNUK7srHwF7Dzbt3ugfc5TaQgMUUNWCOlNJoA-Q,192863
scipy/signal/tests/test_fir_filter_design.py,sha256=vn7jm9fDFqEIqZw2gGm8CO7U-mZZ2NnnfzuBwBMNiq4,29606
scipy/signal/tests/test_ltisys.py,sha256=qClN5NdpEA9BB1nE83cGv1KSbtFJV4U5wHWMxS0tNaU,49559
scipy/signal/tests/test_max_len_seq.py,sha256=8caIaIvvSgV9zsQ8t2MnNBeOSBLqJajPISerank05Qo,3171
scipy/signal/tests/test_peak_finding.py,sha256=2j5nEzhgFkvpudQqHOM5EZ6IqMU5cyqgpsgfVxvC0mQ,34554
scipy/signal/tests/test_result_type.py,sha256=zWsEnxBFDAKmi9IhebffJbvjY9R2aO0kFtUm7skwAm8,1679
scipy/signal/tests/test_savitzky_golay.py,sha256=e7NiioSNnsP2SXYW6xc_ygBPLske5eDIjVf6GjUCIQo,12782
scipy/signal/tests/test_signaltools.py,sha256=EGKEEvsQm5UYU2hRfLzb1KdF3MOAnUNUMaDsjZf_TWQ,144996
scipy/signal/tests/test_spectral.py,sha256=a_Bd7KX6bppQ7VY2UCvHp2GVcLABUQcnxDXzwjOQzZI,60959
scipy/signal/tests/test_upfirdn.py,sha256=qpJJpoo_Hl0yk7pkuDIAQd6Zgs7CIX5_jE6qhHNICJk,11527
scipy/signal/tests/test_waveforms.py,sha256=j80USvnR7ddMZZeqQ5PeiHbJ5m4E7qHG7QbVD5TxKA4,12326
scipy/signal/tests/test_wavelets.py,sha256=k2bIW4Q3HO9cJoimrTg1KXrym-k5uWhqVpBgUNPu-Xk,6130
scipy/signal/tests/test_windows.py,sha256=AKKwO5RGtNUvG4tGQk_W2thxJBVRe9JegXbEZqd5ri0,42608
scipy/signal/waveforms.py,sha256=ORwlr1q1ZjgEz-S-SmtF6tqt3UfBz8EEeFq6wx12Mrk,919
scipy/signal/wavelets.py,sha256=jJPbg5suonF-Ii9wjC6AHQAmZqcEz4F6cpw6mPs3bMw,856
scipy/signal/windows/__init__.py,sha256=G97xwCUcxrUmZO1pb3K_VfzlyUrzcJYpGjMN9UWWC2k,2171
scipy/signal/windows/__pycache__/__init__.cpython-312.pyc,,
scipy/signal/windows/__pycache__/_windows.cpython-312.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-312.pyc,,
scipy/signal/windows/_windows.py,sha256=C4tUvttKvkUTS4QSCrJLYNQ7uNESTFXxvYHLrOxDN2o,85991
scipy/signal/windows/windows.py,sha256=CqOWSr35U0WGthpoIA4EQxomL4JfmWjGAEExNbdpOxM,1149
scipy/sparse/__init__.py,sha256=ESpGCOdmJQp-v0lHIlD3zP8L6CM1SN1zTnp4VP11bOA,8961
scipy/sparse/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/__pycache__/_base.cpython-312.pyc,,
scipy/sparse/__pycache__/_bsr.cpython-312.pyc,,
scipy/sparse/__pycache__/_compressed.cpython-312.pyc,,
scipy/sparse/__pycache__/_construct.cpython-312.pyc,,
scipy/sparse/__pycache__/_coo.cpython-312.pyc,,
scipy/sparse/__pycache__/_csc.cpython-312.pyc,,
scipy/sparse/__pycache__/_csr.cpython-312.pyc,,
scipy/sparse/__pycache__/_data.cpython-312.pyc,,
scipy/sparse/__pycache__/_dia.cpython-312.pyc,,
scipy/sparse/__pycache__/_dok.cpython-312.pyc,,
scipy/sparse/__pycache__/_extract.cpython-312.pyc,,
scipy/sparse/__pycache__/_index.cpython-312.pyc,,
scipy/sparse/__pycache__/_lil.cpython-312.pyc,,
scipy/sparse/__pycache__/_matrix.cpython-312.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-312.pyc,,
scipy/sparse/__pycache__/_spfuncs.cpython-312.pyc,,
scipy/sparse/__pycache__/_sputils.cpython-312.pyc,,
scipy/sparse/__pycache__/base.cpython-312.pyc,,
scipy/sparse/__pycache__/bsr.cpython-312.pyc,,
scipy/sparse/__pycache__/compressed.cpython-312.pyc,,
scipy/sparse/__pycache__/construct.cpython-312.pyc,,
scipy/sparse/__pycache__/coo.cpython-312.pyc,,
scipy/sparse/__pycache__/csc.cpython-312.pyc,,
scipy/sparse/__pycache__/csr.cpython-312.pyc,,
scipy/sparse/__pycache__/data.cpython-312.pyc,,
scipy/sparse/__pycache__/dia.cpython-312.pyc,,
scipy/sparse/__pycache__/dok.cpython-312.pyc,,
scipy/sparse/__pycache__/extract.cpython-312.pyc,,
scipy/sparse/__pycache__/lil.cpython-312.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-312.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-312.pyc,,
scipy/sparse/__pycache__/sputils.cpython-312.pyc,,
scipy/sparse/_base.py,sha256=EtNoC128CUa5XykelWuBrAZ6WhdP9AFO9TAmuAG9iYs,51519
scipy/sparse/_bsr.py,sha256=gj9gd-U5cz-E_Ti3mK1dL20uMnkkArKXYJzchabCns0,26519
scipy/sparse/_compressed.py,sha256=iM_4xWo7jCPurg85dh-cY71TMaKVi2SpJKKdcyfqu40,52424
scipy/sparse/_construct.py,sha256=O74S-X9dAebDGP4NuTMY_ak32tsyYG-0afwOJ0V7L98,31252
scipy/sparse/_coo.py,sha256=hKUyAUwm-_mGeqVNuyjWgSjMUeEbY2Pid1e2YvgZvaQ,23732
scipy/sparse/_csc.py,sha256=ZWAPtTMQTtLBImZhCXtrJtA_6XWEUSLzf9fVEIk1LLw,8572
scipy/sparse/_csparsetools.cp312-win_amd64.dll.a,sha256=UAFgstmTs8pBzDa5JiscGvFBJW3KX1LX4QnWw76O2ds,1616
scipy/sparse/_csparsetools.cp312-win_amd64.pyd,sha256=OuxAvGVBkfpmWtzS9l3POiBnaCMxHfVVWlsm08KMzsY,612352
scipy/sparse/_csr.py,sha256=PcZnIzIa1U0HZXBuZGLSYtw7dEVqYzZJoZUN2Cx5Law,12437
scipy/sparse/_data.py,sha256=P8uNH8muSuTY91o9nkKRDpXtQuHAGP-iHOfh1-vMV9g,16896
scipy/sparse/_dia.py,sha256=me3WFPxH2ivAQD0ZdszQDaEyn12FQazSJZrxTgiuEOs,16939
scipy/sparse/_dok.py,sha256=VzkgtDKLSASIF9T9SjQ_syPHxUqs5-g5keosLKmZRqk,16824
scipy/sparse/_extract.py,sha256=WnyNwj3g_xJG4vp5AWd0gTzIDZOeRIU0-FpiBU3HiKk,4817
scipy/sparse/_index.py,sha256=CENg8RICiGgRngRA66JInHP004P5T7YLdkLhJbHy9Yc,13315
scipy/sparse/_lil.py,sha256=2GswkUaTV0iV3JHnPgUdOFHHQZ4XDwVknLCxrL7G0ho,19177
scipy/sparse/_matrix.py,sha256=FSFFA-ZrIoVocoTwp-GpI1SEpvChegdLvrFy5r-Opvo,4185
scipy/sparse/_matrix_io.py,sha256=6bi3RLMZy9ks66QawQ3fXLKUPkdJWVTMF5MgOjlLPTM,5498
scipy/sparse/_sparsetools.cp312-win_amd64.dll.a,sha256=h5IiqQ6BmzkwUOsP4v50TslupMnHJRNHBIwj9SGW0so,1608
scipy/sparse/_sparsetools.cp312-win_amd64.pyd,sha256=pIgAKGPbJx2dvVzdlrCuj-K3sfoXy0fb4CYs7f9De5k,4211712
scipy/sparse/_spfuncs.py,sha256=vwBEj6xNGh1V17dONJX76wCAj9iFfmteGqh48DZ_vng,2063
scipy/sparse/_sputils.py,sha256=5se6o8vqKnUFf425O0ECR_OW2trvD09-wtUk0d9PAC0,13434
scipy/sparse/base.py,sha256=pSqkd4j0eVR7-LLCYaB1QqtpHaM0Prbijxepue14Z-U,1058
scipy/sparse/bsr.py,sha256=4yre3ig776iff_Z-Uh73waIhdBFOu-Xz6hFWRa9DUSU,1104
scipy/sparse/compressed.py,sha256=s7ctEABLvUH_bpwYge_TrH13UaHAxSsmID000A-Hep8,1340
scipy/sparse/construct.py,sha256=qfM6te-w3En3QBTF4g7-3u5VTwWCv13WPD7TiQTYdHA,1211
scipy/sparse/coo.py,sha256=xylo2KaQz1BlK6UG0NpOTgJkdtRcofZElc7cjZrCS8s,1138
scipy/sparse/csc.py,sha256=WRiDAxCAr0frWNtxg5oCJd_tVUCQQPgXHXpC43POkYw,872
scipy/sparse/csgraph/__init__.py,sha256=wI_NApSS88_2jDQvrrvmPw-hCoBLmVo89Emb_jZT-Bw,7947
scipy/sparse/csgraph/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-312.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-312.pyc,,
scipy/sparse/csgraph/__pycache__/setup.cpython-312.pyc,,
scipy/sparse/csgraph/_flow.cp312-win_amd64.dll.a,sha256=ZUgwyTVC6sHgJNUVWhOanNCjgHSA-jUYYisriaVXF8s,1520
scipy/sparse/csgraph/_flow.cp312-win_amd64.pyd,sha256=6gTCCZmRE1zYPrcw-MpWBYeT3ZNhn9YU3W3hcYIlhFw,272896
scipy/sparse/csgraph/_laplacian.py,sha256=4fVuAfEqlO4piY_9axPdfY3PAQVPx3xyhLjBmZB1qDo,18418
scipy/sparse/csgraph/_matching.cp312-win_amd64.dll.a,sha256=GxP8bGjstu7Obtx3UcCkrfz_wX-op2IrxOvI6leNkJk,1568
scipy/sparse/csgraph/_matching.cp312-win_amd64.pyd,sha256=Zt49lYj_cmZmyYY6JWhWndaV7jRZ4MlVTtAzqthILwk,275968
scipy/sparse/csgraph/_min_spanning_tree.cp312-win_amd64.dll.a,sha256=4h6hXy7z2DvBH3M_u-sgnLJEbeVMkwr27QXQITK9zqo,1676
scipy/sparse/csgraph/_min_spanning_tree.cp312-win_amd64.pyd,sha256=1v1E7A0d4KCsY8M-PyL83eCWniE1u7pGD-P1ZWwUdmQ,200704
scipy/sparse/csgraph/_reordering.cp312-win_amd64.dll.a,sha256=lXS-9Yf4UImSRPMarCjpCt24q95Qqnqxw0vk5__KjOA,1592
scipy/sparse/csgraph/_reordering.cp312-win_amd64.pyd,sha256=SIgT4cBcJxfiLA2sdLAqx9bdUf6fUIRBn48wm-wBjHA,258048
scipy/sparse/csgraph/_shortest_path.cp312-win_amd64.dll.a,sha256=EiLMr3uCMHYrAI0Np0p_cye8Z9ynxP0kc2D-w_xbyvs,1628
scipy/sparse/csgraph/_shortest_path.cp312-win_amd64.pyd,sha256=FoKjycOqbo_0iVo9gbd2EkI4syROIrFWOAWlpDdoBls,406016
scipy/sparse/csgraph/_tools.cp312-win_amd64.dll.a,sha256=b3lLMkJSnneNa7n2pBPVA633LnNWIHD8-eiOWcWIosg,1532
scipy/sparse/csgraph/_tools.cp312-win_amd64.pyd,sha256=ih5yW7-3fjTNn0z9-0jg4rJM6YbJj8GjkSTGw9WJKUM,146432
scipy/sparse/csgraph/_traversal.cp312-win_amd64.dll.a,sha256=AN0roTdZ-008Bl6TmHUEKxXjYf46fXMsWaiP53S2oCw,1580
scipy/sparse/csgraph/_traversal.cp312-win_amd64.pyd,sha256=BRLbgN7uCfE7g4ADFEtkz9dykxZVx-YKeVOD_zFBSrQ,501760
scipy/sparse/csgraph/_validation.py,sha256=1hOpo7O_rkzhI_EjXM_AV0l5Tm2-LQ9jay5VGAY13iA,2385
scipy/sparse/csgraph/setup.py,sha256=6mbVfcjBmpLDEx7yRg5eFoOkviADL0-oQRfp5PiWDeU,1135
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-312.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=HiW9gS_ttLPBl9XvdVCRZGRKfWdoiW4PsmVTqp1Q-kQ,4067
scipy/sparse/csgraph/tests/test_conversions.py,sha256=3gfyXwYIcC00KM6Ug2irTpMpuscTwJdAkuzd4cNzWdk,1916
scipy/sparse/csgraph/tests/test_flow.py,sha256=FDRd335RogmRkkRtH_wUwylXXYr-3WUFwqvktzHLWlM,7621
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=-QW6ydKkqgZvZolXrpNuv-dFmS3143cXFhc1VVcUymI,11311
scipy/sparse/csgraph/tests/test_matching.py,sha256=GEoiMXISJlDsyVfWR0LS2fH1YkOpSJzyiyuZWcEbwMk,12238
scipy/sparse/csgraph/tests/test_reordering.py,sha256=4YgPFLLffko_c_k1QpqUd2NvXAwIsVbsECnIFNuVHmA,2683
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=0QopCD2rVQU1olqq_I1YNvBlfRC717LZta_pGOUg16M,14836
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=x-m3m3NjK03t_Azn4BdJ_-SzkedmarFW1uQBhhfdTc8,2180
scipy/sparse/csgraph/tests/test_traversal.py,sha256=6kHqs9jJSzi5qrBGkP5A7LenAI646d-eg1ZoN5eobgU,2921
scipy/sparse/csr.py,sha256=VAwsUL0XGGZvobRQn4K63FXN_XLT29d5yaAUI7tFoSk,923
scipy/sparse/data.py,sha256=JoqtrFgPUGkwc_depYqiUbP7wVtCupA2EFQsQBBDI-k,844
scipy/sparse/dia.py,sha256=UjADiySCpJtlipGakKG07Lv64hxbyGbkdsInqax-aPU,975
scipy/sparse/dok.py,sha256=jRaZZHF34-joUkousdXd7GxMUx6s-eHWxOnDuIrMF6o,1022
scipy/sparse/extract.py,sha256=agyOt7kq2eg8pPvkuFq06whM4IhmF5xiuJznwvqHMcY,812
scipy/sparse/lil.py,sha256=DHwwW-_wY7LAKw2My3IetGNzGu6g5Eox_Bj10IhxrCE,1022
scipy/sparse/linalg/__init__.py,sha256=qaRfiIoGJ8XOJS4Y4kJtjmQgwQ4gz6-x9qJGjBBXHfE,3853
scipy/sparse/linalg/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_interface.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_matfuncs.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_svdp.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/dsolve.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/eigen.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/isolve.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/__init__.py,sha256=TtXewy9uEo0NWNiJ7VyfLARI_ruCsMry4Eh4b0q6zVQ,2062
scipy/sparse/linalg/_dsolve/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/_add_newdocs.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/linsolve.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=c8_fJLZeoxFD4KXx0Se76rd-rr-3VokL_o4Qkd1zEZk,3947
scipy/sparse/linalg/_dsolve/_superlu.cp312-win_amd64.dll.a,sha256=BGuPVnhUqqTdxWBc20HnpeuzInCW-3HK0Q-ANaUts6I,1560
scipy/sparse/linalg/_dsolve/_superlu.cp312-win_amd64.pyd,sha256=T1f2K0oLeLWRztvtqqlMknjRs3a_nepEwr4OCRl2LNk,411136
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=2RzZbIg4BS8SDPNOvzfQQAOIEJ1g5azUeqlLh8yT2SI,26767
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/tests/__pycache__/test_linsolve.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=4Q_RUM2B0ctzUVM8kWeT3lxl6HlRWmXK_KwWFaNmpDY,28434
scipy/sparse/linalg/_eigen/__init__.py,sha256=013F3u6pMe0J0wKjMFq_a1jSuO-pwS_h_XzmJI3xfMM,482
scipy/sparse/linalg/_eigen/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds_doc.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/_svds.py,sha256=r-yB1Kwnah8nbJB4h96SGoddAmmsTX-4qgw1pXLuKow,21240
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=7nPBSRss-1CTJRcBcd_t1F7EjRnYgEk5xY4dkP-WKZk,15921
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=_LPGx94UYM99CGPDxZluUY64AVouztNpEfPaF4RAs98,1937
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=EU0vXuTlZzMcXxCITUTVxUZ_N0EgcDF06Q2rMzy0Q3o,582
scipy/sparse/linalg/_eigen/arpack/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/arpack/__pycache__/arpack.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/arpack/_arpack.cp312-win_amd64.dll.a,sha256=aid3p9QLqex0_oQnkWM4ToyXH0e0-8OSIEF7y8SWFAw,1544
scipy/sparse/linalg/_eigen/arpack/_arpack.cp312-win_amd64.pyd,sha256=dmz6fmCsKT_QvcuFn4FuuD0WIRSFfjDrPVIFT2GV3bI,797184
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=gfQiyMqc22OOe9LBy7EEnQqdrgmmoT-xF5LqB_pc7Wo,68971
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/test_arpack.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=p_GH-V7ccGLcgxY_JY6RhEplX_MWh5XDGIgZO4HVNPY,24443
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=8aw6542gPHNriFRBENTZ5rb1M3cqJKToG--paaqpaxM,436
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/lobpcg.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=_x6mcbPUyYO7HxPZOH3djM0hAVExgH8-TzAMhjDrN_Y,41963
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=ajTpHKd7xFL4F-eXhwzFzWyKGGdB42xGgRWoW8GQPLE,24158
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/tests/__pycache__/test_svds.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=7duIidn9oeA--vccU3iE_yjn3r3xlqA0oMnyh6QYKCA,38461
scipy/sparse/linalg/_expm_multiply.py,sha256=_XV3t6bbCFQZm_JfQ3KNUtC4e-QQ7K3T-mAdl4pUDHU,27106
scipy/sparse/linalg/_interface.py,sha256=GI89DoGICL5r1JzXOwHMkSfmbnVGWxL1TQTJHBzJexo,28739
scipy/sparse/linalg/_isolve/__init__.py,sha256=rFcoMYM70Tbtjlc3xhWhgjdNHUOwc4TSV1sOFZwL6Gw,499
scipy/sparse/linalg/_isolve/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/_gcrotmk.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/iterative.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lgmres.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsmr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsqr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/minres.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/tfqmr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/utils.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=lBtNZh-wDFR4lzNFGTAIJDxbOOwDutpkmqUkyNNA_3s,16493
scipy/sparse/linalg/_isolve/_iterative.cp312-win_amd64.dll.a,sha256=HnfHaGOke4nmaYIhdeaTK3wNsTlfts3t7hlPjPZZ54I,1580
scipy/sparse/linalg/_isolve/_iterative.cp312-win_amd64.pyd,sha256=XQWcA1odW1genTzSe4RYZTLG5f8xIO4E41HcCiOcN5g,253440
scipy/sparse/linalg/_isolve/iterative.py,sha256=a0qMKOamdhKFFH4cpFo0PxUETPks_xIXbGyWVo-iKmo,31479
scipy/sparse/linalg/_isolve/lgmres.py,sha256=lRjz0M-A25b65fyxWpJLPsYFe-Ck_NmLEH_Csa24EbU,9169
scipy/sparse/linalg/_isolve/lsmr.py,sha256=WtBdVpQI0abyz6RtSeMCkxdyZHP8eTAY9RYb1MjvPOI,16143
scipy/sparse/linalg/_isolve/lsqr.py,sha256=6pld_K09HE1YPVYCSMEWfWynJoUR2cqnkHHB1-6hngQ,21801
scipy/sparse/linalg/_isolve/minres.py,sha256=zOuVdKIEhsxS23E3Hr8BHxPCIZVXrk0JUTTjxht4Au8,11250
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_gcrotmk.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_iterative.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lgmres.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsmr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsqr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_minres.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_utils.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=wCYQKfJF9dL3LNOFR63STx171miMRqtPDnHFR_83sjQ,5573
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=k22TZfbHEPrImjXa6W0yAPLtOvb4dCuBs5y0yWxlozk,27243
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=30He16TFGH7PxmTPiG4gD9hZXOwMSTekFesHds19Ea0,7271
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=1d0m0ManoE-QTVfFOtNRC47tJK4jGmGfGVJQCpVjtYE,6551
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=lxGYz6aAAgx1Pza2Fxx_V2NJaqLYxORPe5YAaHYmQmk,3874
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=Qyh4jVaBlhjUstHZaIKnx5LBfR4pUbqYRz83DK_T_OI,2543
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=5LFVBgy2zQvakzgEMdVl8hxcCDXEMOZAXpm3x53Cyw0,255
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=RkxysQ_Cdz6DnzUIU38PA_doE89zwrb3d2WhrDhcE5k,6425
scipy/sparse/linalg/_isolve/utils.py,sha256=LjktcTFezfc_dtnhxbzOfGB-TZhsjTxx_CDVLDA4MZE,3725
scipy/sparse/linalg/_matfuncs.py,sha256=CLvahEV_qxkDSK_Tq8QDhTPFGRRhQ8kjAG48Uisy9Nc,28073
scipy/sparse/linalg/_norm.py,sha256=YOCMIrz4V8Pg4QS1f3Xm5xQJKsWJb-trMPN1jGyauYw,6255
scipy/sparse/linalg/_onenormest.py,sha256=wVJXPEI7AwZQT0JOtC_044bM6DaQC7oMkMhxGgPZE_E,15953
scipy/sparse/linalg/_propack/_cpropack.cp312-win_amd64.dll.a,sha256=-Puv9GJmlmc5V-JlsKYcfCiddh8Q2lieTjUGMF2dQLw,1568
scipy/sparse/linalg/_propack/_cpropack.cp312-win_amd64.pyd,sha256=qRI6h4RR-KKodkrA3hlmupcn0XyPHNlMRcVJQ0RdtWs,478720
scipy/sparse/linalg/_propack/_dpropack.cp312-win_amd64.dll.a,sha256=sZtPRJEcFMWW9RX3I97pDetmjsHwiOWdwsAudUTX-84,1568
scipy/sparse/linalg/_propack/_dpropack.cp312-win_amd64.pyd,sha256=P4AVzZm0xiuyNmy11XrPx8yAMS57TNsHFM_ALMcbzYY,448512
scipy/sparse/linalg/_propack/_spropack.cp312-win_amd64.dll.a,sha256=QKBJQiXV6fHoiAnQLr7bYxSec4O9yeNekQq6V6976K8,1568
scipy/sparse/linalg/_propack/_spropack.cp312-win_amd64.pyd,sha256=HcOqrUUTMrZCTrkhc2Q8JWdKkIx4uXLSbw-mjxS1f8M,450048
scipy/sparse/linalg/_propack/_zpropack.cp312-win_amd64.dll.a,sha256=uXz74Dpd5FBC8S3ZDkIZ8djxafuA9YwY0jnsCKpy5aI,1568
scipy/sparse/linalg/_propack/_zpropack.cp312-win_amd64.pyd,sha256=lf3riyNQkuXKs4UvTwUB02dQzYzWzzcwT4UL4Y9RIJQ,467456
scipy/sparse/linalg/_svdp.py,sha256=OHFRidhprCiilL40L2fjJOhX-3k3mpsGSL6Vh6d81XM,12006
scipy/sparse/linalg/dsolve.py,sha256=nYG7t4XlrdAoPvgrAepq4XNaINC9yfSjMbMG5EfByRI,1241
scipy/sparse/linalg/eigen.py,sha256=bOh3MTGtHhSsS3ArrwRzed87-oE8AJJPZ2oE3UKraSU,1188
scipy/sparse/linalg/interface.py,sha256=Se4ac_HtvwbuOCcwtvnYYI0Rr-iyiQBLn47vGHhRBRU,965
scipy/sparse/linalg/isolve.py,sha256=Vusbrwgbf3BuiOw_EZLmR7pFpxx5knsYUHj3y6GpN1k,934
scipy/sparse/linalg/matfuncs.py,sha256=wTkNcrOl0TuJuPq1e0QdM1g2ebfZso7mjoKhzF2Xl80,978
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_propack.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-312.pyc,,
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=s9J-TYvGQCB_zD2jc_jSpZ5me8pxHp8mQPJII-8W6_E,14264
scipy/sparse/linalg/tests/test_interface.py,sha256=VzkF4946FsehyEjQvAop4JmADXpTZ3XbuPgwyK9BOKY,18424
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=AeKJcvJaIPdvfBcCj0hnjtidWw1jli4fxq-pN6Ndrc0,21861
scipy/sparse/linalg/tests/test_norm.py,sha256=ZuZ0yjU4U5XmkGkWJLPFTdAwF0T-sNUfaHovTuuXcUI,6304
scipy/sparse/linalg/tests/test_onenormest.py,sha256=_1CbXJgWFL-72PYjtlheU27jd5xGX90_Vki834yZ6qk,9479
scipy/sparse/linalg/tests/test_propack.py,sha256=g2Rws63UIosl6q_8B3uwE068IahiAXQq9KILolbtaW4,6471
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=RAcfJVKMNzL3QZV1TG93kJLIintPxh9CIUDLMEDHw-M,6365
scipy/sparse/sparsetools.py,sha256=RqGWecObVQBPDNkavAVJW9WulL-etEd3zbheY2OXdwg,2496
scipy/sparse/spfuncs.py,sha256=wpFJZyDrXihxfCzKKPqf6vz9wJrNHysl_UOpUq7uvXo,871
scipy/sparse/sputils.py,sha256=qdSMDzaOwOKiN4BDaaxfJqbj2-aPyDvNc_nL3WnS0_A,1239
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_array_api.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_deprecations.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-312.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_array_api.py,sha256=jVxFSSYTJaVvPsHz3BB_ILIUMTkALh3RT_WYkm8Aa8c,13983
scipy/sparse/tests/test_base.py,sha256=cocm6_uTZbGM8_fNsmo8bGBfN1X44HOgt0eAadGH2oE,191487
scipy/sparse/tests/test_construct.py,sha256=AoyEbd_lLnu9E2QSb82sZkJcdGRrEp1xk8EC4zuSzpE,25471
scipy/sparse/tests/test_csc.py,sha256=mfZbIugI1T0etqGNgjH_3Skxj-13481Dog9U7cXaWx0,3000
scipy/sparse/tests/test_csr.py,sha256=a6BR5zJNPdQnZ92sxkVGK3dpE7Sgj6Y4uUPKQgZFxns,5820
scipy/sparse/tests/test_deprecations.py,sha256=wiSkHu7R-lpfw6HoHvryJ9HzdDyPudsJIbKcIG5FMbo,743
scipy/sparse/tests/test_extract.py,sha256=4XpaYwpNmZhNF_sTF3vKz13uKlPTEd1VhE6Ocr-L8fk,1355
scipy/sparse/tests/test_matrix_io.py,sha256=1KftcjrHqffNjZ_0B57ZDjvphW0R00tMk_duUkk04iw,2628
scipy/sparse/tests/test_sparsetools.py,sha256=BOmJCNF9ebgBqUVSmqPEeV9vSLdsdqtfwmAO0oN-Jtk,10890
scipy/sparse/tests/test_spfuncs.py,sha256=jUPhJjGat0YjcEeZWrD6Iz2G3el0ubAyts4ezvbrCM8,3355
scipy/sparse/tests/test_sputils.py,sha256=f1dj3tk2ttc5Zjg9j6Fh7Gt6SPzozPe4L46XKx4pnJQ,7493
scipy/spatial/__init__.py,sha256=rvPxCaitVLY2ECMpYvgA0PkRNBbdi5iXmSeBlf2gOwk,3812
scipy/spatial/__pycache__/__init__.cpython-312.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-312.pyc,,
scipy/spatial/__pycache__/_kdtree.cpython-312.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-312.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-312.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-312.pyc,,
scipy/spatial/__pycache__/ckdtree.cpython-312.pyc,,
scipy/spatial/__pycache__/distance.cpython-312.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-312.pyc,,
scipy/spatial/__pycache__/qhull.cpython-312.pyc,,
scipy/spatial/_ckdtree.cp312-win_amd64.dll.a,sha256=VWTEPBw3yh-0O5fl2ozEvpBS9lJdf9TRtILm7klQliA,1560
scipy/spatial/_ckdtree.cp312-win_amd64.pyd,sha256=djvDUUV7bvYER1z4xYpT2x8YfzF91RDKVyM7BkBOPX8,1641472
scipy/spatial/_ckdtree.pyi,sha256=YfW_RcGoQaZ--DnzUi5PZLMQzRonqpXNyAtzCwaachA,6173
scipy/spatial/_distance_pybind.cp312-win_amd64.dll.a,sha256=8eRi4U-0Wot5VfUCNcAWQDQ8htXqR0a3aBFiWVmlGXo,1656
scipy/spatial/_distance_pybind.cp312-win_amd64.pyd,sha256=DS_kBE1-tcuVonsVmh9pHnWexnKBMfx9LXebvoqaAzA,1383936
scipy/spatial/_distance_wrap.cp312-win_amd64.dll.a,sha256=bYXq7I385h6JthuLhdEgu1zrjQAC-AIDv9gxpT4E-hU,1628
scipy/spatial/_distance_wrap.cp312-win_amd64.pyd,sha256=H-M6BbB8NH2g3mLqkdcvk3Kn-bcoez24cMZXtEijsHw,110592
scipy/spatial/_geometric_slerp.py,sha256=FqF1FoX3fnMIaHER7e1onwWlLDLufDiumAy0QSY79WQ,8184
scipy/spatial/_hausdorff.cp312-win_amd64.dll.a,sha256=yZib9c6vAhf1a1d5hPMyi92c8s5da5xANj_1VwoIJxA,1580
scipy/spatial/_hausdorff.cp312-win_amd64.pyd,sha256=Ldwpg2Txvj6uT_JWrn1J9pNX1NBLWNOCd3WuD51KfKI,193024
scipy/spatial/_kdtree.py,sha256=0pC_iHnxvA86YJ-7Ew4Nu6ZvxSazaLlQoid62ny6Tmc,34364
scipy/spatial/_plotutils.py,sha256=hoLzA44Oqx9jZu7gFfZ57k1-WfVWkKpXUx7lChRVP7M,7437
scipy/spatial/_procrustes.py,sha256=xfcyARufJ98uzxJJzHwLG_VHXgajhNPZpIHTsniiaXI,4559
scipy/spatial/_qhull.cp312-win_amd64.dll.a,sha256=Mxzbqu77QO8i7oAYqeXBL-3-CwQEg2s4-skFWLl6Opo,1532
scipy/spatial/_qhull.cp312-win_amd64.pyd,sha256=dOBrtbNtzX4tBp6srXbvI8IFfAHdv2M0neQPpCN6xFE,1002496
scipy/spatial/_qhull.pyi,sha256=uKWjHpls4mJiY678JL6MRo8bcMEceAMrwGWAT1-ecBw,6179
scipy/spatial/_spherical_voronoi.py,sha256=9bJXqiSwc47d9WmirIv7X_Iam0wDjPZoMWgV9y5J6wE,13906
scipy/spatial/_voronoi.cp312-win_amd64.dll.a,sha256=hn1Spg93-4kL1sH8hRuMFnpcfkAIevCF_O5O1tiSkSM,1560
scipy/spatial/_voronoi.cp312-win_amd64.pyd,sha256=5kP3FDqx80uBCpEP4Ih9apFzhbfVYUjyW2WBmorAf7A,188928
scipy/spatial/_voronoi.pyi,sha256=Udy1F5LdAWzkFiapiu-VUHav_k55CvKARI5EPvm5QQU,116
scipy/spatial/ckdtree.py,sha256=MHwcJFwNH_asRheaq1FkqzFAqYLvjFyZoF6LVWlC0y8,897
scipy/spatial/distance.py,sha256=GG24an3HEcfEI3ZwxrjzeL9Q0sZNOcVGy46ZdNTPT-M,95886
scipy/spatial/distance.pyi,sha256=SM3gIUzqT6BzX_SZaFYFbFXFkTuwEwHVbhNRUlxOLM0,5485
scipy/spatial/kdtree.py,sha256=wMI4S04wBuzIZXO7nVWdMi-WEXZTacUABER6dEQN0-w,904
scipy/spatial/qhull.py,sha256=AXTCNWWiZxgQKkTG0tNVj60fWY5BCBuvV_1qqa40ZCM,926
scipy/spatial/qhull_src/COPYING.txt,sha256=liRS5zfffHQ6PcJ0QjIHECi4wEUOdnSlUFfDMOoZd-s,1673
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-312.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=LTxfJD5Hzxi0mmMzhzCgp0dUJflrg6O5duj8LVxa9lA,5760
scipy/spatial/tests/data/cdist-X2.txt,sha256=Tb2q8iKbPvUlxO_n1pBfYkWvu1z_LuBcwFXCIRZgBOg,11520
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=sWic54AuDE3IyLk6XOw0BcyCSzr5BkXrpDJc7B00OTQ,15150
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=D6kMC8OK5H2Z6G07l592VtEENnehwy8wuM1rjVq7wXU,50020
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Ysw196_xQ_ApwKiaB7OXZ-gVvhvhFH8wUZAw7EfacTs,178802
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=jtPmZwg5AfpIom-wMjty8_6tI69ePUnJ7eYdFyFq9G4,3042
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=Wf3dVmhan8EEYiKAsrgN5WNMmAmH9v9NjJpO4xRdmPE,178802
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=aMGSV28cjKIV2CRfdR2zZsisln6oxi1tYQbxZ9tuMMo,3042
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=PdVB_30O5CHL9ms8KRB1kFiswp9Sed8CV540WQvNaJc,178802
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=VDgXqx3vUn26r1EZFcSk-rq1-wef2WVFcoBWLG8zvro,3042
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=q5nrvDyyqdltrjBRyT5eFVQIW-y2Igb2JdC8tBAXl7E,178802
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=nLK5yDtcmU-svoYeLPuZZoolZpxmpOaeLswLLR5QoGM,3042
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=2UzMIvz_r7ZT2Twt-jH-pst0Avue4ueGZezk7T2_6q0,50020
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=VWMmzL2jP_vhDzTBhYtZH7-VBKJFpafRROrefDeokWI,178802
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=Kn8vHq17IbWtj1G9XzCFY1EuXFTPnD7gX9FwW2KSm44,3042
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=OqjHwrqGBwmwsfTeN2fI1EFiWSpOA6ZlZYwN121Ys1o,3042
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=nj_JYQ6bfvhlFDKu5OC_zBeOzPKgewkmpxScJEPAzhs,3042
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=wz1O-EHZT2cDwFtow4ZoLWQgS8WY6DPzb_Ik9SXA8Fs,172739
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=rps5AwUK4Up9qCR3wAE1wfwuKE5ftKZTHChPYH0hsfA,2819
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=rfWuQ4RgSfY0ko_jqlSTjQIJYGCHeEbEwjHYpAC9Ivo,178802
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=jKlA87ldGof8ndZLkV9l97-Tsh2uXTIlyZRsMoWr0gA,3042
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=bWYNT7XqpLTGkQBMfnCFJ7bnoxooZlzfI0_O54UOquI,178802
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=bGIB7ygJC8CGHAoVKtwOdPgAqDM5bzffpgxYMTCKgmQ,178802
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=g9-qvSA4qe4S0xPxnTa17zqa4Z-P5S0TV1gAsbr-RAs,3042
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=vpgBfMlrUUH-nYlnLqm_Ms-MIjrgOlNCW-TW8C4Yan0,3042
scipy/spatial/tests/data/random-bool-data.txt,sha256=sydRG9aL6CH9i7-Nt5X2mNyQDsu9OC1fbUD-MRdt5bc,6100
scipy/spatial/tests/data/random-double-data.txt,sha256=OcmsKJSbi_eY65ld50iAatodSzw2NxFfDJIDHx0lOpQ,75100
scipy/spatial/tests/data/random-int-data.txt,sha256=saHYkK0CMRzV78RV18tUGIF4uKQlOqkN4cEWqD4gnFw,10366
scipy/spatial/tests/data/random-uint-data.txt,sha256=aBbEt1tldXIvAvkDMbSr9WddkxXtMTa05Qq8j4s55CE,8811
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=x-c_sDM8alTwdaj7vYvGAQEsjrg6n5LycGrrPR4dlY0,507
scipy/spatial/tests/test__plotutils.py,sha256=LakEz_Dtuw8CExd_0__YE45wJ2Kdjcy1-1MzkZ-nob8,1997
scipy/spatial/tests/test__procrustes.py,sha256=iZypw3jevzOPiHv15UZHwc3sXE3pNlUlCeKTZfoL5vE,5090
scipy/spatial/tests/test_distance.py,sha256=_JMBEvHhZUZdJ_cpfEsxoCBDe9Pn5mR-jL0P_i2A7E8,85513
scipy/spatial/tests/test_hausdorff.py,sha256=7ZZJjse3SoM8wYx_roN7d2nYIJwHAh_QbXD2ZqZxEjE,7286
scipy/spatial/tests/test_kdtree.py,sha256=lA_LjtdBVcYSS_-vMkNP0Yr-_neZMSvs29a_XFbtpGI,49703
scipy/spatial/tests/test_qhull.py,sha256=QDzgXT7wey46rXJkVndJcgUFBl8sUbju00D-fdYi4jg,44897
scipy/spatial/tests/test_slerp.py,sha256=DDZie6nQFvdtM8PAY26gnUluKQALFUPfudI-WQbP1cA,16812
scipy/spatial/tests/test_spherical_voronoi.py,sha256=ZvVei6LbfAFhZn1muYetMOViik6zog-CKlDaHTJjt8I,14716
scipy/spatial/transform/__init__.py,sha256=AR19SJ8oEv3Pt2eXGRiHkglp7wU_rGvRJ8JEIBqs4AI,729
scipy/spatial/transform/__pycache__/__init__.cpython-312.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-312.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-312.pyc,,
scipy/spatial/transform/__pycache__/rotation.cpython-312.pyc,,
scipy/spatial/transform/_rotation.cp312-win_amd64.dll.a,sha256=1LybRG12loCod44kMGCfhu4CjJipgg8ige7e0LZlB6o,1568
scipy/spatial/transform/_rotation.cp312-win_amd64.pyd,sha256=7weeHdBFzCd8bbNros97-vB8LswUnNL0wqbsYhLcbw0,643072
scipy/spatial/transform/_rotation.pyi,sha256=FXvxtBvNk6SBL-Slo19q6tjaicGEQw_MYRrzXXN0Lvk,2697
scipy/spatial/transform/_rotation_groups.py,sha256=XBEazTsMg71VeDaexivk9VYmVDgHPkNji9FrlRypjyc,4562
scipy/spatial/transform/_rotation_spline.py,sha256=bQlcYfmYrF1-__9YFv_Utkr-5yLRwlsXJcS-y6eENDs,14543
scipy/spatial/transform/rotation.py,sha256=JbzMKJ7p-VSz4y1o5QEVZk6Yc6-c_FloPaLDkPpQHYU,905
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-312.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-312.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-312.pyc,,
scipy/spatial/transform/tests/test_rotation.py,sha256=C43Wv3vrpefET0vOmPnEw6QuaP-DkndxOD2GlVefROM,48691
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=mATjBt62JCkoqpEMHU2zXB9HF-h7KFpWMBrSLyxnkTA,5729
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=yMBr91x2Tt2GqMvJVTOMcO2n3xgOwGC80MN7HNXQkdM,5267
scipy/special.pxd,sha256=vZV_tS467FzwvnjYCtD4T8r5NAuPmITjU8ccJioLZ3I,43
scipy/special/__init__.py,sha256=nJkHC8FBR-Jc7fzwNHJ_GwNp37NaYNRSuzLid9fpVLQ,31206
scipy/special/__pycache__/__init__.cpython-312.pyc,,
scipy/special/__pycache__/_add_newdocs.cpython-312.pyc,,
scipy/special/__pycache__/_basic.cpython-312.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-312.pyc,,
scipy/special/__pycache__/_lambertw.cpython-312.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-312.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-312.pyc,,
scipy/special/__pycache__/_orthogonal.cpython-312.pyc,,
scipy/special/__pycache__/_sf_error.cpython-312.pyc,,
scipy/special/__pycache__/_spfun_stats.cpython-312.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-312.pyc,,
scipy/special/__pycache__/_testutils.cpython-312.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-312.pyc,,
scipy/special/__pycache__/basic.cpython-312.pyc,,
scipy/special/__pycache__/orthogonal.cpython-312.pyc,,
scipy/special/__pycache__/sf_error.cpython-312.pyc,,
scipy/special/__pycache__/specfun.cpython-312.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-312.pyc,,
scipy/special/_add_newdocs.py,sha256=MGLCK9gBOMQXd1akl6c4pf1qaN1Xpcfx78hpU8M3TH0,405635
scipy/special/_basic.py,sha256=OqV80LsXVaUtF4NnxdTCByZdk2J9I3hE6cZHDiG9_X4,98806
scipy/special/_comb.cp312-win_amd64.dll.a,sha256=x34UtIgYXgD39HWjJppxHS7qdT8jaMBpNJQ_BLHSc_8,1520
scipy/special/_comb.cp312-win_amd64.pyd,sha256=su06CyrsrJhGheNCoKZXimzGtFg4HrVD_HuXcIC6WrI,35328
scipy/special/_ellip_harm.py,sha256=T2SA2CUqdQbAaI-gGhswXc_NTu1zDfgqqgtbRm_Sssw,5480
scipy/special/_ellip_harm_2.cp312-win_amd64.dll.a,sha256=cUycBqhPA9tTW8P9G22H7aQqe0bU5SW9Surt1hYdVOc,1616
scipy/special/_ellip_harm_2.cp312-win_amd64.pyd,sha256=UiKn1GfM5GNslb7dUL4o179VMOI3Q9vgs-qr2G73U94,84992
scipy/special/_lambertw.py,sha256=-38iqIRZ7l5Ox2xJXsM8U4nyS2hzPixe6M8vS9AG9bY,3950
scipy/special/_logsumexp.py,sha256=atmzxn3ZufgjPG-insiMHtIZktn0SvdZjZIBmCwTzEI,8821
scipy/special/_mptestutils.py,sha256=6GCFaRe7q3knf-tq14DuiJRMVU6smsd_m-fj012hYFo,14963
scipy/special/_orthogonal.py,sha256=WHuEg_8BYTi_70ZOTOL100fVAEoh2Mn_V3bdR-VGmcw,76817
scipy/special/_orthogonal.pyi,sha256=QT6rbr9YFsa2EBE5OSESkG3A1gWr37xLFdxWLNcUVSQ,8663
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/cosine_cdf.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel_data.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-312.pyc,,
scipy/special/_precompute/cosine_cdf.py,sha256=7Fjlx721WrFcUMSUh6Vv40NMiR1QX71963X1tHOd55M,371
scipy/special/_precompute/expn_asy.py,sha256=trfFT7O7EweO7DfQHIHCzkwWNuqm7sTtCo-HjsUQthY,1319
scipy/special/_precompute/gammainc_asy.py,sha256=8XBo6RBUzdxUhH7GrVeVTzsMJgSnGb9NpknFAdHwcBY,2618
scipy/special/_precompute/gammainc_data.py,sha256=w3vsqOEvC6a7s58voRucZdWFXpO3jIfSGrGwVTNEalY,4201
scipy/special/_precompute/lambertw.py,sha256=UsSir6v9vfyYrlu5F3W-Qn3MMW5RzqqftfZAC-kTH8I,2029
scipy/special/_precompute/loggamma.py,sha256=J2jz62nBO-7JEOA7nG4JQQt5BuXSfUPRik2r3pwTr_s,1137
scipy/special/_precompute/struve_convergence.py,sha256=5EAmA618oy2cvlemsiszLDiUM1Q83Ft11azcq3z8Rgs,3584
scipy/special/_precompute/utils.py,sha256=tJpjcNS6WaXzqP7cLac7yFyXfb_pfe3AKvDCa0mwi9A,925
scipy/special/_precompute/wright_bessel.py,sha256=2OcwWu1RpbohBr_CIdn3ODUWeE8ZrFz5j6GnWtDTIlY,13208
scipy/special/_precompute/wright_bessel_data.py,sha256=TIwEyxKLTl2DqbLvppxdVWB1jJu-hL157M3JMjFK6eI,5799
scipy/special/_precompute/wrightomega.py,sha256=hAKXz3Kk9L-5JOBA86U6-yC_tsF8gOz0p8Y1vmWcAzE,996
scipy/special/_precompute/zetac.py,sha256=cZMVkubEXtildiIM9p5ldrV1sEoy240MckKPO3JhM6A,618
scipy/special/_sf_error.py,sha256=hDbymj1eSiPjl828PR-Kidkcd8RiVDpJEB-fGA9ltHM,390
scipy/special/_specfun.cp312-win_amd64.dll.a,sha256=28PiJW8O39DBOBjzoi6YV6NbTDfXbQ4RLKI3IMSAVM4,1560
scipy/special/_specfun.cp312-win_amd64.pyd,sha256=X8GIG4R2SrvkXjDdUgNTv6NGJn8SnuUnYcQjJnZ4CP4,521216
scipy/special/_spfun_stats.py,sha256=uIOOQPBeg-JD_W4ADbybIjlYSjtsqI-4w5YxgbrYfnU,3913
scipy/special/_spherical_bessel.py,sha256=Xgn6yWFTDc4nqvQMFU9hlDmRm5PieOsbHkGvdebsQqw,10566
scipy/special/_test_internal.cp312-win_amd64.dll.a,sha256=ktOCRa2_NuYY3keYw4KpN4zGMJPSpRZxRmLd03hV7n4,1628
scipy/special/_test_internal.cp312-win_amd64.pyd,sha256=l-VaGF_h5T9WdkUY2IAKUJ9gDA2f2m0LvMSu9_bkutM,235008
scipy/special/_test_internal.pyi,sha256=SLu2iMKH7AWdVCbKlF0SpGFEdu2i-opkrFoCTq6zMaY,347
scipy/special/_testutils.py,sha256=KLRUZyetPVp6IcBkg8UyLicOAzXVJ5HqsguqiwTwyZ4,12288
scipy/special/_ufuncs.cp312-win_amd64.dll.a,sha256=JUQ1Z1nalamP6q0nqo0kzRrC4tZTF2iFCAr-GriKt0A,1544
scipy/special/_ufuncs.cp312-win_amd64.pyd,sha256=a9WzREZOFVYaWw82zsrs63X-oG-9W6m_yd06fVRudOQ,1953280
scipy/special/_ufuncs.pyi,sha256=h5L3lEQMocnD6wAQSPPSjVljv1OkwcWLBnvUO4bvyYI,9353
scipy/special/_ufuncs.pyx,sha256=JiTShSjhv8-dvEAUnu2NeAzhPm0_LWIHTN3Q838_xNA,899225
scipy/special/_ufuncs_cxx.cp312-win_amd64.dll.a,sha256=JsGfAVfjOjRIH3GVgXma3r9w0QuKxcP1d5CFN6tnobQ,1592
scipy/special/_ufuncs_cxx.cp312-win_amd64.pyd,sha256=d4eUqQOP3Y-A6IAZLrSNOkCRIXmdrtIhplz9OPIuiYQ,1372160
scipy/special/_ufuncs_cxx.pxd,sha256=3lHMlItVOJ24ZQKpXJGrv6EyqMEji73K7UoYB252nm8,1400
scipy/special/_ufuncs_cxx.pyx,sha256=DKgzgebXbvjtlBkBmg7wzPr16ZAkWaz_uuX6ZyOkfLk,7621
scipy/special/_ufuncs_cxx_defs.h,sha256=2Y0jGOFcbM93dODOZEDQfh6bqq43NF9kUwKNryV1UmA,2052
scipy/special/_ufuncs_defs.h,sha256=Jz3IHaAa7MQlJthsRuK43u5diEZb8hiWHAr4eTq4ZmY,11336
scipy/special/add_newdocs.py,sha256=uBu_FANz5Ljbpj_D6qt6caSBRhNE1ynR_eLOVFn14w8,667
scipy/special/basic.py,sha256=ciDsf1oN-UMMP_EuitkiYoHQLV3BTBJxG3Jr2kom12M,1993
scipy/special/cython_special.cp312-win_amd64.dll.a,sha256=xTFUzPjknMwq9hyPCcl3zJ1uDtf21owy35P29By-fxY,1628
scipy/special/cython_special.cp312-win_amd64.pyd,sha256=dpSin2wuIL9HEfyj1XwdlTe-p6ZbsHQ49Nn28tAJOag,2540544
scipy/special/cython_special.pxd,sha256=2-FQJnq5f5j0mOeiNOZz52Ige_Ya66MNYhijZHYmcGQ,14629
scipy/special/cython_special.pyi,sha256=sC6nE-1ffPYfFKVvopszCin-ilDqBWkDW2O6s0and2M,61
scipy/special/cython_special.pyx,sha256=aZ_UqIoxrn7Tjbu8xwXWMOmd6eB7CjBx49X85GRj7uA,141600
scipy/special/orthogonal.py,sha256=LvwpYXqJl8Ks1s2oHQpQiRDfg20m5I10udE1RfaGQFk,2108
scipy/special/sf_error.py,sha256=DljWhSwCFY0NUbufWQ0VNCVlqZDpxR_qfLRezboMTac,820
scipy/special/specfun.py,sha256=kFR7qPScWwSHb_tT57o9rtapiTxnpN-xp1OqfnHysT8,1110
scipy/special/spfun_stats.py,sha256=faboFB2SOG333mwd3nSnBDQbHI89fa9om8QdvXUfB9s,795
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_cdft_asymptotic.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_cosine_distr.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_dd.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_hyp2f1.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_log_softmax.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_ndtri_exp.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_powm1.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_wright_bessel.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-312.pyc,,
scipy/special/tests/data/boost.npz,sha256=89v_GtzMGGCOiyicvR4rAGsPttpcbyDjq0MH_hKMMIM,1270643
scipy/special/tests/data/gsl.npz,sha256=X7Vf4jA3wye4vNm0C6X9KNosK2_NSzPBxGwR1EYmvag,51433
scipy/special/tests/data/local.npz,sha256=ykTwo3nEwxCbDrGbvE7t-kjbmxQ-IJaoNyuXTdwOEFY,203438
scipy/special/tests/test_basic.py,sha256=Tpz46zOlx_3UjgbEcsba0Qvoum4qP_0T1LMN66QN6aA,162777
scipy/special/tests/test_bdtr.py,sha256=ZPWchkB2hVjJ7MALAN9K4QWI8PeGSxcqduER3_myJRk,3343
scipy/special/tests/test_boxcox.py,sha256=K3BgEIIMADJLcnrzi9Nq7SWErOIiLQP36t6Bz6aoRMk,2778
scipy/special/tests/test_cdflib.py,sha256=oMoFEVG19mEvsrK-NeU7AkD43mHvw-xWbhpIMjkOuxg,13849
scipy/special/tests/test_cdft_asymptotic.py,sha256=uF6yhrAGSPdlP1tdLDoR2-IBlkjFUeEo2hBAr7DTxJY,1478
scipy/special/tests/test_cosine_distr.py,sha256=ENyfDI4OabouefeuzVJQO7UsBE3Eaorv39Z4GQoBc7E,2773
scipy/special/tests/test_cython_special.py,sha256=MbllzO4uka8BECeEDdbXQOHuVIiuHHCW8VGmOXWxcpw,19008
scipy/special/tests/test_data.py,sha256=sKOllugp67XY11adRtpqk4epqKvyieYYZn13BSAFX4o,29098
scipy/special/tests/test_dd.py,sha256=3tsB8dnU22c5CEG5l-NJBxDmra5iQGhvUVKn7POdqvs,2020
scipy/special/tests/test_digamma.py,sha256=WGB-E2XOPFtIZLVidWFiC2gCQbSMpOpOQyO_o2_WLr8,1436
scipy/special/tests/test_ellip_harm.py,sha256=qOVC4b0N6y7ubJNYa4K54W4R5oiHdWghdhy_rO5_gac,9918
scipy/special/tests/test_erfinv.py,sha256=JMvb2hWNQ63uPevk3kAL6KypAtOMJzg8n1G3Om9A15c,3148
scipy/special/tests/test_exponential_integrals.py,sha256=cPxuyehzPSgv_o3Q1Cfb2ON5NNxgBgyAi8dCDILIQe0,3805
scipy/special/tests/test_faddeeva.py,sha256=2BXe8JR2d_QBMvjX-bmHkP0TDzy4JgZhM6XvSlqO1HA,2653
scipy/special/tests/test_gamma.py,sha256=-u9wzNPSPsJTs_DJL6VZAXXIcIqJsxOeQy__MdRTGxY,270
scipy/special/tests/test_gammainc.py,sha256=Rrv_OnC7U6EtXSvrbGQpcPb7sccWVPIgIAzjs6DBIxM,3951
scipy/special/tests/test_hyp2f1.py,sha256=H8b6jTAA0qx4kEJhQjPVlKEKGGL7_1v1fPdNBsXA50M,80727
scipy/special/tests/test_hypergeometric.py,sha256=1FF94kA-5Z1wRPF2Wz8V_G00hU87oLIWXlAuEBnP0gg,5738
scipy/special/tests/test_kolmogorov.py,sha256=wzCFKI0n3hUyPsc8-lybLx18UgEsOq7X5kl7hFkyyco,18819
scipy/special/tests/test_lambertw.py,sha256=yUKLzwXbuRiosc33zN0mqEiuSnxrfO4LOtwnH6mKNl4,4665
scipy/special/tests/test_log_softmax.py,sha256=0VhKfjYbv5-bV_d4tWyrG6OawlRjCUi_O8mRSgW0Mn8,3524
scipy/special/tests/test_loggamma.py,sha256=SdxYiItvixR4NabOKLyBHu-iEP7R52Rew8N8bZugH2s,2062
scipy/special/tests/test_logit.py,sha256=eJfxl4AYJF2VRFebHTw5NS7eF7Q_R82C42x3cou7uwE,5685
scipy/special/tests/test_logsumexp.py,sha256=89z07XS91GhkTVSQIIpRTVjdli9_8cCggHY0a5QenPg,6374
scipy/special/tests/test_mpmath.py,sha256=mUy8Hm6QQPb5RWBwvh0aqc76U-FJedDK9ekgZfD_6bE,77216
scipy/special/tests/test_nan_inputs.py,sha256=0GiYf9pNQ_8SX17hVx34MvEiaSa8CfszqN-Cr_KfDbk,1901
scipy/special/tests/test_ndtr.py,sha256=n_Y8wG9MYVmZOXPgJoiYjPGlUULdiPoDQFsuR6HzPWg,2757
scipy/special/tests/test_ndtri_exp.py,sha256=b4vFFkhdzIDWNgmKOEy5UjRCYtHsdkoaasRi_L9lzIM,3802
scipy/special/tests/test_orthogonal.py,sha256=2T1hWmHYzZjVkTQJdvIGMW4ABSt0eSiFwuAYWOQRAH4,32273
scipy/special/tests/test_orthogonal_eval.py,sha256=OB93dI8G9fukPlpssmzN2n1wPEhj1DQye4TIjMsODCI,9587
scipy/special/tests/test_owens_t.py,sha256=sotiVxRrjoQ4lYjbbBx88nQKHEHhNowfJ7KPdIM-vNQ,1845
scipy/special/tests/test_pcf.py,sha256=VTEqviYLsm0DBnuu1Bpyn6rEocf9sZGSyY12ufwxaYo,688
scipy/special/tests/test_pdtr.py,sha256=YpZQ7ssQ5z0yL2lKss4lDooRlz_3n-aY2q4a8aVXXB8,1332
scipy/special/tests/test_powm1.py,sha256=AG5D4ex4jCS97xN7RlyumVc09OUTuczNxS2IYwt9OAg,2341
scipy/special/tests/test_precompute_expn_asy.py,sha256=DFW5CiFXjHNUkWgZrehKUxPNTd09Cllp1nZ0_FU172g,607
scipy/special/tests/test_precompute_gammainc.py,sha256=sY-xFU6MwT-bzjlXXYqu2_spch0XGJO0sZR6pQHMgPs,4567
scipy/special/tests/test_precompute_utils.py,sha256=EWXMuVz5WDUQISPzh87i02u1im9TcLbDBxyk-BO6iZY,1201
scipy/special/tests/test_round.py,sha256=aySiXp52xcim56HDkpumtSnFYgRTGzZFQ2USLNmcECI,437
scipy/special/tests/test_sf_error.py,sha256=oS-LiXV8HnctZOEtsUC_rl-hGDksMcTL7i0gjOcjmgI,3643
scipy/special/tests/test_sici.py,sha256=REYntiqC0T4Pb-66ESBmjEmIoTJDbjYs786XNwUkzIw,1263
scipy/special/tests/test_spence.py,sha256=r4chXBi_nKK1uSfPse7owNhvcC93aBvcMBZf_8wTlF8,1131
scipy/special/tests/test_spfun_stats.py,sha256=wiPQbTQD1vpgk3M1tFkTm0x5FnPgMGdp-5wO1XVm-7Q,2059
scipy/special/tests/test_sph_harm.py,sha256=jUhoguaBjEo2L6660qg41qaLxOxhbMZkMBsa0e5Afmo,1143
scipy/special/tests/test_spherical_bessel.py,sha256=nTlVlx0IMpKmI9rKUBUJCxIhoXvyLnTdtLiEnds1iiY,14663
scipy/special/tests/test_trig.py,sha256=1_SHYdg-HNGQjEOr9QcDz2PwPWXnnPdZ1QEkuxXB5HQ,2163
scipy/special/tests/test_wright_bessel.py,sha256=t15XtWFFnN9YapbVZyyA88I32j8X14ADbUhZzbMRbLU,4270
scipy/special/tests/test_wrightomega.py,sha256=-2-tzEj7HA21xTo_WtCU5nc6rJDT9Tr0Z9IjA2f39mM,3677
scipy/special/tests/test_zeta.py,sha256=FbhrcRuDXJ_ZrVMRdUvICiaGMp7DM5CeWcOwYSKhXvk,1416
scipy/stats/__init__.py,sha256=3XCz8XBd-MDQO2GZcSqPVZhNV2DxbaGAhW2-qjyL-AE,18742
scipy/stats/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/__pycache__/_axis_nan_policy.cpython-312.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-312.pyc,,
scipy/stats/__pycache__/_binomtest.cpython-312.pyc,,
scipy/stats/__pycache__/_censored_data.cpython-312.pyc,,
scipy/stats/__pycache__/_common.cpython-312.pyc,,
scipy/stats/__pycache__/_constants.cpython-312.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-312.pyc,,
scipy/stats/__pycache__/_covariance.cpython-312.pyc,,
scipy/stats/__pycache__/_crosstab.cpython-312.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-312.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-312.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-312.pyc,,
scipy/stats/__pycache__/_entropy.cpython-312.pyc,,
scipy/stats/__pycache__/_fit.cpython-312.pyc,,
scipy/stats/__pycache__/_generate_pyx.cpython-312.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-312.pyc,,
scipy/stats/__pycache__/_kde.cpython-312.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-312.pyc,,
scipy/stats/__pycache__/_mannwhitneyu.cpython-312.pyc,,
scipy/stats/__pycache__/_morestats.cpython-312.pyc,,
scipy/stats/__pycache__/_mstats_basic.cpython-312.pyc,,
scipy/stats/__pycache__/_mstats_extras.cpython-312.pyc,,
scipy/stats/__pycache__/_multicomp.cpython-312.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-312.pyc,,
scipy/stats/__pycache__/_odds_ratio.cpython-312.pyc,,
scipy/stats/__pycache__/_page_trend_test.cpython-312.pyc,,
scipy/stats/__pycache__/_qmc.cpython-312.pyc,,
scipy/stats/__pycache__/_qmvnt.cpython-312.pyc,,
scipy/stats/__pycache__/_relative_risk.cpython-312.pyc,,
scipy/stats/__pycache__/_resampling.cpython-312.pyc,,
scipy/stats/__pycache__/_result_classes.cpython-312.pyc,,
scipy/stats/__pycache__/_rvs_sampling.cpython-312.pyc,,
scipy/stats/__pycache__/_sensitivity_analysis.cpython-312.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-312.pyc,,
scipy/stats/__pycache__/_stats_py.cpython-312.pyc,,
scipy/stats/__pycache__/_survival.cpython-312.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-312.pyc,,
scipy/stats/__pycache__/_variation.cpython-312.pyc,,
scipy/stats/__pycache__/_warnings_errors.cpython-312.pyc,,
scipy/stats/__pycache__/biasedurn.cpython-312.pyc,,
scipy/stats/__pycache__/contingency.cpython-312.pyc,,
scipy/stats/__pycache__/distributions.cpython-312.pyc,,
scipy/stats/__pycache__/kde.cpython-312.pyc,,
scipy/stats/__pycache__/morestats.cpython-312.pyc,,
scipy/stats/__pycache__/mstats.cpython-312.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-312.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-312.pyc,,
scipy/stats/__pycache__/mvn.cpython-312.pyc,,
scipy/stats/__pycache__/qmc.cpython-312.pyc,,
scipy/stats/__pycache__/sampling.cpython-312.pyc,,
scipy/stats/__pycache__/statlib.cpython-312.pyc,,
scipy/stats/__pycache__/stats.cpython-312.pyc,,
scipy/stats/_axis_nan_policy.py,sha256=KI6lxLyHXfXagJ9-Er_csjc-F4fq8HwrTyvjxWqH5Hc,29014
scipy/stats/_biasedurn.cp312-win_amd64.dll.a,sha256=UeO1qEqDF6qWywquOLa_S8G5bG1IEmtoTLWfH1XWca0,1580
scipy/stats/_biasedurn.cp312-win_amd64.pyd,sha256=t7G4uQiOz5pzoqzE9cp5C_0Rihwnn2XWldHryjcAJg0,357376
scipy/stats/_biasedurn.pxd,sha256=OHa5weTjmvbIdkvmgDnD021p44bVaS5mNvDIx562SLI,1073
scipy/stats/_binned_statistic.py,sha256=JFZ6sjFfVHXRDep3h6aLEitaiW4dJKeaUIN5h8PCYs0,33499
scipy/stats/_binomtest.py,sha256=hr6r2GAaKfXQmhtk2kujbPEUmM_CPvKN5i_18LZLksM,13418
scipy/stats/_boost/__init__.py,sha256=TOjKqfv_jPF6l8By1mdPbQFrV_H-ZMLRCUFZ7mNfL8A,1812
scipy/stats/_boost/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/_boost/beta_ufunc.cp312-win_amd64.dll.a,sha256=Wiiiq-25whFCTcgOxZdJiZQeTYfitDphFFQspUSegdc,1580
scipy/stats/_boost/beta_ufunc.cp312-win_amd64.pyd,sha256=g3KvXNVO0aZec7BYMpJoYRF6_bxMdbx8J0Gy1RaQ1G4,1058304
scipy/stats/_boost/binom_ufunc.cp312-win_amd64.dll.a,sha256=8tGudk-ixojPugBjnw-cd7MTplOh0s9O_EO6z0ezCF0,1592
scipy/stats/_boost/binom_ufunc.cp312-win_amd64.pyd,sha256=Xt-8rlvYdUueNVoD5-U2DZfw-XnsgcX-yZqMJ0bYZ5E,1034240
scipy/stats/_boost/hypergeom_ufunc.cp312-win_amd64.dll.a,sha256=mtWWF0piBC_XeAAphcO2hRtxKRFsMdCxC6zOd3W-qA0,1640
scipy/stats/_boost/hypergeom_ufunc.cp312-win_amd64.pyd,sha256=8N9oHralRewNb-BOFJuy0KHXdUVaHn6D-4iVs3T5s6Y,992768
scipy/stats/_boost/invgauss_ufunc.cp312-win_amd64.dll.a,sha256=MwpMbBsqAAcD22MVqzuxhL_likwOY9AiqlcWO0F_Cyk,1628
scipy/stats/_boost/invgauss_ufunc.cp312-win_amd64.pyd,sha256=LxyCLUNFE971KToO49R8DeLm4dwQUU0wOA-hZIVf6PE,1027072
scipy/stats/_boost/nbinom_ufunc.cp312-win_amd64.dll.a,sha256=1qAAkLXonjutb4PzZ-gFUI3FcZptWdgmAKcT1xxPdWo,1608
scipy/stats/_boost/nbinom_ufunc.cp312-win_amd64.pyd,sha256=QDw0QZZJiLFDCDmuMa2PB8ottPans53CZ3j0YzpG0Ro,1035264
scipy/stats/_boost/ncf_ufunc.cp312-win_amd64.dll.a,sha256=4YVhFpZlPXhszbneXxwdvFPk8Q80sdWDtRCNR94rxms,1568
scipy/stats/_boost/ncf_ufunc.cp312-win_amd64.pyd,sha256=b_j44iWbqN_Fr0rkm46Rzogqt-yEKLMEXFnVxwec7gw,1034240
scipy/stats/_boost/nct_ufunc.cp312-win_amd64.dll.a,sha256=odTcyGufKbM-aUcGuXj3clVLeGV9BJqBl9arNq7Q5S8,1568
scipy/stats/_boost/nct_ufunc.cp312-win_amd64.pyd,sha256=OoNEscLQhM8GQV7mD0_kAjYpwMUYSIwmQuYBrtFQ9AE,1069056
scipy/stats/_boost/ncx2_ufunc.cp312-win_amd64.dll.a,sha256=iqPY4u0j_wZP9SDnvxaV0LR_lWQ0P9tBk9OZ1ADNmgw,1580
scipy/stats/_boost/ncx2_ufunc.cp312-win_amd64.pyd,sha256=Er1xzMvKLxdBeYXYOxyIhshFZfw1W19b5XeaOlMIAWM,1034240
scipy/stats/_boost/skewnorm_ufunc.cp312-win_amd64.dll.a,sha256=a3k4LrVSvWxrlYPNSuzDJZyYQeSHTN9UPfMA8Fy9z-U,1628
scipy/stats/_boost/skewnorm_ufunc.cp312-win_amd64.pyd,sha256=d--v9spLbTplp7dk1iXN1Uw0lMJzLagrzbxB8XaG9-E,238080
scipy/stats/_censored_data.py,sha256=-mOWeG-hGYqCz8sjbELiBeBOE216wS8PsvmD99YNVxA,18765
scipy/stats/_common.py,sha256=PUBtrtKESxYoaVx3tlq8ZYImEYCIuYslFYikUS7uwdU,177
scipy/stats/_constants.py,sha256=AZugmM-2GSPX31Ppxo60H41VzwhlMFuanU8tpXAKFLM,1001
scipy/stats/_continuous_distns.py,sha256=dyhBhM08q9DAxeFdyyZWpArqbn414W0SadAStBnTY40,375845
scipy/stats/_covariance.py,sha256=-kLKVWUj2kE0zE8FUpk2wzL33rjuCC-QzFTAlrKCgV8,23097
scipy/stats/_crosstab.py,sha256=dHftHq4CGFdfo87PDQFbNprSM7-qgfhtIUzUhTfWa8A,7559
scipy/stats/_discrete_distns.py,sha256=-hbtUyCTebpkZaU7Lm-KZzy-rwnXLbbazKgfMDEbmas,56430
scipy/stats/_distn_infrastructure.py,sha256=crdBz3buHPOuDFhi3tPgsj79pUYcxgLl7oosKikBCHE,149365
scipy/stats/_distr_params.py,sha256=UzTe91F3C8ixG-oQ50yO5pLkS9Zs8HIv9UaE2FelXtM,8860
scipy/stats/_entropy.py,sha256=Es9QotOpuwIYrkHpzel-PUG0kzg4XmSv2bUsLCXMCKU,14676
scipy/stats/_fit.py,sha256=v9_OnfoLJ_nXenAGItn_WjXQlrKEIz928H1zvBNZDdA,59772
scipy/stats/_generate_pyx.py,sha256=Fg-lkO4VjlmR_tWiyipUboSaFWxW6eqJ7PYZRuwYNaA,1348
scipy/stats/_hypotests.py,sha256=wfXmc3PssgwkrM0wScFfB7HU24A5wAhJudFwZ5DLM_0,80659
scipy/stats/_kde.py,sha256=ZDUBosvMnPzQQr1LY0BaLGkCQThZSK5ljljxH-aS3PU,25714
scipy/stats/_ksstats.py,sha256=RuzkXm3pY8ybimBFzPTBEhH2pcHk14useXulZJZ-Gn0,20700
scipy/stats/_levy_stable/__init__.py,sha256=ph396W5554wzYfsMBH1A0eBRv7dxBhUqj4Fbe4G3E80,45049
scipy/stats/_levy_stable/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/_levy_stable/levyst.cp312-win_amd64.dll.a,sha256=noHYGhrIALDa3dKyv1jQ7eRpp3uX78JvLKRGnXWgdX0,1532
scipy/stats/_levy_stable/levyst.cp312-win_amd64.pyd,sha256=K6p59WKcw45U234sMgggb9Kxr0rmf8pL6fL9aIToN5g,36352
scipy/stats/_mannwhitneyu.py,sha256=KnkZrhy_YvpVN1CmzhW3PtN9IuIyuZ0u_6NMiCKM_hk,19460
scipy/stats/_morestats.py,sha256=d3Azn0MNvkicPI4-dHJTAlDyNagqyMnQXGNGubCrRV8,194826
scipy/stats/_mstats_basic.py,sha256=IVboaJPrJkAX6IKE0imzTrxKT1szumN1r61YmzkDclQ,121415
scipy/stats/_mstats_extras.py,sha256=JyKLfV6-cGeLglJkuOEOzN41Zc_6Nchgs9Z5cRzXqQU,16110
scipy/stats/_multicomp.py,sha256=a0QNETPUdVz-muYize7NnHIB50LpMGkXIfNiBbyBjhM,17741
scipy/stats/_multivariate.py,sha256=D3_V4h3aJAYf6oNOS_fhVlmNTqOSz-Osp_Yb3G_Qh2U,241401
scipy/stats/_mvn.cp312-win_amd64.dll.a,sha256=4XdSTox9b5f59iJOPqqGvS8Rdo4KmPOWEJ14hL23ids,1512
scipy/stats/_mvn.cp312-win_amd64.pyd,sha256=tBrDKGPWBc__21FPK5kXopOMtSwCCA-OzmbmkqQyCjI,105984
scipy/stats/_odds_ratio.py,sha256=UQ3o-JrX-p6bXU1jV8xTtP1wixApqbakPw6kmxrU7Hs,18338
scipy/stats/_page_trend_test.py,sha256=t2OeHwjvZa83X57FHZEwdM5putSa12v5LV2WoAbphNw,19478
scipy/stats/_qmc.py,sha256=zfJTIVzolIS8Z1DwvL9wmDuXRtVtQbX5TE09jLhUsG8,96953
scipy/stats/_qmc_cy.cp312-win_amd64.dll.a,sha256=Q1HkgvKGdZfgit7uOwIHkzIuQBpZdSvvrWuEh2zEJE4,1544
scipy/stats/_qmc_cy.cp312-win_amd64.pyd,sha256=ItLyWn6MqqVkf6OmHxnQNpsm7IqKcVoulPv-G_ut0y8,377344
scipy/stats/_qmc_cy.pyi,sha256=l2173UiDribq_G9S47QozpcKo8nO9D3rojwKLEd8m8k,1188
scipy/stats/_qmvnt.py,sha256=nlJZeatM_ZrfvGrZ5NKGPc4pP-g7TbdMj0aDdvVY6Z8,19300
scipy/stats/_rcont/__init__.py,sha256=xtM2CGKxJ2uUfF6Pfyoi_0em8i5pa6njGRaaGSrvFkA,88
scipy/stats/_rcont/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/_rcont/rcont.cp312-win_amd64.dll.a,sha256=ga6cuI6wD8DYj-HZwBefqS4v3HjaMihGUpck7n0FLc4,1520
scipy/stats/_rcont/rcont.cp312-win_amd64.pyd,sha256=U3a6eVmSxHr84MZaT7jQ7tKsLzT1JhkHpD-e9U4fafM,246272
scipy/stats/_relative_risk.py,sha256=txlXt8-p2wLydMIrKEOwdAt5giral_X51T2itd7VDDw,9834
scipy/stats/_resampling.py,sha256=t1DBMGa3gjI7B6fUpbwP4Bgpk3NK9o6_JoAaSN4nn0A,81891
scipy/stats/_result_classes.py,sha256=904WSGrKwzuWHdd3MaS-HUwTJzLm8iF5aNzh0wZq-TM,1125
scipy/stats/_rvs_sampling.py,sha256=8poUe5TLEwfL55G3s9oya7frqtsFJaKsVLP8WYuqV0c,7374
scipy/stats/_sensitivity_analysis.py,sha256=eREGpGMsND6Q_MyupfC5gvA3_8Xj4GdaVdn-NF8ye4I,25465
scipy/stats/_sobol.cp312-win_amd64.dll.a,sha256=iH2d7p_4IUKApAEInXu8qjGFaCs-RqfWMAHQBnWxL-8,1532
scipy/stats/_sobol.cp312-win_amd64.pyd,sha256=45N--pAKg3qz-MdHdgMeNSQ0WBgCPeEJvd8S2N7FZ2Q,293888
scipy/stats/_sobol.pyi,sha256=tskim056aVi6WIymUMEMdWHaLk8QMqWa2kCAbF2ZTPo,1025
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_statlib.cp312-win_amd64.dll.a,sha256=3-aWsIzeGdyLXmhooQr4JiNnFFFpgAP0TlVYoCB0eCw,1560
scipy/stats/_statlib.cp312-win_amd64.pyd,sha256=FugVNptyZI-mWaDzooIlu6g-JmDfgVhJKIg6nFQf_aA,79360
scipy/stats/_stats.cp312-win_amd64.dll.a,sha256=nMNjhL7X3z_0oBISMDAccn93bj1beAW8-nc2gt75gwk,1532
scipy/stats/_stats.cp312-win_amd64.pyd,sha256=xs6KL8n_3lMMum0yHTWuj_StbRxG3Un7CaggUWtVAJw,606720
scipy/stats/_stats.pxd,sha256=iR5uHEVtvhVTh99viNHee7PBYW-qF2mlo0zkhO8TGOw,717
scipy/stats/_stats_mstats_common.py,sha256=4GGZZLYO3iRLFfv8lt9YuLl8OlGDoOxOsp3mrXS31_k,19150
scipy/stats/_stats_py.py,sha256=Fn6nlU5Gp_gn6OVxQfv4AHv0qXQFHJEJt7OoVc9ttUI,404436
scipy/stats/_stats_pythran.cp312-win_amd64.dll.a,sha256=V7GCwVg9Hb7VwW06GM7-SWnwiFM_-T8h0CERzdEQcus,1628
scipy/stats/_stats_pythran.cp312-win_amd64.pyd,sha256=4xqZdmaifEdkuTDWk1bJIBlFJF0jWwCKve7wXrhWOEo,1064448
scipy/stats/_survival.py,sha256=rGGjGVDrGwOyszULwIShmVtIGr7hwxlzUJwCTxU5TdY,26652
scipy/stats/_tukeylambda_stats.py,sha256=RtXsm72WCNJ8kQSYkL6x5XEsRopLVkY_wUSS_32NtmY,7070
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/_unuran/unuran.pxd,sha256=5X4UKcLgRHfhiWNY0mLjreJMZL3xUsDyj654VexGo4g,44666
scipy/stats/_unuran/unuran_wrapper.cp312-win_amd64.dll.a,sha256=isWdoHm_Ezc0VKXOBwmvB-etQ-8IQ6r_tfjeUsuZEA0,1628
scipy/stats/_unuran/unuran_wrapper.cp312-win_amd64.pyd,sha256=t-PDA9cFu55v-dsoMRUPYO0BxtZ4g28W3CBC5wA31Do,1410560
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=G74Y6nZt7KZ0Q5JPxgL1LenzmBKxjs2L5yKpgIDd864,5723
scipy/stats/_variation.py,sha256=BLv5x6qN7-feZDx4XEiyfiVx-yvDVUKj3gYS8IRgglg,8551
scipy/stats/_warnings_errors.py,sha256=f_Hxg3GzZJW2U1B608HFABSkV2w9Xjv1qtsND2AuGKA,1234
scipy/stats/biasedurn.py,sha256=bXHhZqM472v0-lqj0UMBof-qtjruOLpstdFIAPcBOEM,719
scipy/stats/contingency.py,sha256=h5LBs00rbertVp6Hk9_ASfLYmWpMokZTpZ6svox6QQE,16751
scipy/stats/distributions.py,sha256=ToRhksTTk5urpI-AVqAor0EXdWDcsn8alJOeY3iFnPI,841
scipy/stats/kde.py,sha256=5EhrHL0zZ7FLyFF4VBmYCc1M-wa7VNfD87xVAN_gzEs,954
scipy/stats/morestats.py,sha256=jqgjNz70dAjfgp9tc7LJryQoEBz040fvTo0ldpUHflc,1662
scipy/stats/mstats.py,sha256=JdqfzHXLEmI5xiiBcbM-Wdb5Q_yH3njqruTRVO_2dL0,2397
scipy/stats/mstats_basic.py,sha256=1fbmNeUjlO3t6peD5zcTejM7tFNB8gP2-u0dnOi_SGU,2181
scipy/stats/mstats_extras.py,sha256=KQV_AcD1IysR_k72bZLum4oE1Jq2voSEuSY4huHqJHc,1035
scipy/stats/mvn.py,sha256=Yw91PcX47SDmlwSsDV-SoibWYT6RwbjfX00F5C2hZVQ,815
scipy/stats/qmc.py,sha256=fa_YRd0VZnuNo_esB9scs0BV020m8Jt5aHbUqEiX-Ds,11858
scipy/stats/sampling.py,sha256=sOll8H8kHb5LW8lz1gJffTxfyv8mjkeTh8zseJsuXoc,1247
scipy/stats/statlib.py,sha256=R2uxcmTgcaJ9eK733dsvQe19a-MiJ0UXH_hy8BjR0CM,806
scipy/stats/stats.py,sha256=zlShnE60hEjicnaj3O_3Lj3sEqNggWiVauH8MSRMWT8,2746
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_axis_nan_policy.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_boost_ufuncs.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_censored_data.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_continuous_fit_censored.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_crosstab.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_entropy.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_multicomp.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_odds_ratio.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_qmc.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_relative_risk.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_resampling.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_sampling.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_sensitivity_analysis.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_survival.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_variation.cpython-312.pyc,,
scipy/stats/tests/common_tests.py,sha256=XPCd5vMlM8qZ8vJMRQHCWhqa9iYR_1vrZtrIbGQnIDE,12618
scipy/stats/tests/data/__pycache__/_mvt.cpython-312.pyc,,
scipy/stats/tests/data/__pycache__/fisher_exact_results_from_r.cpython-312.pyc,,
scipy/stats/tests/data/_mvt.py,sha256=uB4WvzL5ZYcF7e_TmCXvfV1dnoqalifwGXEpiHlPxPs,7092
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=AqKIctwYj-6FUc6XhwZIfrPCEw073oMKhSWrDhuz00I,27956
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=WhCDyQYtOWbyRL_6N2_JsDMkSr-GoNDdAWrznNcfuKc,3171
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=Wk8C1_JW4Yww3K0bgeAmC7gQtawRP2EH4G3uFo0HZj4,2032
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=rSjKwNT7ef_qJSK1U0kIO96hDxKm-MRs-6vMzbTWuJY,6304
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=cI4y6vNnaavnUz59zRttxnCRtg35ON03xPDmVmDvews,48430
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=4YQE0GKLeGcFgLIwo6O2zZl9lZ0A-Soog6TalCsZN3o,469635
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=EwjrFYHyjObJ3kypUg_Mcp-RbDTx-d7hD7bmWB5bTOc,7064
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=exMz5uVs7XQUUJbfd0z7H2MFITyyj5qCIsQYyoXQwpY,55668
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=DA8Jot8F8unbUySmqu7YVkdlHodOn1w4krjyn8SiAPc,541674
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=_ZXFEKqLPOf2fd0yhGzOrX2auuRBeQM2xvsPywrjVY8,7630
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=h9043__rZdOdvj6LpzvRZkT8OWwarQP8ApUiXzumrvk,61097
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=9GiMWg1WkPyG-ifgxaJl7vCyRkHnSvJ6py2WFQhN0T8,595702
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=WraQbGipxLLrFmHAzX0qCfWkaabU4FtraR8ZD197lE8,2688
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=ZIARw6MmSmQkPpmLKGurnF_WxcZqQxUJmgoVsooTJIU,30737
scipy/stats/tests/test_axis_nan_policy.py,sha256=bpbgu-VLjqh2AlqfATZntDWikvozs7WBtLOdQOroeTg,48748
scipy/stats/tests/test_binned_statistic.py,sha256=Zx8gdkQGNDoF6gUXLbi3PJdXsN-4ZmVrB_4HnCcYYYY,19382
scipy/stats/tests/test_boost_ufuncs.py,sha256=UsSWzKUjZbVQYorXWd_DbVeP49M6ykhGoq9uS4Jksmw,1871
scipy/stats/tests/test_censored_data.py,sha256=LtTBWL6HY-0m7HzbZkYDLO1NH3Z0h_D7I2XZ27CwuWY,7087
scipy/stats/tests/test_contingency.py,sha256=IXGuId-yDBKkt3aYl-Qj16Ojl_hFKQ0NEkLjfFCzlZ8,7947
scipy/stats/tests/test_continuous_basic.py,sha256=DAgwQ_fs8CPEzlPOYtdBdfbJEOMQ0trFLdtZ-VRA0Y4,42151
scipy/stats/tests/test_continuous_fit_censored.py,sha256=T5BroUnb0_5K8mSClXZuHdl4s6qvw4ys-rEZoGSVnYg,24869
scipy/stats/tests/test_crosstab.py,sha256=8NEB6tQ-kVkPYhGvW_nH0WQbWzTruHGWzlrZdTyXPcc,3997
scipy/stats/tests/test_discrete_basic.py,sha256=mxwI6p68F0_JlkQBnoprF25aqxnc1QdweMaPo91fnKk,20327
scipy/stats/tests/test_discrete_distns.py,sha256=odieHJCwnxtsJNiGVIQMpfKOTWp17JGdcOZLomkwY4A,21039
scipy/stats/tests/test_distributions.py,sha256=eNLc7VbVI8zX4Bo8Fxts0YQ2sxO9cGx08aQEOi3uWMw,366101
scipy/stats/tests/test_entropy.py,sha256=Ay5NsZSGzAGRea2ZTyxtFfSijUsnQvWwlP-oHL74vP0,11563
scipy/stats/tests/test_fit.py,sha256=xvEpWJYH9JXXA3L5mVVw78Gimbvh8F8OFV_Xf0OSp1E,43928
scipy/stats/tests/test_hypotests.py,sha256=iMufIlNMcZbc6w3TqVLsjevlyZfJBMAtgllj-cxzUMQ,75838
scipy/stats/tests/test_kdeoth.py,sha256=pZQ-zNJCyK_Ix3WHV5MCoXJc2T11RGYyUqr2j3zRVzs,20988
scipy/stats/tests/test_morestats.py,sha256=5Fpm5eNgRPDN1cw_NuHib49aMKJjA3lqGyToWmCxL1U,122239
scipy/stats/tests/test_mstats_basic.py,sha256=73C4z0K9Cv2CtiTHNAAG7IwGVxMlHm4EzRbm50ltCoQ,87072
scipy/stats/tests/test_mstats_extras.py,sha256=ae8Qn_-iLAI70UaMDbl_r78eeX2xVShuNrRXa81jMxA,7469
scipy/stats/tests/test_multicomp.py,sha256=zabamhTMhUvYbxWS7vHQEPXuFJv6FAg5czBhVVYbchs,18230
scipy/stats/tests/test_multivariate.py,sha256=g-wvnj_75nHuouCklWInUB4Mu2bQOoiuuuyA4PwsCfo,147264
scipy/stats/tests/test_odds_ratio.py,sha256=J5A_AiTlmnMM4ccKOl2cNrv9oBBRFyI5krfJB1F0GMo,6852
scipy/stats/tests/test_qmc.py,sha256=JOGQ9HpVMEIrQVyRCNtefJhuBQ-J_2HAUkh5JLHrD6U,53729
scipy/stats/tests/test_rank.py,sha256=LyDYHiuBpYiFeOITlk2iRsp7nTWJfL4Ola7culVfaMA,11647
scipy/stats/tests/test_relative_risk.py,sha256=_JizDcNuNxqEQ5j9fNqbvhu0Xxa8YkDUlBivVTsykhY,3741
scipy/stats/tests/test_resampling.py,sha256=CkgFq_4oLKqNbZ8lCBmiNQsgOAbzJgvLZBX0669PEfc,72375
scipy/stats/tests/test_sampling.py,sha256=pxfFP6MCxNEHMAf1PBmNDHleBkOpyCq_GnupEp22wQo,52475
scipy/stats/tests/test_sensitivity_analysis.py,sha256=BYqPE4RC5ciZoYnzw28dgNwBxDz9aMHxfzNPQH7dmz8,10462
scipy/stats/tests/test_stats.py,sha256=Nc0qpE6_Z-5Yle_U450ZoIOnhV13_-jjvrfLkhlGBOw,352508
scipy/stats/tests/test_survival.py,sha256=C0oQxr1N-UqBXZVwbY6mB9JhvBJbwP5mJt_nDy_MkpU,22458
scipy/stats/tests/test_tukeylambda_stats.py,sha256=AmqMknbKki17oYdfP5H_IaP6WyuvWRXNyUrWflzGLAE,3316
scipy/stats/tests/test_variation.py,sha256=enueq0viYlJAV_YBD9DPlmGeHQ27Jngdp2WapAbkpiE,6403
scipy/version.py,sha256=aPB4tjJx2nhdrVVWB2mvT3NFtbb01IysVIhx8K4pepc,276
