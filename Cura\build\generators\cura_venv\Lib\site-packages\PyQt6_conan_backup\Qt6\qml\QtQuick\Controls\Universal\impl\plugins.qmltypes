import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qquickuniversalbusyindicator_p.h"
        name: "QQuickUniversalBusyIndicator"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.Universal.impl/BusyIndicatorImpl 2.0",
            "QtQuick.Controls.Universal.impl/BusyIndicatorImpl 2.1",
            "QtQuick.Controls.Universal.impl/BusyIndicatorImpl 2.4",
            "QtQuick.Controls.Universal.impl/BusyIndicatorImpl 2.7",
            "QtQuick.Controls.Universal.impl/BusyIndicatorImpl 2.11",
            "QtQuick.Controls.Universal.impl/BusyIndicatorImpl 6.0",
            "QtQuick.Controls.Universal.impl/BusyIndicatorImpl 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property { name: "count"; type: "int"; read: "count"; write: "setCount"; index: 0; isFinal: true }
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 1; isFinal: true }
    }
    Component {
        file: "qquickuniversalfocusrectangle_p.h"
        name: "QQuickUniversalFocusRectangle"
        accessSemantics: "reference"
        prototype: "QQuickPaintedItem"
        exports: [
            "QtQuick.Controls.Universal.impl/FocusRectangle 2.0",
            "QtQuick.Controls.Universal.impl/FocusRectangle 2.1",
            "QtQuick.Controls.Universal.impl/FocusRectangle 2.4",
            "QtQuick.Controls.Universal.impl/FocusRectangle 2.7",
            "QtQuick.Controls.Universal.impl/FocusRectangle 2.11",
            "QtQuick.Controls.Universal.impl/FocusRectangle 6.0",
            "QtQuick.Controls.Universal.impl/FocusRectangle 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
    }
    Component {
        file: "qquickuniversalprogressbar_p.h"
        name: "QQuickUniversalProgressBar"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.Universal.impl/ProgressBarImpl 2.0",
            "QtQuick.Controls.Universal.impl/ProgressBarImpl 2.1",
            "QtQuick.Controls.Universal.impl/ProgressBarImpl 2.4",
            "QtQuick.Controls.Universal.impl/ProgressBarImpl 2.7",
            "QtQuick.Controls.Universal.impl/ProgressBarImpl 2.11",
            "QtQuick.Controls.Universal.impl/ProgressBarImpl 6.0",
            "QtQuick.Controls.Universal.impl/ProgressBarImpl 6.3"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539]
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 0; isFinal: true }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            write: "setProgress"
            index: 1
            isFinal: true
        }
        Property {
            name: "indeterminate"
            type: "bool"
            read: "isIndeterminate"
            write: "setIndeterminate"
            index: 2
            isFinal: true
        }
    }
}
