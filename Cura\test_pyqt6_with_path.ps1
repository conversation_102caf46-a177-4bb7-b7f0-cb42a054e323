# 设置 Conan 环境并添加 PyQt6 bin 目录到 PATH
Write-Host "Setting up Conan environment..." -ForegroundColor Green
& .\build\generators\virtual_python_env.bat

Write-Host "Adding PyQt6 bin directory to PATH..." -ForegroundColor Green
$env:PATH = ".\build\generators\cura_venv\Lib\site-packages\PyQt6\Qt6\bin;" + $env:PATH

Write-Host "Testing PyQt6..." -ForegroundColor Green
python test_pyqt6.py

Write-Host "`nTest completed." -ForegroundColor Green
