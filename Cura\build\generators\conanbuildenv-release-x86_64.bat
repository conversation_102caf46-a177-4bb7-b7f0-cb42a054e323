@echo off
chcp 65001 > nul
setlocal
echo @echo off > "%~dp0/deactivate_conanbuildenv-release-x86_64.bat"
echo echo Restoring environment >> "%~dp0/deactivate_conanbuildenv-release-x86_64.bat"
for %%v in (ACLOCAL_PATH AUTOPOINT PATH PYTHON PYTHON_ROOT) do (
    set foundenvvar=
    for /f "delims== tokens=1,2" %%a in ('set') do (
        if /I "%%a" == "%%v" (
            echo set "%%a=%%b">> "%~dp0/deactivate_conanbuildenv-release-x86_64.bat"
            set foundenvvar=1
        )
    )
    if not defined foundenvvar (
        echo set %%v=>> "%~dp0/deactivate_conanbuildenv-release-x86_64.bat"
    )
)
endlocal


set "ACLOCAL_PATH=%ACLOCAL_PATH%;C:\Users\<USER>\.conan2\p\gette9ae7dd5027b5f\p\res\aclocal"
set "AUTOPOINT=C:\Users\<USER>\.conan2\p\gette9ae7dd5027b5f\p\bin\autopoint"
set "PATH=C:\Users\<USER>\.conan2\p\gette9ae7dd5027b5f\p\bin;%PATH%;C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p\bin"
set "PYTHON=%PYTHON%;C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p\bin\python.exe"
set "PYTHON_ROOT=%PYTHON_ROOT%;C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p"