<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="091843f3-9911-4a7a-85fa-584f5544aa31" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-dark-colorblind/theme.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-dark-colorblind/theme.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-dark/theme.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-dark/theme.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light-colorblind/theme.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light-colorblind/theme.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/cura-light/theme.json" beforeDir="false" afterPath="$PROJECT_DIR$/resources/themes/cura-light/theme.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/themes/daily_test_colors.json" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "customColor": "",
  "associatedIndex": 1
}]]></component>
  <component name="ProjectId" id="2yrIgbskB59vIj9YNDUoMqtO4tu" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.cura_app.executor": "Run",
    "Python.cura_external_engine.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "C:/Mac/Home/Desktop/CuraProject/Cura",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Python.cura_app">
    <list>
      <item itemvalue="Python.cura_app" />
      <item itemvalue="Python 测试.pytest in TestBuildVolume.py" />
      <item itemvalue="Python 测试.pytest in TestContainerManager.py" />
      <item itemvalue="Python 测试.pytest in TestConvexHullDecorator.py" />
      <item itemvalue="Python 测试.pytest in TestCuraContainerRegistry.py" />
      <item itemvalue="Python 测试.pytest in TestCuraSceneNode.py" />
      <item itemvalue="Python 测试.pytest in TestCuraStackBuilder.py" />
      <item itemvalue="Python 测试.pytest in TestDefinitionContainer.py" />
      <item itemvalue="Python 测试.pytest in TestExtruderStack.py" />
      <item itemvalue="Python 测试.pytest in TestGCodeListDecorator.py" />
      <item itemvalue="Python 测试.pytest in TestGlobalStack.py" />
      <item itemvalue="Python 测试.pytest in TestHitChecker.py" />
      <item itemvalue="Python 测试.pytest in TestIntentManager.py" />
      <item itemvalue="Python 测试.pytest in TestLayer.py" />
      <item itemvalue="Python 测试.pytest in TestMachineAction.py" />
      <item itemvalue="Python 测试.pytest in TestMachineManager.py" />
      <item itemvalue="Python 测试.pytest in TestOAuth2.py" />
      <item itemvalue="Python 测试.pytest in TestObjectsModel.py" />
      <item itemvalue="Python 测试.pytest in TestPrintInformation.py" />
      <item itemvalue="Python 测试.pytest in TestPrintOrderManager.py" />
      <item itemvalue="Python 测试.pytest in TestProfileRequirements.py" />
      <item itemvalue="Python 测试.pytest in TestProfiles.py" />
      <item itemvalue="Python 测试.pytest in tests" />
      <item itemvalue="Python 测试.pytest in TestSettingInheritanceManager.py" />
      <item itemvalue="Python 测试.pytest in TestSettingOverrideDecorator.py" />
      <item itemvalue="Python 测试.pytest in TestSettingVisibilityPresets.py" />
      <item itemvalue="Python 测试.pytest in TestStartEndGCode.py" />
      <item itemvalue="Python 测试.pytest in TestThemes.py" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-PY-251.26094.141" />
        <option value="bundled-python-sdk-9f8e2b94138c-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="091843f3-9911-4a7a-85fa-584f5544aa31" name="更改" comment="" />
      <created>1750586378374</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750586378374</updated>
      <workItem from="1750586379496" duration="2321000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/Cura$cura_external_engine.coverage" NAME="cura_app 覆盖结果" MODIFIED="1750590962917" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>